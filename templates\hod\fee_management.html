{% extends "base.html" %}
{% block title %}Fee Management | College ERP{% endblock %}
{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}
{% block navbar %}{% include "partials/navbar.html" %}{% endblock %}
{% block sidebar %}{% include "partials/sidebar_hod.html" %}{% endblock %}

{% block content %}
  <section class="content">
    <div class="container-fluid">
<!-- Page Header -->
{% with page_title="Fee Management" icon_class="fas fa-money-bill-wave" show_actions=False %}
  {% include "partials/page_header.html" %}
{% endwith %}

<!-- Filters -->
<div class="row">
  <div class="col-md-12">
    <div class="card card-primary card-outline shadow-sm">
<div class="card-header">
  <h3 class="card-title font-weight-bold">Filter Fee Records</h3>
</div>
<form method="get" id="feeFilterForm" class="needs-validation" novalidate>
  <div class="card-body">
    <div class="row">
      <div class="col-md-2">
        <div class="form-group">
          <label for="branch">Branch</label>
          <select name="branch" id="branch" class="form-control mb-2">
            <option value="">-- All Branches --</option>
            {% for branch in hod_branches %}
              <option value="{{ branch.id }}" {% if branch.id|stringformat:"s" == selected_branch_id %}selected{% endif %}>
                {{ branch.name }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="semester">Semester</label>
          <select name="semester" id="semester" class="form-control mb-2">
            <option value="">-- All Semesters --</option>
            {% for semester in semesters %}
              <option value="{{ semester.id }}" {% if semester.id|stringformat:"s" == selected_semester_id %}selected{% endif %}>
                Semester {{ semester.number }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="section">Section</label>
          <select name="section" id="section" class="form-control mb-2">
            <option value="">-- All Sections --</option>
            {% for section in sections %}
              <option value="{{ section.id }}" {% if section.id|stringformat:"s" == selected_section_id %}selected{% endif %}>
                {{ section.name }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="year">Academic Year</label>
          <select name="year" id="year" class="form-control mb-2">
            <option value="">-- All Years --</option>
            {% for year in years %}
              <option value="{{ year.id }}" {% if year.id|stringformat:"s" == selected_year_id %}selected{% endif %}>
                {{ year.number }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="status">Payment Status</label>
          <select name="status" id="status" class="form-control mb-2">
            <option value="">-- All Status --</option>
            {% for status_code, status_display in status_choices %}
              <option value="{{ status_code }}" {% if status_code == selected_status %}selected{% endif %}>
                {{ status_display }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="payment_type">Payment Type</label>
          <select name="payment_type" id="payment_type" class="form-control mb-2">
            <option value="">-- All Types --</option>
            {% for type_code, type_display in payment_type_choices %}
              <option value="{{ type_code }}" {% if type_code == selected_payment_type %}selected{% endif %}>
                {{ type_display }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4">
        <div class="form-group">
          <label for="search">Search</label>
          <input type="text" name="search" id="search" class="form-control mb-2"
                 placeholder="Name, Email, Roll No, Remarks..." value="{{ search_query }}">
        </div>
      </div>
      <div class="col-md-8">
        <div class="form-group">
          <label>&nbsp;</label><br>
          <button type="submit" class="btn btn-primary" id="filterFeeBtn">
            <i class="fas fa-search mr-1"></i> Filter Records
          </button>
          <button type="button" class="btn btn-secondary ml-2" onclick="resetForm()">
            <i class="fas fa-undo mr-1"></i> Reset
          </button>
          <button type="button" class="btn btn-info ml-2" onclick="showAllRecords()">
            <i class="fas fa-list mr-1"></i> Show All Records
          </button>
        </div>
      </div>
    </div>
  </div>
</form>
    </div>
  </div>
</div>

<!-- Fee Records -->
{% if fee_records %}
<div class="row">
  <div class="col-md-12">
    <div class="card card-success card-outline shadow-sm">
<div class="card-header d-flex align-items-center">
  <h3 class="card-title font-weight-bold">
    Fee Records
    {% if selected_branch %}({{ selected_branch.name }}{% endif %}
    {% if selected_semester %} - Semester {{ selected_semester.number }}{% endif %}
    {% if selected_section %} - {{ selected_section.name }}{% endif %}
    {% if selected_year %} - {{ selected_year.number }}{% endif %}
    {% if selected_branch %}){% endif %}
  </h3>
  {% if total_records %}
  <div class="card-tools">
    <span class="badge badge-success mr-2">{{ total_records }} Record{{ total_records|pluralize }}</span>
    {% if status_summary %}
      {% for status, count in status_summary.items %}
        <span class="badge badge-{% if status == 'confirmed' %}success{% elif status == 'partial' %}warning{% else %}danger{% endif %} mr-1">
          {{ count }} {{ status|title }}
        </span>
      {% endfor %}
    {% endif %}
  </div>
  {% endif %}
</div>
<div class="card-body">
  {% if total_amount_sum %}
  <div class="row mb-3">
    <div class="col-md-3">
      <div class="info-box bg-info">
        <span class="info-box-icon"><i class="fas fa-rupee-sign"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Total Amount</span>
          <span class="info-box-number">₹{{ total_amount_sum|floatformat:2 }}</span>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="info-box bg-success">
        <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Paid Amount</span>
          <span class="info-box-number">₹{{ paid_amount_sum|floatformat:2 }}</span>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="info-box bg-warning">
        <span class="info-box-icon"><i class="fas fa-clock"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Remaining</span>
          <span class="info-box-number">₹{{ remaining_amount_sum|floatformat:2 }}</span>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="info-box bg-primary">
        <span class="info-box-icon"><i class="fas fa-percentage"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Collection %</span>
          <span class="info-box-number">
            {% if total_amount_sum > 0 %}
              {{ collection_percentage|floatformat:1 }}%
            {% else %}
              0%
            {% endif %}
          </span>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <div class="table-responsive">
    <table id="feeRecordsTable" class="table table-bordered table-hover table-striped mb-0">
      <thead class="thead-dark">
        <tr>
          <th>Student</th>
          <th>Roll Number</th>
          <th>Branch</th>
          <th>Semester</th>
          <th>Payment Type</th>
          <th>Total Amount</th>
          <th>Paid Amount</th>
          <th>Remaining</th>
          <th>Status</th>
          <th>Payment Date</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for record in fee_records %}
        <tr>
          <td>{{ record.student.user.get_full_name|default:"-" }}</td>
          <td>{{ record.student.roll_number|default:"-" }}</td>
          <td>{{ record.student.branch.name|default:"-" }}</td>
          <td>{{ record.student.current_semester.number|default:"-" }}</td>
          <td>{{ record.get_payment_type_display|default:"-" }}</td>
          <td>₹{{ record.total_amount|default:"0.00" }}</td>
          <td>₹{{ record.paid_amount|default:"0.00" }}</td>
          <td>₹{{ record.remaining_amount|default:"0.00" }}</td>
          <td>
            <span class="badge badge-{% if record.status == 'confirmed' %}success{% elif record.status == 'partial' %}warning{% else %}danger{% endif %}">
              {{ record.get_status_display }}
            </span>
          </td>
          <td>{{ record.payment_date|date:"M d, Y"|default:"-" }}</td>
          <td>
            <div class="btn-group btn-group-sm">
              {% if record.payment_proof %}
                <a href="{{ record.payment_proof.url }}" target="_blank" class="btn btn-info btn-sm" title="View Payment Proof">
                  <i class="fas fa-eye"></i>
                </a>
              {% endif %}
              {% if record.receipt_pdf %}
                <a href="{{ record.receipt_pdf.url }}" target="_blank" class="btn btn-success btn-sm" title="Download Receipt">
                  <i class="fas fa-download"></i>
                </a>
              {% endif %}
            </div>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>
    </div>
  </div>
</div>
{% elif selected_branch_id or selected_semester_id or selected_section_id or selected_year_id or selected_status or selected_payment_type or search_query %}
<div class="alert alert-info mt-4">
  <i class="fas fa-info-circle mr-2"></i>
  No fee records found for the selected criteria. Try adjusting your filters.
</div>
{% else %}
<div class="alert alert-light mt-4">
  <i class="fas fa-info-circle mr-2"></i>
  <strong>Welcome to Fee Management!</strong><br>
  Select any combination of filters above to view fee records:
  <ul class="mb-0 mt-2">
    <li>Choose specific <strong>Branch, Semester, Section, or Year</strong> to narrow down results</li>
    <li>Filter by <strong>Payment Status</strong> to see payment progress</li>
    <li>Filter by <strong>Payment Type</strong> to see semester-wise or year-wise payments</li>
    <li>Use <strong>Search</strong> to find specific records by student details or remarks</li>
    <li>Leave filters empty and click "Show All Records" to view all fee records</li>
  </ul>
</div>
{% endif %}
    </div>
  </section>
{% endblock %}

{% block extra_js %}
<script>
  $(document).ready(function () {
    // Initialize form state
    // Enable semester dropdown if branch is already selected
    if ($('#branch').val()) {
      loadSemesters($('#branch').val());
    }

    // Enable section dropdown if semester is already selected
    if ($('#semester').val() && $('#branch').val()) {
      loadSections($('#branch').val(), $('#semester').val());
    }

    // Branch change handler
    $('#branch').on('change', function() {
      const branchId = $(this).val();

      // Reset dependent dropdowns but keep them enabled
      resetDependentDropdowns(['semester', 'section'], false);

      if (branchId) {
        loadSemesters(branchId);
      }
    });

    // Semester change handler
    $('#semester').on('change', function() {
      const semesterId = $(this).val();
      const branchId = $('#branch').val();

      // Reset dependent dropdowns but keep them enabled
      resetDependentDropdowns(['section'], false);

      if (semesterId && branchId) {
        loadSections(branchId, semesterId);
      }
    });

    // Function to load semesters based on branch
    function loadSemesters(branchId) {
      $.get('{% url "ajax_get_fee_semesters" %}', {
        branch_id: branchId
      })
      .done(function(data) {
        const semesterSelect = $('#semester');
        semesterSelect.empty().append('<option value="">-- All Semesters --</option>');

        if (data.semesters && data.semesters.length > 0) {
          $.each(data.semesters, function(index, semester) {
            semesterSelect.append(`<option value="${semester.id}">Semester ${semester.number}</option>`);
          });

          // Restore selected value if exists
          const selectedSemester = '{{ selected_semester_id }}';
          if (selectedSemester) {
            semesterSelect.val(selectedSemester);
          }
        }
      })
      .fail(function() {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load semesters. Please try again.',
        });
      });
    }

    // Function to load sections based on branch and semester
    function loadSections(branchId, semesterId) {
      $.get('{% url "ajax_get_fee_sections" %}', {
        branch_id: branchId,
        semester_id: semesterId
      })
      .done(function(data) {
        const sectionSelect = $('#section');
        sectionSelect.empty().append('<option value="">-- All Sections --</option>');

        if (data.sections && data.sections.length > 0) {
          $.each(data.sections, function(index, section) {
            sectionSelect.append(`<option value="${section.id}">${section.name}</option>`);
          });

          // Restore selected value if exists
          const selectedSection = '{{ selected_section_id }}';
          if (selectedSection) {
            sectionSelect.val(selectedSection);
          }
        }
      })
      .fail(function() {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load sections. Please try again.',
        });
      });
    }

    // Function to reset dependent dropdowns
    function resetDependentDropdowns(dropdowns, disable = true) {
      dropdowns.forEach(function(dropdown) {
        const label = dropdown === 'semester' ? 'All Semesters' :
                     dropdown === 'section' ? 'All Sections' :
                     `All ${dropdown.charAt(0).toUpperCase() + dropdown.slice(1)}s`;
        $(`#${dropdown}`).empty().append(`<option value="">-- ${label} --</option>`);
        if (disable) {
          $(`#${dropdown}`).prop('disabled', true);
        }
      });
    }

    // Reset form function
    window.resetForm = function() {
      $('#feeFilterForm')[0].reset();
      resetDependentDropdowns(['semester', 'section'], false);
    };

    // Show all records function
    window.showAllRecords = function() {
      // Set all filters to show all and submit
      $('#branch').val('');
      $('#semester').val('');
      $('#section').val('');
      $('#year').val('');
      $('#status').val('');
      $('#payment_type').val('');
      $('#search').val('');
      resetDependentDropdowns(['semester', 'section'], false);

      // Add a special parameter to show all records
      const form = $('#feeFilterForm');
      $('<input>').attr({
        type: 'hidden',
        name: 'show_all',
        value: 'true'
      }).appendTo(form);

      form.submit();
    };

    // Initialize DataTable using the global helper function with export buttons
    initDataTable('#feeRecordsTable', {
      "pageLength": 10,
      "order": [[9, "desc"]], // Sort by Payment Date
      "columnDefs": [
        { "orderable": false, "targets": 10 } // Disable sorting on actions column
      ],
      "dom": 'Bfrtip',
      "buttons": [
        {
          extend: 'csv',
          text: '<i class="fas fa-file-csv mr-1"></i> CSV',
          className: 'btn btn-sm btn-outline-secondary',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9] // Exclude actions column
          }
        },
        {
          extend: 'excel',
          text: '<i class="fas fa-file-excel mr-1"></i> Excel',
          className: 'btn btn-sm btn-outline-success',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9] // Exclude actions column
          }
        },
        {
          extend: 'pdf',
          text: '<i class="fas fa-file-pdf mr-1"></i> PDF',
          className: 'btn btn-sm btn-outline-danger',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9] // Exclude actions column
          },
          title: 'Fee Records - NITRA TECHNICAL CAMPUS'
        },
        {
          extend: 'colvis',
          text: '<i class="fas fa-columns mr-1"></i> Columns',
          className: 'btn btn-sm btn-outline-primary'
        }
      ]
    });
  });
</script>
{% endblock %}