{% load static i18n %}
{#
  This is a base template that uses only local static files instead of CDN files.
  This file is provided as an alternative to base.html for environments where:
  - Internet connectivity is limited or unavailable
  - You want to reduce external dependencies
  - You need to ensure all resources are served locally

  IMPORTANT: This file is NOT currently used in the project. It was created as requested
  but is not actively used. To use it in the future:
  1. Make sure all the referenced static files exist in your static directory
  2. Update your templates to extend from "base_local.html" instead of "base.html"

  Most of the required files are already available in the static/adminlte/plugins directory.
  Some files like Google Fonts may need to be downloaded separately.

  UNUSED PLUGINS IN STATIC/ADMINLTE:
  The following plugins in the static/adminlte/plugins directory are not required for base functionality:
  - bootstrap-colorpicker: Used for color selection, not used in base templates
  - bootstrap-slider: Used for slider controls, not used in base templates
  - bootstrap-switch: Used for toggle switches, not used in base templates
  - bootstrap4-duallistbox: Used for dual listboxes, not used in base templates
  - bs-custom-file-input: Used for custom file inputs, not used in base templates
  - bs-stepper: Used for form steps, not used in base templates
  - codemirror: Code editor with syntax highlighting, not used in base templates
  - daterangepicker: Date range picker, not used in base templates
  - dropzone: Drag and drop file uploads, not used in base templates
  - ekko-lightbox: Image lightbox, not used in base templates
  - fastclick: Eliminates click delays, not used in base templates
  - filterizr: Filtering and sorting, not used in base templates
  - flag-icon-css: Country flags, not used in base templates
  - fullcalendar: Calendar component, not used in base templates
  - inputmask: Input masking, not used in base templates
  - ion-rangeslider: Range slider, not used in base templates
  - jquery-knob: Circular gauges, not used in base templates
  - jquery-mapael: Vector maps, not used in base templates
  - jquery-mousewheel: Mouse wheel support, not used in base templates
  - jquery-validation: Form validation, not used in base templates
  - jqvmap: Vector maps, not used in base templates
  - jsgrid: Grid component, not used in base templates
  - overlayScrollbars: Custom scrollbars, not used in base templates
  - pace-progress: Page load progress bar, not used in base templates
  - raphael: Vector graphics library, not used in base templates
  - select2-bootstrap4-theme: Bootstrap theme for Select2, not used in base templates
  - summernote: WYSIWYG editor, not used in base templates
  - tempusdominus-bootstrap-4: DateTime picker, not used in base templates
  - toastr: Toast notifications, not used in base templates

  CONDITIONALLY USED PLUGINS:
  The following plugins are used in specific templates but not in the base template:
  - chart.js: Used in dashboard templates for charts and visualizations
  - flot: Used for plotting charts in some templates
  - select2: Used for enhanced select boxes in forms
  - uplot: Lightweight charts, used in some templates
#}
<!DOCTYPE html>
{% get_current_language as LANGUAGE_CODE %}
<html lang="{{ LANGUAGE_CODE }}">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" href="{% static 'logo/logo.png' %}">
  <meta name="description" content="College ERP System">

  <title>{% block title %}College ERP{% endblock %}</title>

  <!-- REQUIRED: Google Fonts (Local files) - Note: You may need to download these files -->
  <link rel="stylesheet" href="{% static 'fonts/source-sans-pro.css' %}">

  <!-- REQUIRED: Font Awesome (Local files) -->
  <link rel="stylesheet" href="{% static 'adminlte/plugins/fontawesome-free/css/all.min.css' %}">

  <!-- REQUIRED: Bootstrap 4 -->
  <link rel="stylesheet" href="{% static 'adminlte/plugins/bootstrap/css/bootstrap.min.css' %}">

  <!-- REQUIRED: AdminLTE -->
  <link rel="stylesheet" href="{% static 'adminlte/dist/css/adminlte.min.css' %}">

  <!-- REQUIRED: DataTables CSS -->
  <link rel="stylesheet" href="{% static 'adminlte/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css' %}">
  <link rel="stylesheet" href="{% static 'adminlte/plugins/datatables-buttons/css/buttons.bootstrap4.min.css' %}">

  <!-- REQUIRED: iCheck Bootstrap -->
  <link rel="stylesheet" href="{% static 'adminlte/plugins/icheck-bootstrap/icheck-bootstrap.min.css' %}">

  <!-- REQUIRED: SweetAlert2 -->
  <link rel="stylesheet" href="{% static 'adminlte/plugins/sweetalert2/sweetalert2.min.css' %}">

  <!--
    OPTIONAL CSS (uncomment as needed):
    <link rel="stylesheet" href="{% static 'adminlte/plugins/select2/css/select2.min.css' %}">
    <link rel="stylesheet" href="{% static 'adminlte/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
    <link rel="stylesheet" href="{% static 'adminlte/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css' %}">
    <link rel="stylesheet" href="{% static 'adminlte/plugins/summernote/summernote-bs4.min.css' %}">
  -->

  {% block extra_css %}{% endblock %}
  {% block head_scripts %}{% endblock %}
</head>

<body class="{% block body_class %}hold-transition{% endblock %}">

  <!-- Preloader -->
  <div class="preloader flex-column justify-content-center align-items-center">
    <img class="animation__shake" src="{% static 'logo/logo.png' %}" alt="College ERP Logo" height="60" width="60">
    <div class="spinner-border mt-3" role="status">
      <span class="sr-only">{% trans "Loading..." %}</span>
    </div>
  </div>

  {% block navbar %}{% endblock %}
  {% block sidebar %}{% endblock %}

  <!-- Main Body Container -->
  <div class="content-wrapper">
    <section class="content">
      <div class="container-fluid pt-3">
        <!-- Flash Messages -->
        {% if messages %}
          {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
              {{ message }}
              <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
          {% endfor %}
        {% endif %}
        {% block content %} {% endblock %}
      </div>
    </section>
  </div>

  <!-- REQUIRED: Core Scripts -->
  <script src="{% static 'adminlte/plugins/jquery/jquery.min.js' %}"></script>
  <script src="{% static 'adminlte/plugins/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
  <script src="{% static 'adminlte/dist/js/adminlte.min.js' %}"></script>

  <!-- REQUIRED: DataTables Core -->
  <script src="{% static 'adminlte/plugins/datatables/jquery.dataTables.min.js' %}"></script>
  <script src="{% static 'adminlte/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js' %}"></script>

  <!-- REQUIRED: DataTables Buttons -->
  <script src="{% static 'adminlte/plugins/datatables-buttons/js/dataTables.buttons.min.js' %}"></script>
  <script src="{% static 'adminlte/plugins/datatables-buttons/js/buttons.bootstrap4.min.js' %}"></script>
  <script src="{% static 'adminlte/plugins/datatables-buttons/js/buttons.html5.min.js' %}"></script>
  <script src="{% static 'adminlte/plugins/datatables-buttons/js/buttons.print.min.js' %}"></script>
  <script src="{% static 'adminlte/plugins/datatables-buttons/js/buttons.colVis.min.js' %}"></script>

  <!-- REQUIRED: File Export (JSZip and PDFMake) -->
  <script src="{% static 'adminlte/plugins/jszip/jszip.min.js' %}"></script>
  <script src="{% static 'adminlte/plugins/pdfmake/pdfmake.min.js' %}"></script>
  <script src="{% static 'adminlte/plugins/pdfmake/vfs_fonts.js' %}"></script>

  <!-- REQUIRED: SweetAlert2 -->
  <script src="{% static 'adminlte/plugins/sweetalert2/sweetalert2.all.min.js' %}"></script>

  <!--
    OPTIONAL JavaScript (uncomment as needed):
    <!-- Charts and Visualization -->
    <script src="{% static 'adminlte/plugins/chart.js/Chart.min.js' %}"></script>
    <script src="{% static 'adminlte/plugins/flot/jquery.flot.js' %}"></script>
    <script src="{% static 'adminlte/plugins/uplot/uPlot.iife.min.js' %}"></script>

    <!-- Form Enhancements -->
    <script src="{% static 'adminlte/plugins/select2/js/select2.full.min.js' %}"></script>
    <script src="{% static 'adminlte/plugins/moment/moment.min.js' %}"></script>
    <script src="{% static 'adminlte/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js' %}"></script>
    <script src="{% static 'adminlte/plugins/summernote/summernote-bs4.min.js' %}"></script>

    <!-- Advanced DataTables Extensions -->
    <script src="{% static 'adminlte/plugins/datatables-responsive/js/dataTables.responsive.min.js' %}"></script>
    <script src="{% static 'adminlte/plugins/datatables-responsive/js/responsive.bootstrap4.min.js' %}"></script>
  -->

  <!-- Custom JS -->
  <script src="{% static 'js/custom.js' %}"></script>

  <!-- Custom Initialization -->
  <script>
    // Preloader removal after content load
    $(window).on('load', function() {
      $('.preloader').fadeOut('slow');
    });

    // Initialize tooltips and other UI elements
    $(document).ready(function() {
      // Initialize Tooltips
      $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        const target = $(e.target).attr("href"); // Activated tab
        if ($(target).find('.table').length && $.fn.DataTable.isDataTable($(target).find('.table'))) {
          try {
            $(target).find('.table').DataTable().columns.adjust().draw(); // Adjust and redraw
          } catch (error) {
            console.warn('DataTable adjust error:', error.message);
          }
        }
      });

      // Initialize basic DataTables for tables with 'auto-datatable' class
      $('.auto-datatable').each(function() {
        const tableId = $(this).attr('id');
        if (tableId && !$.fn.DataTable.isDataTable(this)) {
          try {
            initDataTable('#' + tableId);
          } catch (error) {
            console.error('Auto DataTable initialization failed for:', tableId, error.message);
          }
        } else if (!tableId) {
          console.warn('Auto-datatable element missing ID attribute');
        }
      });

      // Global error handler for DataTables
      $.fn.dataTable.ext.errMode = function (settings, helpPage, message) {
        console.error('DataTables Error:', message);
        // Don't show alert, just log to console
      };
    });

    // Convert Flash Messages to SweetAlert2 Toast
    {% if messages %}
      {% for message in messages %}
        Swal.fire({
          icon: '{{ message.tags }}' === 'danger' ? 'error' : '{{ message.tags }}',
          text: '{{ message }}',
          showConfirmButton: false,
          timer: 3000,
          toast: true,
          position: 'top-end',
          background: {% if message.tags == 'success' %}'#d4edda'{% elif message.tags == 'danger' %}'#f8d7da'{% else %}'#d1ecf1'{% endif %},
          iconColor: {% if message.tags == 'success' %}'#28a745'{% elif message.tags == 'danger' %}'#dc3545'{% else %}'#17a2b8'{% endif %}
        });
      {% endfor %}
    {% endif %}
 </script>

  {% block extra_js %}{% endblock %}
  {% block footer_scripts %}{% endblock %}
</body>
</html>
