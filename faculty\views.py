from datetime import date, datetime
from decimal import Decimal, InvalidOperation

from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db import transaction
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse
from django.db.models import Q, Count
from django.contrib.auth.forms import PasswordChangeForm
from django.contrib.auth import update_session_auth_hash
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.template.loader import render_to_string

from attendance.forms import DateRangeForm
from attendance.models import LectureSession, StudentAttendance
from faculty.models import FacultyProfile
from feedback.forms import FeedbackResponseForm
from feedback.models import Feedback
from materials.models import Assessment, PreviousPaper, StudyNotes
from notifications.forms import FacultyNotificationForm, StudentNotificationFilterForm
from notifications.models import Notification
from results.forms import ResultFilterForm
from results.models import Result, ResultType

from timetable.models import SubjectAllocation, Timetable, Subject
from student.models import StudentProfile
from academic.models import Branch, Section, Semester, Year
from users.views import role_required

DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']


@role_required('3')
@login_required
def faculty_home(request):
    """Faculty dashboard with key metrics and quick links"""
    try:
        faculty = request.user.facultyprofile
        branch = faculty.branches.all()
    except FacultyProfile.DoesNotExist:
        messages.error(request, "Faculty profile not found.")
        return redirect('login')
    except AttributeError:
        messages.error(request, "Branch not assigned to faculty.")
        return redirect('login')

    # Metrics
    assigned_subjects = SubjectAllocation.objects.filter(faculty=faculty).values('subject').distinct().count()
    today = date.today()
    today_classes = Timetable.objects.filter(
        faculty=faculty, day=today.strftime('%A')
    ).order_by('period_time')[:5]
    uploaded_assessments = Assessment.objects.filter(uploaded_by=faculty).count()
    total_students = StudentProfile.objects.filter(
        section__in=SubjectAllocation.objects.filter(faculty=faculty).values('sections')
    ).distinct().count()

    # Assessment data for chart
    assessment_counts = Assessment.objects.filter(uploaded_by=faculty).values('subject__name').annotate(count=Count('id')).order_by('subject__name')[:5]
    assessment_chart_data = {
        'labels': [item['subject__name'] for item in assessment_counts],
        'counts': [item['count'] for item in assessment_counts],
    }

    # Recent feedback (notifications are handled by context processor)
    recent_feedback = Feedback.objects.filter(
        faculty=faculty
    ).order_by('-submitted_at')[:5]

    context = {
        'faculty': faculty,
        'branch': branch,
        'assigned_subjects': assigned_subjects,
        'today_classes': today_classes,
        'uploaded_assessments': uploaded_assessments,
        'total_students': total_students,
        'assessment_chart_data': assessment_chart_data,
        'recent_feedback': recent_feedback,
        'page_title': 'Faculty Dashboard',
    }
    return render(request, 'faculty/home.html', context)

@role_required('3')
@login_required
def faculty_timetable(request):
    """Faculty views their timetable in grid format"""
    try:
        faculty = request.user.facultyprofile
    except FacultyProfile.DoesNotExist:
        messages.error(request, "Faculty profile not found.")
        return redirect('dashboard')

    # Get all timetable entries for the faculty
    timetable_entries = Timetable.objects.filter(
        faculty=faculty
    ).select_related('subject', 'branch', 'semester', 'section')

    # Define days and time slots (matching the form choices)
    days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    time_slots = [
        {'slot': '9:00-10:00'},
        {'slot': '10:00-11:00'},
        {'slot': '11:00-12:00'},
        {'slot': '12:00-1:00'},
        {'slot': '2:15-3:15'},
        {'slot': '3:15-4:15'},
        {'slot': '4:15-5:00'},
    ]

    # Organize timetable data in grid format
    timetable_grid = {}
    for day in days:
        timetable_grid[day] = {}
        for slot in time_slots:
            timetable_grid[day][slot['slot']] = {
                'subject': '-',
                'branch_section': '-'
            }

    # Fill the grid with actual timetable data
    for entry in timetable_entries:
        if entry.day in timetable_grid and entry.period_time in timetable_grid[entry.day]:
            branch_section = f"{entry.branch.code} - S{entry.semester.number} - {entry.section.name}" if entry.branch and entry.semester and entry.section else '-'
            timetable_grid[entry.day][entry.period_time] = {
                'subject': entry.subject.name if entry.subject else '-',
                'branch_section': branch_section
            }

    # Create faculty name for display
    faculty_name = f"{faculty.user.get_full_name()} - Faculty Timetable"

    context = {
        'batch_timetables': {faculty_name: timetable_grid},
        'time_slots': time_slots,
        'days': days,
        'faculty': faculty,
        'page_title': 'Timetable',
    }
    return render(request, 'faculty/timetable.html', context)



@role_required('3')
@login_required
def faculty_view_attendance(request):
    """Faculty view/edit attendance with hierarchical filtering: Branch → Semester → Section → Subject → Date Range"""
    try:
        faculty = request.user.facultyprofile
        faculty_branches = faculty.branches.all()
    except FacultyProfile.DoesNotExist:
        messages.error(request, "Faculty profile not found.")
        return redirect('faculty_home')

    if not faculty_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('faculty_home')

    # Get filter parameters
    selected_branch_id = request.GET.get('branch')
    selected_semester_id = request.GET.get('semester')
    selected_section_id = request.GET.get('section')
    selected_subject_id = request.GET.get('subject')
    selected_start_date = request.GET.get('start_date')
    selected_end_date = request.GET.get('end_date')



    # Initialize context
    context = {
        'faculty_branches': faculty_branches,
        'semesters': [],
        'sections': [],
        'subjects': [],
        'students': [],
        'sessions': [],
        'date_range_form': DateRangeForm(initial={
            'start_date': selected_start_date,
            'end_date': selected_end_date
        }),
        'selected_branch_id': selected_branch_id,
        'selected_semester_id': selected_semester_id,
        'selected_section_id': selected_section_id,
        'selected_subject_id': selected_subject_id,
        'selected_start_date': selected_start_date,
        'selected_end_date': selected_end_date,
        'page_title': 'View/Edit Attendance',
    }

    # Load semesters if branch is selected
    if selected_branch_id:
        try:
            branch = faculty_branches.get(id=selected_branch_id)
            semesters = Semester.objects.filter(
                subjectallocation__faculty=faculty,
                subjectallocation__branches=branch
            ).distinct().order_by('number')
            context['semesters'] = semesters
        except Branch.DoesNotExist:
            messages.error(request, "Selected branch not found or not accessible.")

    # Load sections if semester is selected
    if selected_branch_id and selected_semester_id:
        try:
            branch = faculty_branches.get(id=selected_branch_id)
            semester = Semester.objects.get(id=selected_semester_id)
            sections = Section.objects.filter(
                subjectallocation__faculty=faculty,
                subjectallocation__branches=branch,
                subjectallocation__semester=semester
            ).distinct().order_by('name')
            context['sections'] = sections
        except (Branch.DoesNotExist, Semester.DoesNotExist):
            messages.error(request, "Selected branch or semester not found.")

    # Load subjects if section is selected
    if selected_branch_id and selected_semester_id and selected_section_id:
        try:
            branch = faculty_branches.get(id=selected_branch_id)
            semester = Semester.objects.get(id=selected_semester_id)
            section = Section.objects.get(id=selected_section_id)
            subjects = Subject.objects.filter(
                allocations__faculty=faculty,
                allocations__branches=branch,
                allocations__semester=semester,
                allocations__sections=section
            ).distinct().order_by('name')
            context['subjects'] = subjects
        except (Branch.DoesNotExist, Semester.DoesNotExist, Section.DoesNotExist):
            messages.error(request, "Selected branch, semester, or section not found.")

    # Load students and attendance data - flexible filtering
    if any([selected_branch_id, selected_semester_id, selected_section_id, selected_subject_id, selected_start_date, selected_end_date]):
        try:
            # Parse and validate date range if provided
            start_date = None
            end_date = None
            if selected_start_date and selected_end_date:
                from datetime import datetime
                start_date = datetime.strptime(selected_start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(selected_end_date, '%Y-%m-%d').date()

                # Validate date range
                if start_date > end_date:
                    messages.error(request, "Start date cannot be after end date.")
                    return render(request, "faculty/faculty_view_attendance.html", context)

            # Build query for subject allocations
            allocation_filters = {'faculty': faculty}

            if selected_branch_id:
                allocation_filters['branches__id'] = selected_branch_id
            if selected_semester_id:
                allocation_filters['semester__id'] = selected_semester_id
            if selected_section_id:
                allocation_filters['sections__id'] = selected_section_id
            if selected_subject_id:
                allocation_filters['subject__id'] = selected_subject_id

            # Get subject allocations
            subject_allocations = SubjectAllocation.objects.filter(
                **allocation_filters
            ).select_related('subject', 'semester').prefetch_related('branches', 'sections')

            if subject_allocations.exists():
                # Get sessions based on allocations and date range
                session_filters = {'subject_allocation__in': subject_allocations}
                if start_date and end_date:
                    session_filters['date__range'] = [start_date, end_date]

                sessions = LectureSession.objects.filter(
                    **session_filters
                ).select_related('subject_allocation__subject').order_by('date')

                context['sessions'] = sessions

                # Get students based on the allocations
                student_filters = {}
                if selected_branch_id:
                    student_filters['branch__id'] = selected_branch_id
                if selected_semester_id:
                    student_filters['current_semester__id'] = selected_semester_id
                if selected_section_id:
                    student_filters['section__id'] = selected_section_id

                # If no specific filters, get students from all sections in the allocations
                if not any([selected_branch_id, selected_semester_id, selected_section_id]):
                    section_ids = []
                    for allocation in subject_allocations:
                        section_ids.extend(allocation.sections.values_list('id', flat=True))
                    student_filters['section__id__in'] = list(set(section_ids))

                students = StudentProfile.objects.filter(
                    **student_filters
                ).select_related('user', 'branch', 'section', 'current_semester').order_by('roll_number')

                context['students'] = students

                # Fetch attendance records for all students and sessions
                if sessions.exists() and students.exists():
                    attendances = StudentAttendance.objects.filter(
                        session__in=sessions,
                        student__in=students
                    ).select_related('student', 'session')

                    # Create a nested dictionary for easier access in the template
                    attendance_map = {}
                    for att in attendances:
                        if att.student.id not in attendance_map:
                            attendance_map[att.student.id] = {}
                        attendance_map[att.student.id][att.session.id] = att

                    context['attendance_map'] = attendance_map



                    # Add summary information
                    context['total_sessions'] = sessions.count()
                    context['total_students'] = students.count()
                    context['total_allocations'] = subject_allocations.count()

                # Set selected objects for display
                if selected_subject_id:
                    try:
                        context['selected_subject'] = Subject.objects.get(id=selected_subject_id)
                    except Subject.DoesNotExist:
                        pass

            else:
                messages.info(request, "No subject allocations found for the selected criteria.")

        except ValueError as e:
            messages.error(request, "Invalid date format. Please use YYYY-MM-DD format.")

    # Handle attendance editing
    if request.method == 'POST':
        try:
            # Get all students and sessions from the current context
            updated_count = 0

            # Re-fetch students and sessions based on current filters
            if any([selected_branch_id, selected_semester_id, selected_section_id, selected_subject_id]):
                # Build query for subject allocations
                allocation_filters = {'faculty': faculty}

                if selected_branch_id:
                    allocation_filters['branches__id'] = selected_branch_id
                if selected_semester_id:
                    allocation_filters['semester__id'] = selected_semester_id
                if selected_section_id:
                    allocation_filters['sections__id'] = selected_section_id
                if selected_subject_id:
                    allocation_filters['subject__id'] = selected_subject_id

                # Get subject allocations
                subject_allocations = SubjectAllocation.objects.filter(**allocation_filters)

                if subject_allocations.exists():
                    # Get sessions based on allocations and date range
                    session_filters = {'subject_allocation__in': subject_allocations}
                    if selected_start_date and selected_end_date:
                        from datetime import datetime
                        start_date = datetime.strptime(selected_start_date, '%Y-%m-%d').date()
                        end_date = datetime.strptime(selected_end_date, '%Y-%m-%d').date()
                        session_filters['date__range'] = [start_date, end_date]

                    sessions = LectureSession.objects.filter(**session_filters)

                    # Get students based on the allocations
                    student_filters = {}
                    if selected_branch_id:
                        student_filters['branch__id'] = selected_branch_id
                    if selected_semester_id:
                        student_filters['current_semester__id'] = selected_semester_id
                    if selected_section_id:
                        student_filters['section__id'] = selected_section_id

                    students = StudentProfile.objects.filter(**student_filters)

                    # Process attendance for each student and session
                    for student in students:
                        for session in sessions:
                            # Check if this attendance record was submitted
                            is_present_key = f'is_present_{student.id}_{session.id}'
                            remarks_key = f'remarks_{student.id}_{session.id}'

                            is_present = is_present_key in request.POST
                            remarks = request.POST.get(remarks_key, '').strip() or None

                            # Verify faculty has permission to edit this session
                            if session.subject_allocation.faculty != faculty:
                                continue

                            # Create or update attendance
                            StudentAttendance.objects.update_or_create(
                                session=session,
                                student=student,
                                defaults={
                                    'is_present': is_present,
                                    'remarks': remarks
                                }
                            )
                            updated_count += 1

            if updated_count > 0:
                messages.success(request, f"Attendance updated for {updated_count} record(s) successfully.")
            else:
                messages.warning(request, "No attendance records were updated.")

            # Redirect to preserve GET parameters
            query_params = request.GET.urlencode()
            return redirect(f"{reverse('faculty_view_attendance')}?{query_params}")

        except Exception as e:
            messages.error(request, "Error updating attendance. Please try again.")

    return render(request, "faculty/faculty_view_attendance.html", context)

@role_required('3')
@login_required
def delete_material(request, material_type, material_id):
    """Delete a study note, previous paper, or assessment uploaded by the faculty"""
    faculty = request.user.facultyprofile
    try:
        if material_type == 'study_notes':
            material = get_object_or_404(StudyNotes, id=material_id, uploaded_by=faculty)
        elif material_type == 'previous_paper':
            material = get_object_or_404(PreviousPaper, id=material_id, uploaded_by=faculty)
        elif material_type == 'assessment':
            material = get_object_or_404(Assessment, id=material_id, uploaded_by=faculty)
        else:
            messages.error(request, "Invalid material type.")
            return redirect('upload_materials_new')
        material.delete()
        messages.success(request, "Material deleted successfully.")
    except Exception as e:
        messages.error(request, "Error deleting material. Please try again.")
    return redirect('upload_materials_new')


@role_required('3')
@login_required
def view_feedback(request):

    """Faculty views and responds to feedback"""
    try:
        faculty = request.user.facultyprofile
    except FacultyProfile.DoesNotExist:
        messages.error(request, "Faculty profile not found.")
        return redirect('dashboard')

    # Fetch feedback where faculty is assigned or subject is in their allocation
    allocated_subjects = SubjectAllocation.objects.filter(faculty=faculty).values('subject')
    feedbacks = Feedback.objects.filter(
        Q(faculty=faculty) | Q(subject__in=allocated_subjects)
    ).order_by('-submitted_at')

    # Handle response submission
    if request.method == 'POST':
        feedback_id = request.POST.get('feedback_id')
        feedback = get_object_or_404(Feedback, id=feedback_id, faculty=faculty)
        form = FeedbackResponseForm(data=request.POST, instance=feedback)
        if form.is_valid():
            try:
                form.save()
                messages.success(request, "Response submitted successfully.")
                return redirect('faculty_feedbacks')
            except Exception as e:
                messages.error(request, "Error submitting response. Please try again.")
        else:
            messages.error(request, "Invalid response form.")
    else:
        form = FeedbackResponseForm()

    context = {
        'form': form,
        'feedbacks': feedbacks,
        'page_title': 'Manage Feedback',
    }
    return render(request, 'faculty/feedback.html', context)


@role_required('3')
@login_required
def faculty_profile(request):
    user = request.user
    password_form = PasswordChangeForm(user)

    if request.method == 'POST':
        if 'update_profile' in request.POST:
            user.first_name = request.POST.get('first_name')
            user.last_name = request.POST.get('last_name')
            user.email = request.POST.get('email')

            # Handle image upload with validation
            if 'profile_pic' in request.FILES:
                profile_pic = request.FILES['profile_pic']

                # Validate size (max 2MB)
                if profile_pic.size > 2 * 1024 * 1024:
                    messages.error(request, "Image file size should not exceed 2MB.")
                    return redirect('faculty_profile')

                # Validate format
                valid_extensions = ['image/jpeg', 'image/png']
                if profile_pic.content_type not in valid_extensions:
                    messages.error(request, "Only JPEG and PNG formats are allowed.")
                    return redirect('faculty_profile')

                # Skip image dimension validation for now
                # We would need to import PIL or another library to validate dimensions

                user.profile_pic = profile_pic

            user.save()
            messages.success(request, "Profile updated successfully.")
            return redirect('faculty_profile')

        elif 'change_password' in request.POST:
            password_form = PasswordChangeForm(user, request.POST)
            if password_form.is_valid():
                password_form.save()
                update_session_auth_hash(request, user)
                messages.success(request, "Password changed successfully.")
                return redirect('faculty_profile')
            else:
                messages.error(request, "Please correct the errors below.")

    else:
        password_form = PasswordChangeForm(user)

    return render(request, "faculty/profile.html", {
        'user': user,
        'password_form': password_form,
        'page_title': "My Profile"
    })


@role_required('3')
@login_required
def mark_attendance_new(request):
    """New view for marking attendance with hierarchical selection: semester -> section -> subject"""
    faculty = request.user.facultyprofile

    # Get parameters from request
    selected_semester_id = request.GET.get('semester') or request.POST.get('semester')
    selected_section_id = request.GET.get('section') or request.POST.get('section')
    selected_subject_id = request.GET.get('subject') or request.POST.get('subject')
    session_id = request.session.get('new_session_id') or request.POST.get('session_id')

    # Get semesters that faculty teaches
    semesters = Semester.objects.filter(
        subjectallocation__faculty=faculty
    ).distinct().order_by('number')

    context = {
        'semesters': semesters,
        'sections': [],
        'subjects': [],
        'students': [],
        'selected_semester_id': selected_semester_id,
        'selected_section_id': selected_section_id,
        'selected_subject_id': selected_subject_id,
        'session_id': session_id,
        'today': date.today().strftime('%Y-%m-%d'),
        'page_title': 'Mark Attendance'
    }

    # If semester is selected, get sections
    if selected_semester_id:
        try:
            semester = Semester.objects.get(id=selected_semester_id)
            sections = Section.objects.filter(
                subjectallocation__faculty=faculty,
                subjectallocation__semester=semester
            ).distinct().order_by('name')
            context['sections'] = sections
        except Semester.DoesNotExist:
            messages.error(request, "Selected semester not found.")

    # If section is selected, get subjects
    if selected_semester_id and selected_section_id:
        try:
            semester = Semester.objects.get(id=selected_semester_id)
            section = Section.objects.get(id=selected_section_id)
            subject_allocations = SubjectAllocation.objects.filter(
                faculty=faculty,
                semester=semester,
                sections=section
            ).select_related('subject').distinct()
            context['subjects'] = [allocation.subject for allocation in subject_allocations]
            context['subject_allocations'] = subject_allocations
        except (Semester.DoesNotExist, Section.DoesNotExist):
            messages.error(request, "Selected semester or section not found.")

    # If subject is selected, get students
    if selected_semester_id and selected_section_id and selected_subject_id:
        try:
            semester = Semester.objects.get(id=selected_semester_id)
            section = Section.objects.get(id=selected_section_id)
            subject = Subject.objects.get(id=selected_subject_id)

            # Get the specific allocation
            allocation = SubjectAllocation.objects.filter(
                faculty=faculty,
                semester=semester,
                sections=section,
                subject=subject
            ).first()

            if allocation:
                branches = allocation.branches.all()
                students = StudentProfile.objects.filter(
                    section=section,
                    branch__in=branches
                ).select_related('user', 'branch', 'section')
                context['students'] = students
                context['selected_allocation'] = allocation
            else:
                messages.error(request, "No allocation found for selected combination.")
        except (Semester.DoesNotExist, Section.DoesNotExist, Subject.DoesNotExist):
            messages.error(request, "Selected semester, section, or subject not found.")

    # Handle session creation
    if request.method == 'POST' and 'create_session' in request.POST:
        date_str = request.POST.get('date')
        lecture_count = int(request.POST.get('lecture_count', 1))
        topic = request.POST.get('topic', '').strip()

        if not date_str:
            messages.error(request, "Date is required.")
            return redirect('mark_attendance_new')

        try:
            session_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # Check if date is in the future
            if session_date > date.today():
                messages.error(request, "Cannot create sessions for future dates.")
                return redirect('mark_attendance_new')

            # Get allocation
            semester = Semester.objects.get(id=selected_semester_id)
            section = Section.objects.get(id=selected_section_id)
            subject = Subject.objects.get(id=selected_subject_id)

            allocation = SubjectAllocation.objects.filter(
                faculty=faculty,
                semester=semester,
                sections=section,
                subject=subject
            ).first()

            if not allocation:
                messages.error(request, "No allocation found for selected combination.")
                return redirect('mark_attendance_new')

            # Check if session exists for this date and allocation
            existing_session = LectureSession.objects.filter(
                subject_allocation=allocation,
                date=session_date
            ).first()

            if existing_session:
                # Update existing session
                existing_session.lecture_count += lecture_count
                if topic:
                    if existing_session.topic:
                        existing_session.topic += f"\n{topic}"
                    else:
                        existing_session.topic = topic
                existing_session.save()
                session_id = existing_session.id
                request.session['new_session_id'] = session_id
                messages.success(request, f"Updated existing session for {session_date}. Added {lecture_count} lecture(s).")
            else:
                # Create new session
                new_session = LectureSession.objects.create(
                    subject_allocation=allocation,
                    date=session_date,
                    lecture_count=lecture_count,
                    topic=topic
                )
                session_id = new_session.id
                request.session['new_session_id'] = session_id
                messages.success(request, f"Created new session for {session_date}.")

            # Redirect to same page with session ID
            return redirect(f"{reverse('mark_attendance_new')}?semester={selected_semester_id}&section={selected_section_id}&subject={selected_subject_id}")

        except ValueError:
            messages.error(request, "Invalid date format.")
        except (Semester.DoesNotExist, Section.DoesNotExist, Subject.DoesNotExist):
            messages.error(request, "Selected semester, section, or subject not found.")
        except Exception as e:
            messages.error(request, f"Error creating session: {str(e)}")

    # Handle attendance marking
    if request.method == 'POST' and 'mark_attendance' in request.POST and session_id:
        try:
            # Get session
            session = LectureSession.objects.get(id=session_id)

            # Get student IDs and present status
            student_ids = request.POST.getlist('student_ids')
            present_student_ids = request.POST.getlist('student_present')
            present_set = set(present_student_ids)

            # Use transaction to ensure all or nothing
            with transaction.atomic():
                updated_count = 0

                # Delete existing attendance records for this session
                StudentAttendance.objects.filter(session=session).delete()

                # Create new attendance records
                for student_id in student_ids:
                    try:
                        student = StudentProfile.objects.get(id=student_id)
                        is_present = student_id in present_set
                        remarks = request.POST.get(f'student_remarks_{student_id}', '').strip()

                        # Create attendance record
                        StudentAttendance.objects.create(
                            session=session,
                            student=student,
                            is_present=is_present,
                            remarks=remarks or None
                        )
                        updated_count += 1
                    except Exception as e:
                        raise

            if updated_count > 0:
                messages.success(request, f"Attendance marked for {updated_count} student(s) successfully.")
            else:
                messages.warning(request, "No attendance records were updated.")

            # Clear session ID
            request.session.pop('new_session_id', None)

            # Redirect to same page
            return redirect(f"{reverse('mark_attendance_new')}?semester={selected_semester_id}&section={selected_section_id}&subject={selected_subject_id}")

        except LectureSession.DoesNotExist:
            messages.error(request, "Session not found.")
        except StudentProfile.DoesNotExist:
            messages.error(request, "One or more students not found.")
        except Exception as e:
            messages.error(request, f"Error marking attendance: {str(e)}")

    return render(request, 'faculty/attendance_form_new.html', context)

@role_required('3')
@login_required
def upload_materials_new(request):
    """New implementation for uploading study materials, previous papers, and assessments"""
    try:
        faculty = request.user.facultyprofile
    except Exception as e:
        messages.error(request, "Faculty profile not found.")
        return redirect('faculty_home')

    # Get subjects assigned to the faculty
    subjects = Subject.objects.filter(
        allocations__faculty=faculty
    ).distinct().order_by('name')

    # Fetch uploaded materials
    study_notes = StudyNotes.objects.filter(uploaded_by=faculty).select_related('subject')
    previous_papers = PreviousPaper.objects.filter(uploaded_by=faculty).select_related('subject')
    assessments = Assessment.objects.filter(uploaded_by=faculty).select_related('subject')

    if request.method == 'POST':
        material_type = request.POST.get('material_type')

        if material_type == 'study_notes':
            try:
                # Get form data
                subject_id = request.POST.get('subject')
                description = request.POST.get('description', '').strip()
                file_upload = request.FILES.get('file_upload')

                # Validate data
                if not subject_id or not file_upload:
                    messages.error(request, "Subject and file upload are required.")
                    return redirect('upload_materials_new')

                # Get subject
                subject = Subject.objects.get(id=subject_id)

                # Create study notes
                study_note = StudyNotes(
                    subject=subject,
                    uploaded_by=faculty,
                    file_upload=file_upload,
                    description=description or None
                )

                # Save study notes
                study_note.save()

                messages.success(request, "Study notes uploaded successfully.")
            except Subject.DoesNotExist:
                messages.error(request, "Selected subject not found.")
            except Exception as e:
                messages.error(request, "Error uploading study notes. Please try again.")

        elif material_type == 'previous_paper':
            try:
                # Get form data
                subject_id = request.POST.get('subject')
                year = request.POST.get('year')
                exam_type = request.POST.get('exam_type', '').strip()
                file_upload = request.FILES.get('file_upload')

                # Validate data
                if not subject_id or not year or not exam_type or not file_upload:
                    messages.error(request, "All fields are required for previous papers.")
                    return redirect('upload_materials_new')

                # Get subject
                subject = Subject.objects.get(id=subject_id)

                # Create previous paper
                previous_paper = PreviousPaper(
                    subject=subject,
                    uploaded_by=faculty,
                    file_upload=file_upload,
                    year=year,
                    exam_type=exam_type
                )

                # Save previous paper
                previous_paper.save()

                messages.success(request, "Previous paper uploaded successfully.")
            except Subject.DoesNotExist:
                messages.error(request, "Selected subject not found.")
            except Exception as e:
                messages.error(request, "Error uploading previous paper. Please try again.")

        elif material_type == 'assessment':
            try:
                # Get form data
                subject_id = request.POST.get('subject')
                assessment_title = request.POST.get('assessment_title', '').strip()
                file_upload = request.FILES.get('file_upload')

                # Validate data
                if not subject_id or not assessment_title or not file_upload:
                    messages.error(request, "All fields are required for assessments.")
                    return redirect('upload_materials_new')

                # Get subject
                subject = Subject.objects.get(id=subject_id)

                # Create assessment
                assessment = Assessment(
                    subject=subject,
                    uploaded_by=faculty,
                    file_upload=file_upload,
                    assessment_title=assessment_title
                )

                # Save assessment
                assessment.save()

                messages.success(request, "Assessment uploaded successfully.")
            except Subject.DoesNotExist:
                messages.error(request, "Selected subject not found.")
            except Exception as e:
                messages.error(request, "Error uploading assessment. Please try again.")

        # Redirect to refresh the page and show the updated list
        return redirect('upload_materials_new')

    # Prepare context for template
    context = {
        'subjects': subjects,
        'study_notes': study_notes,
        'previous_papers': previous_papers,
        'assessments': assessments,
        'current_year': datetime.now().year,
        'page_title': 'Upload Materials (New)'
    }

    return render(request, 'faculty/upload_materials_new.html', context)

@role_required('3')
@login_required
def faculty_notifications(request):
    """Faculty views and sends notifications"""
    try:
        faculty = request.user.facultyprofile
    except FacultyProfile.DoesNotExist:
        messages.error(request, "Faculty profile not found.")
        return redirect('dashboard')

    form = FacultyNotificationForm(faculty=faculty, data=request.POST or None, files=request.FILES or None)
    filter_form = StudentNotificationFilterForm(data=request.POST or None)  # Reuse student filter for simplicity

    # Fetch notifications sent by this faculty
    sent_notifications = Notification.objects.filter(
        sender=request.user
    ).order_by('-created_at')

    # Fetch notifications where faculty is a recipient
    received_notifications = Notification.objects.filter(
        Q(recipient_group='all_faculty') |
        Q(recipient_group='faculty_and_students') |
        Q(recipient=request.user)
    ).order_by('-created_at')

    # Apply semester filter
    if filter_form.is_valid():
        selected_semester = filter_form.cleaned_data['semester']
        if selected_semester:
            sent_notifications = sent_notifications.filter(semester=selected_semester)
            received_notifications = received_notifications.filter(semester=selected_semester)

    # Handle sending notifications
    if request.method == 'POST' and 'send_notification' in request.POST:
        if form.is_valid():
            try:
                notification = form.save(commit=False)
                notification.sender = request.user
                notification.sender_type = 'faculty'
                notification.save()
                messages.success(request, "Notification sent successfully.")
                return redirect('faculty_notifications')
            except Exception as e:
                messages.error(request, "Error sending notification. Please try again.")
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"Error in {field}: {error}")

    context = {
        'form': form,
        'filter_form': filter_form,
        'sent_notifications': sent_notifications,
        'received_notifications': received_notifications,
        'page_title': 'Manage Notifications',
    }
    return render(request, 'faculty/notifications.html', context)


@role_required('3')
@login_required
@require_POST
def mark_notification_read(request):
    """Mark a notification as read"""
    try:
        notification_id = request.POST.get('notification_id')
        if not notification_id:
            return JsonResponse({'success': False, 'error': 'Notification ID is required'})

        notification = get_object_or_404(Notification, id=notification_id)

        # Check if the faculty is a recipient of this notification
        if not notification.is_recipient_in_group(request.user):
            return JsonResponse({'success': False, 'error': 'You are not authorized to mark this notification as read'})

        # Mark as read
        notification.is_read = True
        notification.save()

        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'success': False, 'error': 'Unable to mark notification as read'})

@role_required('3')
@login_required
def faculty_students(request):
    """Faculty views student list with flexible filtering similar to HOD"""
    try:
        faculty = request.user.facultyprofile
        faculty_branches = faculty.branches.all()
    except FacultyProfile.DoesNotExist:
        messages.error(request, "Faculty profile not found.")
        return redirect('faculty_home')

    if not faculty_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('faculty_home')

    # Get filter parameters
    selected_branch_id = request.GET.get('branch')
    selected_semester_id = request.GET.get('semester')
    selected_section_id = request.GET.get('section')
    selected_year_id = request.GET.get('year')
    fee_status_filter = request.GET.get('fee_status')
    search_query = request.GET.get('search', '').strip()
    show_all = request.GET.get('show_all', False)

    # Initialize context with all available options
    context = {
        'faculty_branches': faculty_branches,
        'semesters': [],
        'sections': [],
        'years': Year.objects.all(),
        'fee_status_choices': [
            ('pending', 'Pending'),
            ('partial', 'Partial'),
            ('confirmed', 'Confirmed'),
        ],
        'selected_branch_id': selected_branch_id,
        'selected_semester_id': selected_semester_id,
        'selected_section_id': selected_section_id,
        'selected_year_id': selected_year_id,
        'fee_status_filter': fee_status_filter,
        'search_query': search_query,
        'page_title': 'Student List',
    }

    # Load semesters based on selected branch
    if selected_branch_id:
        try:
            selected_branch = faculty_branches.get(id=selected_branch_id)
            context['selected_branch'] = selected_branch
            # Get semesters that faculty teaches in this branch
            context['semesters'] = Semester.objects.filter(
                subjectallocation__faculty=faculty,
                subjectallocation__branches__id=selected_branch_id
            ).distinct().order_by('number')
        except Branch.DoesNotExist:
            messages.error(request, "Selected branch not found or not assigned to you.")

    # Load sections based on selected branch and semester
    if selected_branch_id and selected_semester_id:
        try:
            selected_semester = Semester.objects.get(id=selected_semester_id)
            context['selected_semester'] = selected_semester
            # Get sections that faculty teaches for this branch and semester
            context['sections'] = Section.objects.filter(
                subjectallocation__faculty=faculty,
                subjectallocation__branches__id=selected_branch_id,
                subjectallocation__semester__id=selected_semester_id
            ).distinct().order_by('name')
        except Semester.DoesNotExist:
            messages.error(request, "Selected semester not found.")

    # Build dynamic filters for students - flexible filtering
    if any([selected_branch_id, selected_semester_id, selected_section_id, selected_year_id, fee_status_filter, search_query]) or show_all:
        # Get sections assigned to the faculty via SubjectAllocation
        assigned_section_ids = Section.objects.filter(
            subjectallocation__faculty=faculty
        ).values_list('id', flat=True).distinct()

        # Build student filters - start with faculty's assigned sections
        student_filters = {
            'section__id__in': assigned_section_ids,
            'branch__in': faculty_branches
        }

        if selected_branch_id:
            student_filters['branch_id'] = selected_branch_id
        if selected_semester_id:
            student_filters['current_semester_id'] = selected_semester_id
        if selected_section_id:
            student_filters['section_id'] = selected_section_id
        if selected_year_id:
            student_filters['current_year_id'] = selected_year_id

        # Get students based on filters
        students = StudentProfile.objects.filter(**student_filters).select_related(
            'user', 'section', 'current_semester', 'current_year', 'branch'
        ).order_by('branch__name', 'current_year__number', 'current_semester__number', 'section__name', 'roll_number')

        # Apply search filter
        if search_query:
            students = students.filter(
                Q(user__first_name__icontains=search_query) |
                Q(user__last_name__icontains=search_query) |
                Q(user__email__icontains=search_query) |
                Q(roll_number__icontains=search_query)
            )

        # Prepare student data with fee status
        student_data = []
        for student in students:
            latest_fee = student.studentfeerecord_set.order_by('-payment_date').first()
            fee_status = latest_fee.status if latest_fee else 'pending'
            fee_status_display = latest_fee.get_status_display() if latest_fee else 'Pending'

            # Apply fee status filter
            if fee_status_filter and fee_status != fee_status_filter:
                continue

            student_data.append({
                'student': student,
                'fee_status': fee_status_display,
                'fee_status_code': fee_status,
            })

        context['student_data'] = student_data
        context['total_students'] = len(student_data)

        # Add summary statistics
        if student_data:
            fee_summary = {}
            for data in student_data:
                status = data['fee_status_code']
                fee_summary[status] = fee_summary.get(status, 0) + 1
            context['fee_summary'] = fee_summary

        # Set selected objects for display
        if selected_section_id:
            try:
                context['selected_section'] = Section.objects.get(id=selected_section_id)
            except Section.DoesNotExist:
                pass
        if selected_year_id:
            try:
                context['selected_year'] = Year.objects.get(id=selected_year_id)
            except Year.DoesNotExist:
                pass

    else:
        # No filters applied - show welcome message
        context['student_data'] = []
        context['total_students'] = 0

    return render(request, 'faculty/students.html', context)

@csrf_exempt
@login_required
@role_required('3')
def get_students_and_result(request):
    section_id = request.GET.get('section_id')
    students = list(StudentProfile.objects.filter(section_id=section_id).select_related('user').values(
        'id', 'user__first_name', 'user__last_name'
    ))
    return JsonResponse({'students': students})

@role_required('3')
@login_required
def ajax_search_students(request):
    section_id = request.GET.get('section')
    query = request.GET.get('q', '').strip()

    students = StudentProfile.objects.select_related('user', 'section', 'branch')

    if section_id:
        students = students.filter(section_id=section_id)

    if query:
        students = students.filter(
            Q(user__first_name__icontains=query) |
            Q(user__last_name__icontains=query) |
            Q(user__email__icontains=query)
        )

    html = render_to_string("faculty/_student_table.html", {"students": students})
    return JsonResponse({'html': html})


@role_required('3')
@login_required
def ajax_get_sections_by_semester(request):
    """Get sections for faculty based on selected semester"""
    semester_id = request.GET.get('semester_id')

    if not semester_id:
        return JsonResponse({'sections': []})

    try:
        faculty = request.user.facultyprofile

        # Get sections that faculty teaches in this semester
        sections = Section.objects.filter(
            subjectallocation__faculty=faculty,
            subjectallocation__semester_id=semester_id
        ).distinct().values('id', 'name').order_by('name')

        return JsonResponse({'sections': list(sections)})
    except Exception as e:
        return JsonResponse({'sections': []})


@role_required('3')
@login_required
def ajax_get_subjects_by_section(request):
    """Get subjects for faculty based on selected semester and section"""
    semester_id = request.GET.get('semester_id')
    section_id = request.GET.get('section_id')

    if not semester_id or not section_id:
        return JsonResponse({'subjects': []})

    try:
        faculty = request.user.facultyprofile

        # Get subjects that faculty teaches for this semester and section
        subjects = Subject.objects.filter(
            allocations__faculty=faculty,
            allocations__semester_id=semester_id,
            allocations__sections__id=section_id
        ).distinct().values('id', 'name', 'code').order_by('name')

        return JsonResponse({'subjects': list(subjects)})
    except Exception as e:
        return JsonResponse({'subjects': []})



def _validate_allocation(faculty, subject, branch, semester, section):
    """Check if faculty is allocated to the subject, branch, semester, section"""
    return SubjectAllocation.objects.filter(
        faculty=faculty,
        subject=subject,
        branches=branch,
        semester=semester,
        sections=section
    ).exists()





@role_required('3')
@login_required
def add_results_simple(request):
    """View for adding student results"""
    faculty = request.user.facultyprofile

    # Initialize context
    context = {
        'filter_form': ResultFilterForm(faculty=faculty),
        'show_results_form': False,
        'page_title': 'Add Results',
    }

    # Process filter form
    if request.GET.get('subject'):
        filter_form = ResultFilterForm(faculty, request.GET)
        if filter_form.is_valid():
            # Get selected values
            selected_subject = filter_form.cleaned_data['subject']
            selected_branch = filter_form.cleaned_data['branch']
            selected_semester = filter_form.cleaned_data['semester']
            selected_section = filter_form.cleaned_data['section']
            selected_result_type = filter_form.cleaned_data['result_type']

            # Validate faculty allocation
            if not _validate_allocation(faculty, selected_subject, selected_branch, selected_semester, selected_section):
                messages.error(request, "You are not allocated to this subject, branch, semester, or section.")
                return redirect('add_results_simple')

            # Get students for the selected class
            students = StudentProfile.objects.filter(
                branch=selected_branch,
                current_semester=selected_semester,
                section=selected_section
            ).order_by('roll_number')

            # Get existing results and prepare student data
            results = Result.objects.filter(
                subject=selected_subject,
                branch=selected_branch,
                semester=selected_semester,
                section=selected_section,
                result_type=selected_result_type,
                student__in=students
            )

            # Create a dictionary of results by student ID
            results_dict = {}
            for result in results:
                results_dict[result.student.id] = result

            # Prepare student data with existing results
            students_data = []
            for student in students:
                result = results_dict.get(student.id)
                student_data = {
                    'student': student,
                    'marks': result.marks_obtained if result else '',
                    'remarks': result.remarks if result else ''
                }
                students_data.append(student_data)

            # Update context
            context.update({
                'filter_form': filter_form,
                'show_results_form': True,
                'selected_subject': selected_subject,
                'selected_branch': selected_branch,
                'selected_semester': selected_semester,
                'selected_section': selected_section,
                'selected_result_type': selected_result_type,
                'max_marks': selected_result_type.max_marks,
                'students_data': students_data
            })

    # Process form submission
    if request.method == 'POST' and request.POST.get('save_results') == '1':
        try:
            # Get selected values from POST data
            subject_id = request.POST.get('subject')
            branch_id = request.POST.get('branch')
            semester_id = request.POST.get('semester')
            section_id = request.POST.get('section')
            result_type_id = request.POST.get('result_type')

            # Validate IDs
            if not all([subject_id, branch_id, semester_id, section_id, result_type_id]):
                messages.error(request, "Missing required parameters.")
                return redirect('add_results_simple')

            # Get objects
            subject = get_object_or_404(Subject, id=subject_id)
            branch = get_object_or_404(Branch, id=branch_id)
            semester = get_object_or_404(Semester, id=semester_id)
            section = get_object_or_404(Section, id=section_id)
            result_type = get_object_or_404(ResultType, id=result_type_id)

            # Validate faculty allocation
            if not _validate_allocation(faculty, subject, branch, semester, section):
                messages.error(request, "You are not allocated to this subject, branch, semester, or section.")
                return redirect('add_results_simple')

            # Process student results
            saved_count = 0
            with transaction.atomic():
                # Find all student_id fields in POST data
                for key, value in request.POST.items():
                    if key.startswith('student_id_'):
                        student_id = value
                        marks_key = f'marks_{student_id}'
                        remarks_key = f'remarks_{student_id}'

                        # Get marks and remarks
                        marks_str = request.POST.get(marks_key, '').strip()
                        remarks = request.POST.get(remarks_key, '').strip()

                        # Skip if both marks and remarks are empty
                        if not marks_str and not remarks:
                            continue

                        # Convert marks to Decimal if provided
                        marks = None
                        if marks_str:
                            try:
                                marks = Decimal(marks_str)
                                # Validate marks
                                if marks < 0:
                                    messages.warning(request, f"Marks for student ID {student_id} cannot be negative. Skipping.")
                                    continue
                                if marks > result_type.max_marks:
                                    messages.warning(request, f"Marks for student ID {student_id} cannot exceed {result_type.max_marks}. Skipping.")
                                    continue
                            except (ValueError, TypeError, InvalidOperation):
                                messages.warning(request, f"Invalid marks value for student ID {student_id}. Skipping.")
                                continue

                        # Get student
                        try:
                            student = StudentProfile.objects.get(id=student_id)
                        except StudentProfile.DoesNotExist:
                            messages.warning(request, f"Student with ID {student_id} not found. Skipping.")
                            continue

                        # Create or update result
                        result, created = Result.objects.update_or_create(
                            subject=subject,
                            branch=branch,
                            semester=semester,
                            section=section,
                            student=student,
                            result_type=result_type,
                            defaults={
                                'marks_obtained': marks,
                                'remarks': remarks,
                                'uploaded_by': faculty
                            }
                        )

                        saved_count += 1

            if saved_count > 0:
                messages.success(request, f"Results saved for {saved_count} student(s) successfully.")
            else:
                messages.warning(request, "No results were saved. Please enter marks or remarks for at least one student.")

            # Redirect to the same page with filter parameters
            return redirect(f"{reverse('add_results_simple')}?subject={subject.id}&branch={branch.id}&semester={semester.id}&section={section.id}&result_type={result_type.id}")

        except Exception as e:
            messages.error(request, "Error saving results. Please try again.")

    return render(request, 'faculty/add_results.html', context)

@role_required('3')
@login_required
def view_results(request):
    """View student results with edit action"""
    faculty = request.user.facultyprofile

    # Initialize context
    context = {
        'filter_form': ResultFilterForm(faculty=faculty),
        'show_results': False,
        'page_title': 'View Results',
    }

    # Handle single result edit form submission
    if request.method == 'POST' and request.POST.get('edit_single_result') == '1':
        try:
            # Get form data
            subject_id = request.POST.get('subject')
            branch_id = request.POST.get('branch')
            semester_id = request.POST.get('semester')
            section_id = request.POST.get('section')
            result_type_id = request.POST.get('result_type')
            student_id = request.POST.get('student_id')
            marks_str = request.POST.get('marks', '').strip()
            remarks = request.POST.get('remarks', '').strip()

            # Validate required fields
            if not all([subject_id, branch_id, semester_id, section_id, result_type_id, student_id]):
                messages.error(request, "Missing required parameters.")
                return redirect('view_results')

            # Get objects
            subject = get_object_or_404(Subject, id=subject_id)
            branch = get_object_or_404(Branch, id=branch_id)
            semester = get_object_or_404(Semester, id=semester_id)
            section = get_object_or_404(Section, id=section_id)
            result_type = get_object_or_404(ResultType, id=result_type_id)
            student = get_object_or_404(StudentProfile, id=student_id)

            # Validate faculty allocation
            if not _validate_allocation(faculty, subject, branch, semester, section):
                messages.error(request, "You are not allocated to this subject, branch, semester, or section.")
                return redirect('view_results')

            # Convert marks to Decimal if provided
            marks = None
            if marks_str:
                try:
                    marks = Decimal(marks_str)
                    # Validate marks
                    if marks < 0:
                        messages.error(request, "Marks cannot be negative.")
                        return redirect(f"{reverse('view_results')}?subject={subject.id}&branch={branch.id}&semester={semester.id}&section={section.id}&result_type={result_type.id}")
                    if marks > result_type.max_marks:
                        messages.error(request, f"Marks cannot exceed {result_type.max_marks}.")
                        return redirect(f"{reverse('view_results')}?subject={subject.id}&branch={branch.id}&semester={semester.id}&section={section.id}&result_type={result_type.id}")
                except (ValueError, TypeError, InvalidOperation):
                    messages.error(request, "Invalid marks value.")
                    return redirect(f"{reverse('view_results')}?subject={subject.id}&branch={branch.id}&semester={semester.id}&section={section.id}&result_type={result_type.id}")

            # Update or create result
            result, created = Result.objects.update_or_create(
                subject=subject,
                branch=branch,
                semester=semester,
                section=section,
                student=student,
                result_type=result_type,
                defaults={
                    'marks_obtained': marks,
                    'remarks': remarks,
                    'uploaded_by': faculty
                }
            )

            action = "Created" if created else "Updated"
            messages.success(request, f"Result for {student.user.get_full_name()} has been {action.lower()} successfully.")

            # Redirect back to the results page with the same filters
            return redirect(f"{reverse('view_results')}?subject={subject.id}&branch={branch.id}&semester={semester.id}&section={section.id}&result_type={result_type.id}")

        except Exception as e:
            messages.error(request, "Error updating result. Please try again.")
            return redirect('view_results')

    # Process filter form
    if request.GET.get('subject'):
        filter_form = ResultFilterForm(faculty, request.GET)
        if filter_form.is_valid():
            # Get selected values
            selected_subject = filter_form.cleaned_data['subject']
            selected_branch = filter_form.cleaned_data['branch']
            selected_semester = filter_form.cleaned_data['semester']
            selected_section = filter_form.cleaned_data['section']
            selected_result_type = filter_form.cleaned_data['result_type']

            # Validate faculty allocation
            if not _validate_allocation(faculty, selected_subject, selected_branch, selected_semester, selected_section):
                messages.error(request, "You are not allocated to this subject, branch, semester, or section.")
                return redirect('view_results')

            # Get students for the selected class
            students = StudentProfile.objects.filter(
                branch=selected_branch,
                current_semester=selected_semester,
                section=selected_section
            ).order_by('roll_number')

            # Get results for the selected criteria
            results = Result.objects.filter(
                subject=selected_subject,
                branch=selected_branch,
                semester=selected_semester,
                section=selected_section,
                result_type=selected_result_type,
                student__in=students
            ).select_related('student', 'student__user').order_by('student__roll_number')

            # Update context
            context.update({
                'filter_form': filter_form,
                'show_results': True,
                'selected_subject': selected_subject,
                'selected_branch': selected_branch,
                'selected_semester': selected_semester,
                'selected_section': selected_section,
                'selected_result_type': selected_result_type,
                'results': results
            })

    return render(request, 'faculty/view_results.html', context)


# AJAX endpoints for faculty attendance view filtering
@role_required('3')
@login_required
def ajax_get_faculty_attendance_semesters(request):
    """Get semesters for faculty attendance view based on selected branch"""
    branch_id = request.GET.get('branch_id')

    if not branch_id:
        return JsonResponse({'semesters': []})

    try:
        faculty = request.user.facultyprofile

        # Verify faculty has access to this branch
        if not faculty.branches.filter(id=branch_id).exists():
            return JsonResponse({'semesters': [], 'error': 'Access denied'})

        # Get semesters that faculty teaches in this branch
        semesters = Semester.objects.filter(
            subjectallocation__faculty=faculty,
            subjectallocation__branches__id=branch_id
        ).distinct().values('id', 'number').order_by('number')

        return JsonResponse({'semesters': list(semesters)})
    except Exception as e:
        return JsonResponse({'semesters': []})

@role_required('3')
@login_required
def ajax_get_faculty_attendance_sections(request):
    """Get sections for faculty attendance view based on selected branch and semester"""
    branch_id = request.GET.get('branch_id')
    semester_id = request.GET.get('semester_id')

    if not branch_id or not semester_id:
        return JsonResponse({'sections': []})

    try:
        faculty = request.user.facultyprofile

        # Verify faculty has access to this branch
        if not faculty.branches.filter(id=branch_id).exists():
            return JsonResponse({'sections': []})

        # Get sections that faculty teaches for this branch and semester
        sections = Section.objects.filter(
            subjectallocation__faculty=faculty,
            subjectallocation__branches__id=branch_id,
            subjectallocation__semester_id=semester_id
        ).distinct().values('id', 'name').order_by('name')

        return JsonResponse({'sections': list(sections)})
    except Exception as e:
        return JsonResponse({'sections': []})

@role_required('3')
@login_required
def ajax_get_faculty_attendance_subjects(request):
    """Get subjects for faculty attendance view based on selected branch, semester, and section"""
    branch_id = request.GET.get('branch_id')
    semester_id = request.GET.get('semester_id')
    section_id = request.GET.get('section_id')

    if not branch_id or not semester_id or not section_id:
        return JsonResponse({'subjects': []})

    try:
        faculty = request.user.facultyprofile

        # Verify faculty has access to this branch
        if not faculty.branches.filter(id=branch_id).exists():
            return JsonResponse({'subjects': []})

        # Get subjects that faculty teaches for this branch, semester, and section
        subjects = Subject.objects.filter(
            allocations__faculty=faculty,
            allocations__branches__id=branch_id,
            allocations__semester_id=semester_id,
            allocations__sections__id=section_id
        ).distinct().values('id', 'name', 'code').order_by('name')

        return JsonResponse({'subjects': list(subjects)})
    except Exception as e:
        return JsonResponse({'subjects': []})


# AJAX endpoints for results functionality
@role_required('3')
@login_required
def ajax_get_results_semesters(request):
    """Get semesters for results based on selected branch"""
    branch_id = request.GET.get('branch_id')

    if not branch_id:
        return JsonResponse({'semesters': []})

    try:
        faculty = request.user.facultyprofile

        # Get semesters that faculty teaches in this branch
        semesters = Semester.objects.filter(
            subjectallocation__faculty=faculty,
            subjectallocation__branches__id=branch_id
        ).distinct().values('id', 'number').order_by('number')

        return JsonResponse({'semesters': list(semesters)})
    except Exception as e:
        return JsonResponse({'semesters': []})


@role_required('3')
@login_required
def ajax_get_results_sections(request):
    """Get sections for results based on selected branch and semester"""
    branch_id = request.GET.get('branch_id')
    semester_id = request.GET.get('semester_id')

    if not branch_id or not semester_id:
        return JsonResponse({'sections': []})

    try:
        faculty = request.user.facultyprofile

        # Get sections that faculty teaches for this branch and semester
        sections = Section.objects.filter(
            subjectallocation__faculty=faculty,
            subjectallocation__branches__id=branch_id,
            subjectallocation__semester_id=semester_id
        ).distinct().values('id', 'name').order_by('name')

        return JsonResponse({'sections': list(sections)})
    except Exception as e:
        return JsonResponse({'sections': []})


@role_required('3')
@login_required
def ajax_get_results_subjects(request):
    """Get subjects for results based on selected branch, semester, and section"""
    branch_id = request.GET.get('branch_id')
    semester_id = request.GET.get('semester_id')
    section_id = request.GET.get('section_id')

    if not branch_id or not semester_id or not section_id:
        return JsonResponse({'subjects': []})

    try:
        faculty = request.user.facultyprofile

        # Get subjects that faculty teaches for this branch, semester, and section
        subjects = Subject.objects.filter(
            allocations__faculty=faculty,
            allocations__branches__id=branch_id,
            allocations__semester_id=semester_id,
            allocations__sections__id=section_id
        ).distinct().values('id', 'name', 'code').order_by('name')

        return JsonResponse({'subjects': list(subjects)})
    except Exception as e:
        return JsonResponse({'subjects': []})


# AJAX endpoints for faculty student list filtering
@role_required('3')
@login_required
def ajax_get_faculty_student_semesters(request):
    """Get semesters for faculty student list based on selected branch"""
    branch_id = request.GET.get('branch_id')

    if not branch_id:
        return JsonResponse({'semesters': []})

    try:
        faculty = request.user.facultyprofile

        # Verify faculty has access to this branch
        if not faculty.branches.filter(id=branch_id).exists():
            return JsonResponse({'semesters': [], 'error': 'Access denied'})

        # Get semesters that faculty teaches in this branch
        semesters = Semester.objects.filter(
            subjectallocation__faculty=faculty,
            subjectallocation__branches__id=branch_id
        ).distinct().values('id', 'number').order_by('number')

        return JsonResponse({'semesters': list(semesters)})
    except Exception as e:
        return JsonResponse({'semesters': []})


@role_required('3')
@login_required
def ajax_get_faculty_student_sections(request):
    """Get sections for faculty student list based on selected branch and semester"""
    branch_id = request.GET.get('branch_id')
    semester_id = request.GET.get('semester_id')

    if not branch_id or not semester_id:
        return JsonResponse({'sections': []})

    try:
        faculty = request.user.facultyprofile

        # Verify faculty has access to this branch
        if not faculty.branches.filter(id=branch_id).exists():
            return JsonResponse({'sections': []})

        # Get sections that faculty teaches for this branch and semester
        sections = Section.objects.filter(
            subjectallocation__faculty=faculty,
            subjectallocation__branches__id=branch_id,
            subjectallocation__semester_id=semester_id
        ).distinct().values('id', 'name').order_by('name')

        return JsonResponse({'sections': list(sections)})
    except Exception as e:
        return JsonResponse({'sections': []})