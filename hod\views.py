import hashlib
import logging
from collections import defaultdict
from datetime import date
from django.contrib import messages
from django.contrib.auth import update_session_auth_hash
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import PasswordChangeForm
from django.db import IntegrityError
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.views.decorators.csrf import csrf_exempt
from django.db.models import F, Q

logger = logging.getLogger(__name__)

from academic.models import Branch, Section, Semester, Year
from attendance.forms import DateRangeForm
from attendance.models import LectureSession, StudentAttendance
from faculty.models import FacultyProfile
from feedback.models import Feedback
from fees.models import StudentFeeRecord
from hod.models import HODProfile
from hod.forms import HODUserForm, HODSectionForm
from notifications.forms import NotificationForm
from notifications.models import Notification
from student.models import StudentProfile
from timetable.forms import SubjectAllocationForm, SubjectForm, TimetableForm
from timetable.models import Subject, SubjectAllocation, Timetable
from users.views import role_required

def color_from_code(code):
    hash_code = int(hashlib.md5(code.encode()).hexdigest(), 16)
    r = 200 + (hash_code % 55)
    g = 200 + ((hash_code // 2) % 55)
    b = 200 + ((hash_code // 3) % 55)
    return f'rgb({r},{g},{b})'


@role_required('2')
@login_required
def hod_home(request):
    """HOD dashboard with key metrics and quick links"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('login')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('login')

    # Metrics across all assigned branches
    total_students = StudentProfile.objects.filter(branch__in=hod_branches).count()
    total_faculty = FacultyProfile.objects.filter(branches__in=hod_branches).distinct().count()
    pending_fees = StudentFeeRecord.objects.filter(
        student__branch__in=hod_branches, status='pending'
    ).count()
    confirmed_fees = StudentFeeRecord.objects.filter(
        student__branch__in=hod_branches, status='confirmed'
    ).count()
    partial_fees = StudentFeeRecord.objects.filter(
        student__branch__in=hod_branches, status='partial'
    ).count()
    today = date.today()
    today_classes = Timetable.objects.filter(
        branch__in=hod_branches, day=today.strftime('%A')
    ).order_by('period_time')[:5]

    # Recent feedback from all assigned branches
    recent_feedback = Feedback.objects.filter(
        student__branch__in=hod_branches
    ).order_by('-submitted_at')[:5]

    context = {
        'hod_branches': hod_branches,
        'total_students': total_students,
        'total_faculty': total_faculty,
        'pending_fees': pending_fees,
        'confirmed_fees': confirmed_fees,
        'partial_fees': partial_fees,
        'today_classes': today_classes,
        'recent_feedback': recent_feedback,
        'page_title': 'HOD Dashboard',
    }
    return render(request, "hod/home.html", context)


@role_required('2')
@login_required
def manage_sections(request):
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    if request.method == 'POST':
        form = HODSectionForm(hod_profile=hod_profile, data=request.POST)
        if form.is_valid():
            section = form.save()
            messages.success(request, f"Section '{section.name}' added successfully for {section.branch.name}.")
            return redirect('manage_sections')
        else:
            messages.error(request, f"Error adding section: {form.errors.as_text()}")
    else:
        form = HODSectionForm(hod_profile=hod_profile)

    # Filter sections by HOD's assigned branches
    sections = Section.objects.filter(branch__in=hod_branches).select_related('branch', 'year', 'semester').order_by('branch__name', 'year__number', 'semester__number', 'name')

    # Create a form for each section for the edit modals
    section_forms = [(section, HODSectionForm(hod_profile=hod_profile, instance=section)) for section in sections]

    context = {
        'form': form,
        'section_forms': section_forms,
        'sections': sections,
        'hod_branches': hod_branches,
        'page_title': 'Manage Sections'
    }
    return render(request, 'hod/manage_sections.html', context)

@role_required('2')
@login_required
def edit_section(request, section_id):
    """Edit an existing Section"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    # Ensure the section belongs to one of HOD's branches
    section = get_object_or_404(Section, id=section_id, branch__in=hod_branches)

    if request.method == 'POST':
        form = HODSectionForm(hod_profile=hod_profile, data=request.POST, instance=section)
        if form.is_valid():
            updated_section = form.save()
            messages.success(request, f"Section '{updated_section.name}' updated successfully.")
            return redirect('manage_sections')
        else:
            messages.error(request, f"Error updating section: {form.errors.as_text()}")
    else:
        form = HODSectionForm(hod_profile=hod_profile, instance=section)

    # Filter sections by HOD's assigned branches
    sections = Section.objects.filter(branch__in=hod_branches).select_related('branch', 'year', 'semester').order_by('branch__name', 'year__number', 'semester__number', 'name')

    # Create a form for each section for the edit modals
    section_forms = [(sec, HODSectionForm(hod_profile=hod_profile, instance=sec)) for sec in sections]

    context = {
        'form': form,
        'section_forms': section_forms,
        'sections': sections,
        'hod_branches': hod_branches,
        'page_title': 'Manage Sections'
    }
    return render(request, 'hod/manage_sections.html', context)

@role_required('2')
@login_required
def delete_section(request, section_id):
    """Delete an existing Section"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    # Ensure the section belongs to one of HOD's branches
    section = get_object_or_404(Section, id=section_id, branch__in=hod_branches)

    if request.method == 'POST':
        try:
            section_name = section.name
            branch_name = section.branch.name
            section.delete()
            messages.success(request, f"Section '{section_name}' from {branch_name} deleted successfully.")
        except Exception as e:
            messages.error(request, f"Error deleting section: {str(e)}")
        return redirect('manage_sections')

@role_required('2')
@login_required
def fee_management(request):
    """HOD views fee records with flexible filtering"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Get filter parameters
    selected_branch_id = request.GET.get('branch')
    selected_semester_id = request.GET.get('semester')
    selected_section_id = request.GET.get('section')
    selected_year_id = request.GET.get('year')
    selected_status = request.GET.get('status')
    selected_payment_type = request.GET.get('payment_type')
    search_query = request.GET.get('search', '').strip()
    show_all = request.GET.get('show_all', False)

    # Initialize context with all available options
    context = {
        'hod_branches': hod_branches,
        'semesters': [],
        'sections': [],
        'years': Year.objects.all(),
        'status_choices': StudentFeeRecord.STATUS_CHOICES,
        'payment_type_choices': StudentFeeRecord.PAYMENT_TYPE_CHOICES,
        'selected_branch_id': selected_branch_id,
        'selected_semester_id': selected_semester_id,
        'selected_section_id': selected_section_id,
        'selected_year_id': selected_year_id,
        'selected_status': selected_status,
        'selected_payment_type': selected_payment_type,
        'search_query': search_query,
        'page_title': 'Fee Management',
    }

    # Load semesters based on selected branch
    if selected_branch_id:
        try:
            selected_branch = hod_branches.get(id=selected_branch_id)
            context['selected_branch'] = selected_branch
            # Get semesters that have students with fee records for this branch
            context['semesters'] = Semester.objects.filter(
                studentprofile__studentfeerecord__student__branch=selected_branch
            ).distinct().order_by('number')
        except Branch.DoesNotExist:
            messages.error(request, "Selected branch not found or not assigned to you.")

    # Load sections based on selected branch and semester
    if selected_branch_id and selected_semester_id:
        try:
            selected_semester = Semester.objects.get(id=selected_semester_id)
            context['selected_semester'] = selected_semester
            # Get sections for this branch and semester
            context['sections'] = Section.objects.filter(
                branch_id=selected_branch_id,
                semester=selected_semester
            ).order_by('name')
        except Semester.DoesNotExist:
            messages.error(request, "Selected semester not found.")

    # Build dynamic filters for fee records - flexible filtering
    if any([selected_branch_id, selected_semester_id, selected_section_id, selected_year_id, selected_status, selected_payment_type, search_query]) or show_all:
        # Build fee record filters
        fee_filters = {'student__branch__in': hod_branches}

        if selected_branch_id:
            fee_filters['student__branch_id'] = selected_branch_id
        if selected_semester_id:
            fee_filters['student__current_semester_id'] = selected_semester_id
        if selected_section_id:
            fee_filters['student__section_id'] = selected_section_id
        if selected_year_id:
            fee_filters['academic_year_id'] = selected_year_id
        if selected_status:
            fee_filters['status'] = selected_status
        if selected_payment_type:
            fee_filters['payment_type'] = selected_payment_type

        # Get fee records based on filters
        fee_records = StudentFeeRecord.objects.filter(**fee_filters).select_related(
            'student__user', 'student__branch', 'student__section', 'student__current_semester',
            'student__current_year', 'academic_year'
        ).order_by('-payment_date', 'student__roll_number')

        # Apply search filter
        if search_query:
            fee_records = fee_records.filter(
                Q(student__user__first_name__icontains=search_query) |
                Q(student__user__last_name__icontains=search_query) |
                Q(student__user__email__icontains=search_query) |
                Q(student__roll_number__icontains=search_query) |
                Q(remarks__icontains=search_query)
            )

        context['fee_records'] = fee_records
        context['total_records'] = fee_records.count()

        # Add summary statistics
        if fee_records.exists():
            status_summary = {}
            payment_type_summary = {}
            total_amount_sum = 0
            paid_amount_sum = 0

            for record in fee_records:
                # Status summary
                status = record.status
                status_summary[status] = status_summary.get(status, 0) + 1

                # Payment type summary
                payment_type = record.payment_type
                payment_type_summary[payment_type] = payment_type_summary.get(payment_type, 0) + 1

                # Amount summary
                total_amount_sum += record.total_amount or 0
                paid_amount_sum += record.paid_amount or 0

            context['status_summary'] = status_summary
            context['payment_type_summary'] = payment_type_summary
            context['total_amount_sum'] = total_amount_sum
            context['paid_amount_sum'] = paid_amount_sum
            context['remaining_amount_sum'] = total_amount_sum - paid_amount_sum

            # Calculate collection percentage
            if total_amount_sum > 0:
                context['collection_percentage'] = (paid_amount_sum * 100) / total_amount_sum
            else:
                context['collection_percentage'] = 0

        # Set selected objects for display
        if selected_section_id:
            try:
                context['selected_section'] = Section.objects.get(id=selected_section_id)
            except Section.DoesNotExist:
                pass
        if selected_year_id:
            try:
                context['selected_year'] = Year.objects.get(id=selected_year_id)
            except Year.DoesNotExist:
                pass

    else:
        # No filters applied - show welcome message
        context['fee_records'] = StudentFeeRecord.objects.none()
        context['total_records'] = 0

    return render(request, 'hod/fee_management.html', context)

@role_required('2')
@login_required
def hod_notifications(request):
    """HOD creates and views notifications"""
    hod = request.user
    form = NotificationForm(data=request.POST or None, files=request.FILES or None)
    notifications = Notification.objects.filter(sender=hod).order_by('-created_at')

    if request.method == 'POST' and form.is_valid():
        try:
            notification = form.save(commit=False)
            notification.sender = hod
            notification.save()
            messages.success(request, "Notification sent successfully.")
            return redirect('hod_notifications')
        except Exception as e:
            messages.error(request, f"Error sending notification: {str(e)}")

    context = {
        'form': form,
        'notifications': notifications,
        'page_title': 'Manage Notifications',
    }
    return render(request, 'hod/notifications.html', context)

@role_required('2')
@login_required
def views_attendance(request):
    """View attendance with hierarchical filtering: Branch → Semester → Section → Subject → Month"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Get filter parameters
    selected_branch_id = request.GET.get('branch')
    selected_semester_id = request.GET.get('semester')
    selected_section_id = request.GET.get('section')
    selected_subject_id = request.GET.get('subject')
    selected_start_date = request.GET.get('start_date')
    selected_end_date = request.GET.get('end_date')

    # Initialize date range form
    date_range_form = DateRangeForm(initial={
        'start_date': selected_start_date,
        'end_date': selected_end_date
    })

    context = {
        'hod_branches': hod_branches,
        'semesters': [],
        'sections': [],
        'subjects': [],
        'sessions': [],
        'students': [],
        'date_range_form': date_range_form,
        'page_title': 'View Attendance',
        'selected_branch_id': selected_branch_id,
        'selected_semester_id': selected_semester_id,
        'selected_section_id': selected_section_id,
        'selected_subject_id': selected_subject_id,
        'selected_start_date': selected_start_date,
        'selected_end_date': selected_end_date,
    }

    # Load semesters based on selected branch
    if selected_branch_id:
        try:
            selected_branch = hod_branches.get(id=selected_branch_id)
            context['selected_branch'] = selected_branch
            # Get semesters that have subject allocations for this branch
            context['semesters'] = Semester.objects.filter(
                subjectallocation__branches=selected_branch
            ).distinct().order_by('number')
        except Branch.DoesNotExist:
            messages.error(request, "Selected branch not found or not assigned to you.")

    # Load sections based on selected branch and semester
    if selected_branch_id and selected_semester_id:
        try:
            selected_semester = Semester.objects.get(id=selected_semester_id)
            context['selected_semester'] = selected_semester
            # Get sections for this branch and semester
            context['sections'] = Section.objects.filter(
                branch_id=selected_branch_id,
                semester=selected_semester
            ).order_by('name')
        except Semester.DoesNotExist:
            messages.error(request, "Selected semester not found.")

    # Load subjects based on selected branch, semester, and section
    if selected_branch_id and selected_semester_id and selected_section_id:
        try:
            selected_section = Section.objects.get(id=selected_section_id)
            context['selected_section'] = selected_section
            # Get subjects allocated to this branch, semester, and section
            subject_allocations = SubjectAllocation.objects.filter(
                branches__id=selected_branch_id,
                semester_id=selected_semester_id,
                sections=selected_section
            ).select_related('subject')
            context['subjects'] = [allocation.subject for allocation in subject_allocations]
            context['subject_allocations'] = subject_allocations
        except Section.DoesNotExist:
            messages.error(request, "Selected section not found.")

    # Load students and attendance data - flexible filtering
    if any([selected_branch_id, selected_semester_id, selected_section_id, selected_subject_id, selected_start_date, selected_end_date]):
        try:
            # Parse and validate date range if provided
            start_date = None
            end_date = None
            if selected_start_date and selected_end_date:
                from datetime import datetime
                start_date = datetime.strptime(selected_start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(selected_end_date, '%Y-%m-%d').date()

                # Validate date range
                if start_date > end_date:
                    messages.error(request, "Start date cannot be after end date.")
                    return render(request, "hod/view_attendance.html", context)

                if end_date > date.today():
                    messages.error(request, "End date cannot be in the future.")
                    return render(request, "hod/view_attendance.html", context)

                context['selected_start_date_obj'] = start_date
                context['selected_end_date_obj'] = end_date

            # Build dynamic filters for subject allocations
            allocation_filters = {}
            if selected_branch_id:
                allocation_filters['branches__id'] = selected_branch_id
            if selected_semester_id:
                allocation_filters['semester_id'] = selected_semester_id
            if selected_section_id:
                allocation_filters['sections__id'] = selected_section_id
            if selected_subject_id:
                allocation_filters['subject_id'] = selected_subject_id

            # Get subject allocations based on available filters
            subject_allocations = SubjectAllocation.objects.filter(**allocation_filters).select_related('subject', 'semester', 'faculty').prefetch_related('branches', 'sections')

            # Filter by HOD's branches
            subject_allocations = subject_allocations.filter(branches__in=hod_branches).distinct()

            if subject_allocations.exists():
                # Build dynamic filters for students
                student_filters = {}
                if selected_branch_id:
                    student_filters['branch_id'] = selected_branch_id
                if selected_section_id:
                    student_filters['section_id'] = selected_section_id
                else:
                    # If no specific section, get students from all sections in the allocations
                    allocation_sections = []
                    for allocation in subject_allocations:
                        allocation_sections.extend(allocation.sections.all())
                    if allocation_sections:
                        student_filters['section__in'] = allocation_sections

                # Get students based on filters
                students = StudentProfile.objects.filter(**student_filters).filter(branch__in=hod_branches).select_related('user', 'branch', 'section').order_by('branch__name', 'section__name', 'roll_number').distinct()
                context['students'] = students

                # Build dynamic filters for sessions
                session_filters = {'subject_allocation__in': subject_allocations}
                if start_date and end_date:
                    session_filters['date__range'] = [start_date, end_date]
                elif selected_start_date:  # Only start date provided
                    session_filters['date__gte'] = datetime.strptime(selected_start_date, '%Y-%m-%d').date()
                elif selected_end_date:  # Only end date provided
                    session_filters['date__lte'] = datetime.strptime(selected_end_date, '%Y-%m-%d').date()

                # Get lecture sessions based on filters
                sessions = LectureSession.objects.filter(**session_filters).select_related('subject_allocation__subject').order_by('date', 'subject_allocation__subject__name')
                context['sessions'] = sessions
                context['subject_allocations_data'] = subject_allocations

                # Fetch attendance records for all students and sessions
                if sessions.exists() and students.exists():
                    attendances = StudentAttendance.objects.filter(
                        session__in=sessions,
                        student__in=students
                    ).select_related('student', 'session')
                    attendance_map = {(att.student.id, att.session.id): att for att in attendances}
                    context['attendance_map'] = attendance_map

                    # Add summary information
                    context['total_sessions'] = sessions.count()
                    context['total_students'] = students.count()
                    context['total_allocations'] = subject_allocations.count()

                # Set selected objects for display
                if selected_subject_id:
                    try:
                        context['selected_subject'] = Subject.objects.get(id=selected_subject_id)
                    except Subject.DoesNotExist:
                        pass

            else:
                messages.info(request, "No subject allocations found for the selected criteria.")

        except ValueError as e:
            messages.error(request, "Invalid date format. Please use YYYY-MM-DD format.")

    return render(request, "hod/view_attendance.html", context)

# Assign Students to Sections
@role_required('2')
@login_required
def assign_sections(request):
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Filter students by HOD's branches
    students = StudentProfile.objects.filter(branch__in=hod_branches).select_related('user', 'branch', 'current_year', 'current_semester', 'section')

    if request.method == 'POST':
        student_ids = request.POST.getlist('student_ids')  # Handle multiple student IDs
        section_id = request.POST.get('section_id')
        try:
            # Ensure the section belongs to one of HOD's branches
            section = get_object_or_404(Section, id=section_id, branch__in=hod_branches)
            updated_count = 0
            for student_id in student_ids:
                student = get_object_or_404(StudentProfile, id=student_id, branch__in=hod_branches)
                student.section = section
                student.save()
                updated_count += 1
            messages.success(request, f"Section '{section.name}' assigned to {updated_count} student(s) successfully.")
        except Exception as e:
            messages.error(request, f"Error assigning section: {str(e)}")
        return redirect('assign_sections')

    # Handle filtering
    branch_id = request.GET.get('branch')
    year_id = request.GET.get('year')
    semester_id = request.GET.get('semester')

    # Filter sections by HOD's branches
    sections = Section.objects.filter(branch__in=hod_branches).select_related('branch', 'year', 'semester')

    if branch_id:
        students = students.filter(branch__id=branch_id)
        sections = sections.filter(branch__id=branch_id)
    if year_id:
        students = students.filter(current_year__id=year_id)
        sections = sections.filter(year__id=year_id)
    if semester_id:
        students = students.filter(current_semester__id=semester_id)
        sections = sections.filter(semester__id=semester_id)

    return render(request, "hod/assign_sections.html", {
        "students": students,
        "sections": sections,
        "branches": hod_branches,
        "years": Year.objects.all(),
        "semesters": Semester.objects.all(),
        "page_title": "Assign Section to Students"
    })

# Note: edit_section and delete_section functions are already defined above

@role_required('2')
@login_required
def manage_subjects(request):
    subjects = Subject.objects.all()

    if request.method == 'POST':
        form = SubjectForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, "Subject added successfully.")
            return redirect('manage_subjects')
        else:
            messages.error(request, f"Error adding subject: {form.errors.as_text()}")
    else:
        form = SubjectForm()

    # Create a form for each subject for edit modals
    subject_forms = [(subject, SubjectForm(instance=subject)) for subject in subjects]

    return render(request, "hod/manage_subjects.html", {
        "form": form,
        "subject_forms": subject_forms,
        "subjects": subjects,
        "page_title": "Manage Subjects"
    })

@role_required('2')
@login_required
def edit_subject(request, subject_id):
    subject = get_object_or_404(Subject, id=subject_id)
    if request.method == 'POST':
        form = SubjectForm(request.POST, instance=subject)
        if form.is_valid():
            form.save()
            messages.success(request, "Subject updated successfully.")
            return redirect('manage_subjects')
        else:
            messages.error(request, f"Error updating subject: {form.errors.as_text()}")
    else:
        form = SubjectForm(instance=subject)

    subjects = Subject.objects.all()
    subject_forms = [(subject, SubjectForm(instance=subject)) for subject in subjects]

    return render(request, "hod/manage_subjects.html", {
        "form": form,
        "subject_forms": subject_forms,
        "subjects": subjects,
        "page_title": "Manage Subjects"
    })

@role_required('2')
@login_required
def delete_subject(request, subject_id):
    try:
        subject = get_object_or_404(Subject, id=subject_id)
        subject.delete()
        messages.success(request, "Subject deleted successfully.")
    except Exception as e:
        messages.error(request, f"Error deleting subject: {str(e)}")
    return redirect('manage_subjects')

@role_required('2')
@login_required
def manage_subject_allocations(request):
    allocations = SubjectAllocation.objects.select_related('subject', 'year', 'semester', 'faculty').prefetch_related('branches', 'sections')

    if request.method == 'POST':
        form = SubjectAllocationForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, "Subject allocation added successfully.")
            return redirect('manage_subject_allocations')
        else:
            messages.error(request, f"Error adding subject allocation: {form.errors.as_text()}")
    else:
        form = SubjectAllocationForm()

    allocation_forms = [(allocation, SubjectAllocationForm(instance=allocation)) for allocation in allocations]

    return render(request, "hod/manage_subject_allocations.html", {
        "form": form,
        "allocation_forms": allocation_forms,
        "allocations": allocations,
        "page_title": "Manage Subject Allocations"
    })

@role_required('2')
@login_required
def edit_subject_allocation(request, allocation_id):
    allocation = get_object_or_404(SubjectAllocation, id=allocation_id)
    if request.method == 'POST':
        form = SubjectAllocationForm(request.POST, instance=allocation)
        if form.is_valid():
            form.save()
            messages.success(request, "Subject allocation updated successfully.")
            return redirect('manage_subject_allocations')
        else:
            messages.error(request, f"Error updating subject allocation: {form.errors.as_text()}")
    else:
        form = SubjectAllocationForm(instance=allocation)

    allocations = SubjectAllocation.objects.select_related('subject', 'year', 'semester', 'faculty').prefetch_related('branches', 'sections')
    allocation_forms = [(alloc, SubjectAllocationForm(instance=alloc)) for alloc in allocations]

    return render(request, "hod/manage_subject_allocations.html", {
        "form": form,
        "allocation_forms": allocation_forms,
        "allocations": allocations,
        "page_title": "Manage Subject Allocations"
    })

@role_required('2')
@login_required
def delete_subject_allocation(request, allocation_id):
    try:
        allocation = get_object_or_404(SubjectAllocation, id=allocation_id)
        allocation.delete()
        messages.success(request, "Subject allocation deleted successfully.")
    except Exception as e:
        messages.error(request, f"Error deleting subject allocation: {str(e)}")
    return redirect('manage_subject_allocations')

@role_required('2')
@login_required
def approve_promotions(request):
    # Get HOD's branches
    hod_profile = request.user.profile
    hod_branches = hod_profile.branches.all()

    # Filter students by HOD's branches
    students = StudentProfile.objects.filter(
        branch__in=hod_branches
    ).select_related('user', 'branch', 'current_year', 'current_semester', 'section')

    if request.method == 'POST':
        student_ids = request.POST.getlist('student_ids')
        try:
            updated_count = 0
            for student_id in student_ids:
                student = get_object_or_404(StudentProfile, id=student_id)
                # Check if student is eligible for promotion (not in final semester)
                max_semesters = student.branch.course_duration_years * 2
                if student.current_semester.number < max_semesters:
                    student.promote()
                    updated_count += 1
            messages.success(request, f"{updated_count} student(s) promoted successfully.")
        except Exception as e:
            messages.error(request, f"Error promoting students: {str(e)}")
        return redirect('approve_promotions')

    # Handle filtering
    branch_id = request.GET.get('branch')
    year_id = request.GET.get('year')
    semester_id = request.GET.get('semester')
    section_id = request.GET.get('section')

    if branch_id:
        students = students.filter(branch__id=branch_id)
    if year_id:
        students = students.filter(current_year__id=year_id)
    if semester_id:
        students = students.filter(current_semester__id=semester_id)
    if section_id:
        students = students.filter(section__id=section_id)

    # Filter out students in the final semester
    students = students.exclude(
        current_semester__number=F('branch__course_duration_years') * 2
    )

    return render(request, "hod/approve_promotions.html", {
        "students": students,
        "branches": hod_branches,
        "years": Year.objects.all(),
        "semesters": Semester.objects.all(),
        "sections": Section.objects.filter(branch__in=hod_branches),
        "selected_branch_id": branch_id,
        "selected_year_id": year_id,
        "selected_semester_id": semester_id,
        "selected_section_id": section_id,
        "page_title": "Approve Promotions"
    })

# Profile & Password Update
@role_required('2')
@login_required
def profile(request):
    user = request.user
    profile = user.profile

    # Initialize forms
    user_form = HODUserForm(user=user)
    password_form = PasswordChangeForm(user)

    if request.method == 'POST':
        if 'update_profile' in request.POST:
            user_form = HODUserForm(request.POST, request.FILES)

            if user_form.is_valid():
                # Update user information
                user.first_name = user_form.cleaned_data['first_name']
                user.last_name = user_form.cleaned_data['last_name']
                user.email = user_form.cleaned_data['email']

                # Handle photo upload
                if user_form.cleaned_data['photo']:
                    user.profile_pic = user_form.cleaned_data['photo']

                user.save()

                messages.success(request, "Profile updated successfully.")
                return redirect('hod_profile')
            else:
                messages.error(request, "Please correct the errors below.")

        elif 'change_password' in request.POST:
            password_form = PasswordChangeForm(user, request.POST)
            if password_form.is_valid():
                password_form.save()
                update_session_auth_hash(request, password_form.user)
                messages.success(request, "Password changed successfully.")
                return redirect('hod_profile')
            else:
                messages.error(request, "Please correct the password errors.")

    return render(request, 'hod/profile.html', {
        'user': user,
        'profile': profile,
        'user_form': user_form,
        'password_form': password_form,
        'page_title': 'My Profile'
    })

# Feedback List & Actions
@role_required('2')
@login_required
def hod_feedback_list(request):
    """HOD views feedback in their departments"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Fetch feedback from students in the HOD's assigned branches
    feedbacks = Feedback.objects.filter(
        student__branch__in=hod_branches
    ).order_by('-submitted_at')

    context = {
        'feedbacks': feedbacks,
        'hod_branches': hod_branches,
        'page_title': 'View Feedback',
    }
    return render(request, 'hod/feedback.html', context)


@role_required('2')
@login_required
def hod_timetable(request):
    """HOD views and manages timetable for their assigned branches"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Get branch filter from request, default to first assigned branch
    branch_filter = request.GET.get('branch', '')
    if branch_filter:
        try:
            selected_branch = hod_branches.get(id=branch_filter)
        except (Branch.DoesNotExist, ValueError):
            selected_branch = hod_branches.first()
    else:
        selected_branch = hod_branches.first()

    semester_filter = request.GET.get('semester', '')
    section_filter = request.GET.get('section', '')

    # Filter timetable based on selected branch
    timetable = Timetable.objects.filter(branch=selected_branch).order_by('semester', 'section', 'day', 'period_time')
    if semester_filter:
        timetable = timetable.filter(semester__number=semester_filter)
    if section_filter:
        timetable = timetable.filter(section__name=section_filter)

    # Get semesters and sections for the selected branch
    semesters = Semester.objects.filter(
        subjectallocation__branches=selected_branch
    ).distinct().order_by('number') if selected_branch else Semester.objects.none()

    sections = Section.objects.filter(
        branch=selected_branch
    ).order_by('name') if selected_branch else Section.objects.none()

    context = {
        'timetable': timetable,
        'hod_branches': hod_branches,
        'selected_branch': selected_branch,
        'branch_filter': branch_filter,
        'semester_filter': semester_filter,
        'section_filter': section_filter,
        'semesters': semesters,
        'sections': sections,
        'page_title': 'Timetable Management',
    }
    return render(request, 'hod/timetable.html', context)

@role_required('2')
@login_required
def hod_timetable_add(request):
    """HOD creates a new timetable entry for their assigned branches"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Get branch from query parameter or use first assigned branch
    branch_id = request.GET.get('branch', '')
    if branch_id:
        try:
            selected_branch = hod_branches.get(id=branch_id)
        except (Branch.DoesNotExist, ValueError):
            selected_branch = hod_branches.first()
    else:
        selected_branch = hod_branches.first()

    if request.method == 'POST':
        # Get branch from form data
        branch_id = request.POST.get('branch')
        try:
            selected_branch = hod_branches.get(id=branch_id)
        except (Branch.DoesNotExist, ValueError):
            selected_branch = hod_branches.first()

        form = TimetableForm(request.POST, branch=selected_branch)
        if form.is_valid():
            try:
                timetable = form.save(commit=False)
                timetable.branch = selected_branch
                timetable.save()
                form.save_m2m()  # Save ManyToMany fields (combined_sections)
                messages.success(request, "Timetable entry created successfully.")
                return redirect('hod_timetable')
            except IntegrityError as e:
                messages.error(request, f"Error: {str(e)}")
            except Exception as e:
                messages.error(request, f"An error occurred: {str(e)}")
    else:
        form = TimetableForm(branch=selected_branch)

    context = {
        'form': form,
        'hod_branches': hod_branches,
        'selected_branch': selected_branch,
        'page_title': 'Add Timetable Entry',
    }
    return render(request, 'hod/timetable_add.html', context)

@role_required('2')
@login_required
def hod_timetable_edit(request, pk):
    """HOD edits an existing timetable entry"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Get the timetable entry and verify HOD has access to its branch
    timetable = get_object_or_404(Timetable, pk=pk)
    if timetable.branch not in hod_branches:
        messages.error(request, "You don't have permission to edit this timetable entry.")
        return redirect('hod_timetable')

    selected_branch = timetable.branch

    if request.method == 'POST':
        # Get branch from form data
        branch_id = request.POST.get('branch')
        try:
            selected_branch = hod_branches.get(id=branch_id)
        except (Branch.DoesNotExist, ValueError):
            selected_branch = timetable.branch

        form = TimetableForm(request.POST, instance=timetable, branch=selected_branch)
        if form.is_valid():
            try:
                timetable_entry = form.save(commit=False)
                timetable_entry.branch = selected_branch
                timetable_entry.save()
                form.save_m2m()  # Save ManyToMany fields (combined_sections)
                messages.success(request, "Timetable entry updated successfully.")
                return redirect('hod_timetable')
            except IntegrityError as e:
                messages.error(request, f"Error: {str(e)}")
            except Exception as e:
                messages.error(request, f"An error occurred: {str(e)}")
    else:
        form = TimetableForm(instance=timetable, branch=selected_branch)

    context = {
        'form': form,
        'timetable': timetable,
        'hod_branches': hod_branches,
        'selected_branch': selected_branch,
        'page_title': 'Edit Timetable Entry',
    }
    return render(request, 'hod/timetable_edit.html', context)

@role_required('2')
@login_required
def hod_timetable_delete(request, pk):
    """HOD deletes a timetable entry"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Get the timetable entry and verify HOD has access to its branch
    timetable = get_object_or_404(Timetable, pk=pk)
    if timetable.branch not in hod_branches:
        messages.error(request, "You don't have permission to delete this timetable entry.")
        return redirect('hod_timetable')

    if request.method == 'POST':
        try:
            timetable_info = f"{timetable.branch.name} - {timetable.section.name} - {timetable.day} - {timetable.period_time}"
            timetable.delete()
            messages.success(request, f"Timetable entry '{timetable_info}' deleted successfully.")
        except Exception as e:
            messages.error(request, f"Error deleting timetable entry: {str(e)}")
        return redirect('hod_timetable')

    return redirect('hod_timetable')

@role_required('2')
@login_required
def hod_timetable_view(request):
    """HOD views timetables in grid format similar to student view"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Get filter parameters
    branch_id = request.GET.get('branch', '')
    semester_id = request.GET.get('semester', '')
    section_id = request.GET.get('section', '')

    # Initialize context
    context = {
        'hod_branches': hod_branches,
        'semesters': [],
        'sections': [],
        'batch_timetables': {},
        'time_slots': [],
        'days': [],
        'page_title': 'View Timetable',
    }

    # If branch is selected, get its semesters
    if branch_id:
        try:
            selected_branch = hod_branches.get(id=branch_id)
            context['selected_branch'] = selected_branch
            # Get semesters that have sections in this branch
            context['semesters'] = Semester.objects.filter(
                section__branch=selected_branch
            ).distinct().order_by('number')
        except (Branch.DoesNotExist, ValueError):
            pass

    # If semester is selected, get its sections
    if branch_id and semester_id:
        try:
            selected_branch = hod_branches.get(id=branch_id)
            selected_semester = Semester.objects.get(id=semester_id)
            context['selected_branch'] = selected_branch
            context['selected_semester'] = selected_semester
            context['sections'] = Section.objects.filter(
                branch=selected_branch,
                semester=selected_semester
            ).order_by('name')
        except (Branch.DoesNotExist, Semester.DoesNotExist, ValueError):
            pass

    # If all filters are selected, generate timetable
    if branch_id and semester_id and section_id:
        try:
            selected_branch = hod_branches.get(id=branch_id)
            selected_semester = Semester.objects.get(id=semester_id)  
            selected_section = Section.objects.get(id=section_id, semester=selected_semester, branch=selected_branch)

            context['selected_branch'] = selected_branch
            context['selected_semester'] = selected_semester
            context['selected_section'] = selected_section

            # Get all timetable entries for the selected filters
            timetable_entries = Timetable.objects.filter(
                branch=selected_branch,
                semester=selected_semester,
                section=selected_section
            ).select_related('subject', 'faculty', 'faculty__user')

            # Define days and time slots (matching the form choices)
            days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
            time_slots = [
                {'slot': '9:00-10:00'},
                {'slot': '10:00-11:00'},
                {'slot': '11:00-12:00'},
                {'slot': '12:00-1:00'},
                {'slot': '2:15-3:15'},
                {'slot': '3:15-4:15'},
                {'slot': '4:15-5:15'},
            ]

            # Organize timetable data in grid format
            timetable_grid = {}
            for day in days:
                timetable_grid[day] = {}
                for slot in time_slots:
                    timetable_grid[day][slot['slot']] = {
                        'subject': '-',
                        'faculty': '-'
                    }

            # Fill the grid with actual timetable data
            for entry in timetable_entries:
                if entry.day in timetable_grid and entry.period_time in timetable_grid[entry.day]:
                    timetable_grid[entry.day][entry.period_time] = {
                        'subject': entry.subject.name if entry.subject else '-',
                        'faculty': entry.faculty.user.get_full_name() if entry.faculty else '-'
                    }

            # Create batch name for display
            batch_name = f"{selected_branch.code} - Semester {selected_semester.number} - Section {selected_section.name}"

            context.update({
                'batch_timetables': {batch_name: timetable_grid},
                'time_slots': time_slots,
                'days': days,
                'branch': selected_branch,
                'semester': selected_semester,
                'section': selected_section,
            })

        except (Branch.DoesNotExist, Semester.DoesNotExist, Section.DoesNotExist, ValueError):
            messages.error(request, "Invalid selection. Please try again.")

    return render(request, 'hod/timetable_view.html', context)

@csrf_exempt
@login_required
def ajax_filter_students_by_semester(request):
    if request.method == 'POST':
        semester = request.POST.get('semester')
        students = StudentProfile.objects.filter(semester=semester)
        return JsonResponse({'students': [{'id': s.id, 'name': s.user.get_full_name()} for s in students]})
    return JsonResponse({'students': []})

@login_required
def ajax_get_semesters_by_branch(request):
    """Get semesters for a specific branch"""
    branch_id = request.GET.get('branch_id')

    if not branch_id:
        return JsonResponse({'semesters': []})

    try:
        # Get semesters that have subject allocations for this branch
        semesters = Semester.objects.filter(
            subjectallocation__branches__id=branch_id
        ).distinct().values('id', 'number')

        return JsonResponse({'semesters': list(semesters)})
    except Exception:
        return JsonResponse({'semesters': []})

@login_required
def ajax_get_sections_by_semester(request):
    """Get sections for a specific semester and branch"""
    semester_id = request.GET.get('semester_id')
    branch_id = request.GET.get('branch_id')

    if not semester_id or not branch_id:
        return JsonResponse({'sections': []})

    try:
        # Get sections that have subject allocations for this semester and branch
        sections = Section.objects.filter(
            branch_id=branch_id,
            subjectallocation__semester_id=semester_id,
            subjectallocation__branches__id=branch_id
        ).distinct().values('id', 'name')

        return JsonResponse({'sections': list(sections)})
    except Exception:
        return JsonResponse({'sections': []})

@login_required
def ajax_get_subjects_by_section(request):
    """Get subjects for a specific section, semester, and branch"""
    section_id = request.GET.get('section_id')
    semester_id = request.GET.get('semester_id')
    branch_id = request.GET.get('branch_id')

    if not section_id or not semester_id or not branch_id:
        return JsonResponse({'subjects': []})

    try:
        # Get subjects allocated to this section, semester, and branch
        subjects = Subject.objects.filter(
            allocations__sections__id=section_id,
            allocations__semester_id=semester_id,
            allocations__branches__id=branch_id
        ).distinct().values('id', 'name', 'code')

        return JsonResponse({'subjects': list(subjects)})
    except Exception:
        return JsonResponse({'subjects': []})

@login_required
def ajax_get_faculty_by_subject(request):
    """Get faculty assigned to a specific subject, section, semester, and branch"""
    subject_id = request.GET.get('subject_id')
    section_id = request.GET.get('section_id')
    semester_id = request.GET.get('semester_id')
    branch_id = request.GET.get('branch_id')

    if not subject_id or not section_id or not semester_id or not branch_id:
        return JsonResponse({'faculty': []})

    try:
        # Get faculty assigned to this subject, section, semester, and branch
        faculty = FacultyProfile.objects.filter(
            subject_allocations__subject_id=subject_id,
            subject_allocations__sections__id=section_id,
            subject_allocations__semester_id=semester_id,
            subject_allocations__branches__id=branch_id
        ).distinct().select_related('user').values('id', 'user__first_name', 'user__last_name')

        # Format faculty names
        faculty_list = [
            {
                'id': f['id'],
                'name': f"{f['user__first_name']} {f['user__last_name']}"
            }
            for f in faculty
        ]

        return JsonResponse({'faculty': faculty_list})
    except Exception:
        return JsonResponse({'faculty': []})

@login_required
def ajax_get_attendance_semesters(request):
    """Get semesters for attendance view based on selected branch"""
    branch_id = request.GET.get('branch_id')

    if not branch_id:
        return JsonResponse({'semesters': []})

    try:
        # Verify HOD has access to this branch
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'semesters': [], 'error': 'Access denied'})

        # Get semesters that have subject allocations for this branch
        semesters = Semester.objects.filter(
            subjectallocation__branches__id=branch_id
        ).distinct().values('id', 'number').order_by('number')

        return JsonResponse({'semesters': list(semesters)})
    except Exception:
        return JsonResponse({'semesters': []})

@login_required
def ajax_get_attendance_sections(request):
    """Get sections for attendance view based on selected branch and semester"""
    branch_id = request.GET.get('branch_id')
    semester_id = request.GET.get('semester_id')

    if not branch_id or not semester_id:
        return JsonResponse({'sections': []})

    try:
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'sections': []})

        sections = Section.objects.filter(
            branch_id=branch_id,
            semester_id=semester_id
        ).values('id', 'name').order_by('name')

        return JsonResponse({'sections': list(sections)})
    except Exception:
        return JsonResponse({'sections': []})

@login_required
def ajax_get_attendance_subjects(request):
    """Get subjects for attendance view based on selected branch, semester, and section"""
    branch_id = request.GET.get('branch_id')
    semester_id = request.GET.get('semester_id')
    section_id = request.GET.get('section_id')

    if not branch_id or not semester_id or not section_id:
        return JsonResponse({'subjects': []})

    try:
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'subjects': []})

        subjects = Subject.objects.filter(
            allocations__branches__id=branch_id,
            allocations__semester_id=semester_id,
            allocations__sections__id=section_id
        ).distinct().values('id', 'name', 'code').order_by('name')

        return JsonResponse({'subjects': list(subjects)})
    except Exception:
        return JsonResponse({'subjects': []})

@login_required
def ajax_get_student_semesters(request):
    """Get semesters for student list based on selected branch"""
    branch_id = request.GET.get('branch_id')

    if not branch_id:
        return JsonResponse({'semesters': []})

    try:
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'semesters': []})

        semesters = Semester.objects.filter(
            studentprofile__branch_id=branch_id
        ).distinct().values('id', 'number').order_by('number')

        return JsonResponse({'semesters': list(semesters)})
    except Exception:
        return JsonResponse({'semesters': []})

@login_required
def ajax_get_student_sections(request):
    """Get sections for student list based on selected branch and semester"""
    branch_id = request.GET.get('branch_id')
    semester_id = request.GET.get('semester_id')

    if not branch_id or not semester_id:
        return JsonResponse({'sections': []})

    try:
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'sections': []})

        sections = Section.objects.filter(
            branch_id=branch_id,
            semester_id=semester_id
        ).values('id', 'name').order_by('name')

        return JsonResponse({'sections': list(sections)})
    except Exception:
        return JsonResponse({'sections': []})

@login_required
def ajax_get_fee_semesters(request):
    """Get semesters for fee management based on selected branch"""
    branch_id = request.GET.get('branch_id')

    if not branch_id:
        return JsonResponse({'semesters': []})

    try:
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'semesters': []})

        semesters = Semester.objects.filter(
            studentprofile__studentfeerecord__student__branch_id=branch_id
        ).distinct().values('id', 'number').order_by('number')

        return JsonResponse({'semesters': list(semesters)})
    except Exception:
        return JsonResponse({'semesters': []})

@login_required
def ajax_get_fee_sections(request):
    """Get sections for fee management based on selected branch and semester"""
    branch_id = request.GET.get('branch_id')
    semester_id = request.GET.get('semester_id')

    if not branch_id or not semester_id:
        return JsonResponse({'sections': []})

    try:
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'sections': []})

        sections = Section.objects.filter(
            branch_id=branch_id,
            semester_id=semester_id
        ).values('id', 'name').order_by('name')

        return JsonResponse({'sections': list(sections)})
    except Exception:
        return JsonResponse({'sections': []})

@login_required
def ajax_get_promotion_years(request):
    """Get years for promotion based on selected branch"""
    branch_id = request.GET.get('branch_id')

    if not branch_id:
        return JsonResponse({'years': []})

    try:
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'years': []})

        # Get years that have students in this branch
        years = Year.objects.filter(
            studentprofile__branch_id=branch_id
        ).distinct().values('id', 'number').order_by('number')

        return JsonResponse({'years': list(years)})
    except Exception:
        return JsonResponse({'years': []})

@login_required
def ajax_get_promotion_semesters(request):
    """Get semesters for promotion based on selected branch and year"""
    branch_id = request.GET.get('branch_id')
    year_id = request.GET.get('year_id')

    if not branch_id or not year_id:
        return JsonResponse({'semesters': []})

    try:
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'semesters': []})

        # Get semesters that have students in this branch and year
        semesters = Semester.objects.filter(
            studentprofile__branch_id=branch_id,
            studentprofile__current_year_id=year_id
        ).distinct().values('id', 'number').order_by('number')

        return JsonResponse({'semesters': list(semesters)})
    except Exception:
        return JsonResponse({'semesters': []})

@login_required
def ajax_get_promotion_sections(request):
    """Get sections for promotion based on selected branch, year, and semester"""
    branch_id = request.GET.get('branch_id')
    year_id = request.GET.get('year_id')
    semester_id = request.GET.get('semester_id')

    if not branch_id or not year_id or not semester_id:
        return JsonResponse({'sections': []})

    try:
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'sections': []})

        # Get sections that have students in this branch, year, and semester
        sections = Section.objects.filter(
            branch_id=branch_id,
            year_id=year_id,
            semester_id=semester_id,
            studentprofile__branch_id=branch_id,
            studentprofile__current_year_id=year_id,
            studentprofile__current_semester_id=semester_id
        ).distinct().values('id', 'name').order_by('name')

        return JsonResponse({'sections': list(sections)})
    except Exception:
        return JsonResponse({'sections': []})

@role_required('2')
@login_required
def section_timetable_views(request, section_id):
    try:
        section = get_object_or_404(Section, id=section_id)
        entries = Timetable.objects.filter(section=section).select_related('subject', 'faculty', 'faculty__user')

        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        periods = sorted({e.period_time for e in entries})
        timetable = defaultdict(lambda: defaultdict(lambda: None))
        subject_colors = {}

        for e in entries:
            timetable[e.day][e.period_time] = e
            if e.subject.code not in subject_colors:
                subject_colors[e.subject.code] = color_from_code(e.subject.code)

        return render(request, 'section_timetable.html', {
            'section': section,
            'days': days,
            'periods': periods,
            'timetable': timetable,
            'subjects': Subject.objects.all(),
            'faculties': FacultyProfile.objects.select_related('user'),
            'subject_colors': subject_colors,
        })
    except Exception as e:
        messages.error(request, f"Error loading timetable: {e}")
        return redirect('hod_timetable')

@role_required('2')
@login_required
def views_faculties(request):
    """HOD views faculty list for their branches"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Filter faculties by HOD's branches
    faculties = FacultyProfile.objects.filter(branches__in=hod_branches).select_related('user').distinct().order_by('user__first_name')

    # Add branch filter
    branch_filter = request.GET.get('branch', '')
    if branch_filter:
        faculties = faculties.filter(branches__id=branch_filter)

    context = {
        'faculties': faculties,
        'hod_branches': hod_branches,
        'branch_filter': branch_filter,
        'page_title': 'Faculty List',
    }
    return render(request, 'hod/faculty_list.html', context)

@role_required('2')
@login_required
def views_students(request):
    """HOD views student list with flexible filtering"""
    try:
        hod_profile = request.user.profile
        hod_branches = hod_profile.branches.all()
    except HODProfile.DoesNotExist:
        messages.error(request, "HOD profile not found.")
        return redirect('hod_home')

    if not hod_branches.exists():
        messages.warning(request, "No branches assigned to you. Please contact administrator.")
        return redirect('hod_home')

    # Get filter parameters
    selected_branch_id = request.GET.get('branch')
    selected_semester_id = request.GET.get('semester')
    selected_section_id = request.GET.get('section')
    selected_year_id = request.GET.get('year')
    fee_status_filter = request.GET.get('fee_status')
    search_query = request.GET.get('search', '').strip()
    show_all = request.GET.get('show_all', False)

    # Initialize context with all available options
    context = {
        'hod_branches': hod_branches,
        'semesters': [],
        'sections': [],
        'years': Year.objects.all(),
        'fee_status_choices': [
            ('pending', 'Pending'),
            ('partial', 'Partial'),
            ('confirmed', 'Confirmed'),
        ],
        'selected_branch_id': selected_branch_id,
        'selected_semester_id': selected_semester_id,
        'selected_section_id': selected_section_id,
        'selected_year_id': selected_year_id,
        'fee_status_filter': fee_status_filter,
        'search_query': search_query,
        'page_title': 'Student List',
    }

    # Load semesters based on selected branch
    if selected_branch_id:
        try:
            selected_branch = hod_branches.get(id=selected_branch_id)
            context['selected_branch'] = selected_branch
            # Get semesters that have students in this branch
            context['semesters'] = Semester.objects.filter(
                studentprofile__branch=selected_branch
            ).distinct().order_by('number')
        except Branch.DoesNotExist:
            messages.error(request, "Selected branch not found or not assigned to you.")

    # Load sections based on selected branch and semester
    if selected_branch_id and selected_semester_id:
        try:
            selected_semester = Semester.objects.get(id=selected_semester_id)
            context['selected_semester'] = selected_semester
            # Get sections for this branch and semester
            context['sections'] = Section.objects.filter(
                branch_id=selected_branch_id,
                semester=selected_semester
            ).order_by('name')
        except Semester.DoesNotExist:
            messages.error(request, "Selected semester not found.")

    # Build dynamic filters for students - flexible filtering
    if any([selected_branch_id, selected_semester_id, selected_section_id, selected_year_id, fee_status_filter, search_query]) or show_all:
        # Build student filters
        student_filters = {'branch__in': hod_branches}

        if selected_branch_id:
            student_filters['branch_id'] = selected_branch_id
        if selected_semester_id:
            student_filters['current_semester_id'] = selected_semester_id
        if selected_section_id:
            student_filters['section_id'] = selected_section_id
        if selected_year_id:
            student_filters['current_year_id'] = selected_year_id

        # Get students based on filters
        students = StudentProfile.objects.filter(**student_filters).select_related(
            'user', 'section', 'current_semester', 'current_year', 'branch'
        ).order_by('branch__name', 'current_year__number', 'current_semester__number', 'section__name', 'roll_number')

        # Apply search filter
        if search_query:
            students = students.filter(
                Q(user__first_name__icontains=search_query) |
                Q(user__last_name__icontains=search_query) |
                Q(user__email__icontains=search_query) |
                Q(roll_number__icontains=search_query)
            )

        # Prepare student data with fee status
        student_data = []
        for student in students:
            latest_fee = student.studentfeerecord_set.order_by('-payment_date').first()
            fee_status = latest_fee.status if latest_fee else 'pending'
            fee_status_display = latest_fee.get_status_display() if latest_fee else 'Pending'

            # Apply fee status filter
            if fee_status_filter and fee_status != fee_status_filter:
                continue

            student_data.append({
                'student': student,
                'fee_status': fee_status_display,
                'fee_status_code': fee_status,
            })

        context['student_data'] = student_data
        context['total_students'] = len(student_data)

        # Add summary statistics
        if student_data:
            fee_summary = {}
            for data in student_data:
                status = data['fee_status_code']
                fee_summary[status] = fee_summary.get(status, 0) + 1
            context['fee_summary'] = fee_summary

        # Set selected objects for display
        if selected_section_id:
            try:
                context['selected_section'] = Section.objects.get(id=selected_section_id)
            except Section.DoesNotExist:
                pass
        if selected_year_id:
            try:
                context['selected_year'] = Year.objects.get(id=selected_year_id)
            except Year.DoesNotExist:
                pass

    else:
        # No filters applied - show welcome message
        context['student_data'] = []
        context['total_students'] = 0

    return render(request, 'hod/student_list.html', context)

@login_required
def ajax_get_timetable_semesters(request):
    """Get semesters for timetable filtering based on selected branch"""
    branch_id = request.GET.get('branch_id')

    if not branch_id:
        return JsonResponse({'semesters': []})

    try:
        # Verify HOD has access to this branch
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'semesters': [], 'error': 'Access denied'})

        # Get semesters that have timetable entries for this branch
        semesters = Semester.objects.filter(
            timetable__branch_id=branch_id
        ).distinct().values('id', 'number').order_by('number')

        return JsonResponse({'semesters': list(semesters)})
    except Exception:
        return JsonResponse({'semesters': []})

@login_required
def ajax_get_timetable_sections(request):
    """Get sections for timetable filtering based on selected branch and semester"""
    branch_id = request.GET.get('branch_id')
    semester_number = request.GET.get('semester_number')

    if not branch_id:
        return JsonResponse({'sections': []})

    try:
        # Verify HOD has access to this branch
        hod_profile = request.user.profile
        if not hod_profile.branches.filter(id=branch_id).exists():
            return JsonResponse({'sections': []})

        # Build filter for sections
        section_filters = {'timetable__branch_id': branch_id}
        if semester_number:
            section_filters['timetable__semester__number'] = semester_number

        # Get sections that have timetable entries for this branch (and semester if specified)
        sections = Section.objects.filter(**section_filters).distinct().values('id', 'name').order_by('name')

        return JsonResponse({'sections': list(sections)})
    except Exception:
        return JsonResponse({'sections': []})

