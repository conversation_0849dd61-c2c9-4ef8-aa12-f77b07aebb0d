# NITRA-CMS (College Management System)

A comprehensive Django-based College Management System designed for **NITRA TECHNICAL CAMPUS, GHAZIABAD**. This system provides role-based access for administrators, HODs, faculty, students, and accountants to manage various academic and administrative operations.

## 🏫 About NITRA-CMS

NITRA-CMS is a full-featured college management system that streamlines academic operations, student management, fee collection, attendance tracking, and more. Built with Django 5.2 and modern web technologies, it provides a secure and user-friendly interface for all stakeholders.

The system features a modern, responsive design with comprehensive role-based access control, automated receipt generation with QR codes, real-time notifications, and advanced reporting capabilities.

## ✨ Key Features

### 👥 Multi-Role Support
- **Admin**: Complete system administration
- **HOD (Head of Department)**: Department-level management across multiple branches
- **Faculty**: Teaching, attendance, and student management
- **Student**: Academic records, fee payments, and notifications
- **Accountant**: Fee management and verification

### 📚 Academic Management
- **Branch Management**: CSE, ECE, ME, and more
- **Student Profiles**: Complete academic and personal details
- **Subject Allocation**: Faculty-subject assignments
- **Timetable Management**: Branch-wise and section-wise scheduling
- **Results Management**: Multiple result types (Mid-Term, End-Term, etc.)

### 📊 Attendance System
- **Digital Attendance**: Faculty can mark attendance digitally
- **Session Management**: Lecture session tracking
- **Attendance Reports**: Comprehensive attendance analytics
- **Student View**: Students can view their attendance records

### 💰 Fee Management
- **Fee Structures**: Configurable fee structures by branch/semester
- **Payment Tracking**: Digital payment proof uploads with file validation
- **Receipt Generation**: Professional PDF receipts with college branding and QR codes
- **Verification System**: Two-step accountant verification workflow
- **Payment Types**: Semester-wise and year-wise fee options
- **QR Code Integration**: Unique QR codes for each verified payment
- **Automated Processing**: Automatic receipt generation upon verification

### 📱 Notifications System
- **Role-based Notifications**: Targeted messaging system with group filtering
- **Read/Unread Status**: Notification status tracking with real-time updates
- **Faculty Notifications**: Faculty can send notifications to students
- **Dashboard Integration**: Notifications displayed on home pages with preview
- **Group Targeting**: Send notifications to specific branches, semesters, or sections
- **Context Processor**: Global notification count and recent notifications

### 📖 Study Materials
- **Study Notes**: Faculty can upload study materials
- **Previous Papers**: Exam paper repository
- **Assessments**: Assignment and assessment management
- **Subject-wise Organization**: Materials organized by subjects

### 🔐 Security Features
- **Email-based Authentication**: Secure login with email verification
- **Role-based Access Control**: Strict permission management
- **Login Attempt Protection**: Brute-force attack prevention with Django Axes
- **Password Security**: Strong password validation

## 🛠️ Technology Stack

### Core Framework
- **Backend**: Django 5.2, Python 3.8+
- **Database**: SQLite (development), PostgreSQL/MySQL (production)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 4/5
- **Template Engine**: Django Templates with custom context processors

### Key Dependencies
- **Admin Interface**: Django Jazzmin (Beautiful admin UI with custom icons)
- **Security**: Django Axes for brute-force protection
- **Environment**: Django Environ for configuration management
- **Static Files**: WhiteNoise for production static file serving
- **File Processing**: Pillow for image handling
- **PDF Generation**: ReportLab for professional receipts
- **QR Codes**: qrcode library for payment verification
- **Data Export**: Django Import-Export for admin data management
- **Caching**: Redis (production), Local Memory (development)

## 📋 Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- Git

## 🚀 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd NITRA-CMS
```

### 2. Create Virtual Environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Environment Configuration
Create a `.env` file in the project root:
```env
# Core Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration (Optional - defaults to SQLite)
DATABASE_URL=postgres://user:password@localhost:5432/nitra_cms

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# External Services
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key

# Production Settings (Optional)
REDIS_URL=redis://127.0.0.1:6379/1
```

### 5. Database Setup
```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. Create Superuser (Optional)
```bash
python manage.py createsuperuser
```

### 7. Load Dummy Data (For Testing)
```bash
python manage.py add_dummy_data
```

**Note**: This command creates comprehensive test data including:
- 200+ students across all branches and semesters
- Faculty members with subject allocations
- HODs for each branch
- Fee structures and sample payments
- Attendance records and notifications
- Study materials and feedback data

### 8. Run the Development Server
```bash
python manage.py runserver
```

Visit `http://127.0.0.1:8000` to access the application.

## 👤 Default User Credentials

After loading dummy data, you can use these credentials:

### Admin
- **Email**: <EMAIL>
- **Password**: Abhi@356#

### HOD Users
- **Email**: <EMAIL>, <EMAIL>, <EMAIL>
- **Password**: Abhi@356#

### Faculty Users
- **Email**: <EMAIL>, <EMAIL>, etc.
- **Password**: Abhi@356#

### Student Users
- **Email**: student1@nitra.<NAME_EMAIL>
- **Password**: Abhi@356#

### Accountant
- **Email**: <EMAIL>
- **Password**: Abhi@356#

## 📁 Project Structure

```
NITRA-CMS/
├── academic/           # Academic structure (branches, sections, etc.)
├── accountant/         # Accountant role functionality
├── attendance/         # Attendance management with session tracking
├── cms_project/        # Main project settings and configuration
├── faculty/           # Faculty role functionality
├── feedback/          # Student feedback system
├── fees/              # Fee management with QR codes and receipts
├── hod/               # HOD role functionality with multi-branch support
├── materials/         # Study materials management
├── notifications/     # Real-time notification system
├── results/           # Results management with multiple types
├── student/           # Student role functionality
├── timetable/         # Timetable management with branch filtering
├── users/             # User authentication and email verification
├── templates/         # HTML templates with role-based partials
│   ├── partials/      # Reusable template components
│   ├── accountant/    # Accountant-specific templates
│   ├── faculty/       # Faculty-specific templates
│   ├── hod/           # HOD-specific templates
│   ├── student/       # Student-specific templates
│   └── users/         # Authentication templates
├── static/            # Static files (CSS, JS, images, AdminLTE)
│   ├── adminlte/      # AdminLTE theme files
│   ├── js/            # Custom JavaScript
│   └── logo/          # College logo and branding
├── media/             # User uploaded files
│   ├── fee_proofs/    # Payment proof uploads
│   ├── fee_receipts/  # Generated PDF receipts
│   ├── fee_qr/        # QR code images
│   └── study_notes/   # Study materials
└── logs/              # Application logs (debug, error, django)
```

## 🔧 Key Management Commands

### Database Management
```bash
python manage.py makemigrations        # Create new migrations
python manage.py migrate               # Apply migrations
python manage.py flush                 # Reset database (removes all data)
python manage.py createsuperuser       # Create admin user
```

### Dummy Data Management
```bash
python manage.py add_dummy_data        # Load comprehensive test data
```
**What this creates:**
- 200+ students across all branches, years, and semesters
- Faculty members with subject allocations
- HODs for CSE, ECE, and ME branches
- Fee structures for all branch-semester combinations
- Sample fee payments with different verification statuses
- Attendance sessions and records
- Notifications and feedback data

### Static Files & Production
```bash
python manage.py collectstatic         # Collect static files for production
python manage.py check --deploy        # Check production readiness
```

## 🌟 Key Features in Detail

### Fee Management System
- **Professional Receipts**: Auto-generated PDF receipts with NITRA branding and blue color scheme
- **QR Code Integration**: Unique QR codes generated for each verified payment
- **Payment Verification**: Two-step verification process by accountants
- **Fee Structure Management**: Configurable fee structures by branch and semester
- **File Validation**: Secure upload system for payment proofs
- **Automated Processing**: Receipts generated automatically upon verification
- **Payment Tracking**: Track semester-wise and year-wise fee payments

### Attendance System
- **Session-based Tracking**: Lecture sessions with automatic date validation
- **Bulk Operations**: Mark attendance for entire classes efficiently
- **Attendance Analytics**: Comprehensive reporting with date range filters
- **Student Dashboard**: Students can view detailed attendance records
- **Flexible Filtering**: Filter by branch, semester, section, subject, and date range
- **Session Management**: Prevent duplicate sessions and future date entries

### Notification System
- **Targeted Messaging**: Role-based notification delivery with group filtering
- **Read Status Tracking**: Mark notifications as read/unread with real-time updates
- **Dashboard Integration**: Global notification count and recent notifications preview
- **Group Targeting**: Send to specific branches, semesters, sections, or all users
- **Context Processor**: Notifications available across all pages
- **Faculty Broadcasting**: Faculty can send notifications to their students

### Results Management
- **Multiple Result Types**: Mid-Term, End-Term, Assignment, Quiz, Practical
- **Cascading Filters**: Branch → Semester → Section → Subject selection
- **Grade Management**: Comprehensive grading system with validation
- **Result Analytics**: Performance tracking and reporting
- **Flexible Editing**: Edit results with proper validation and error handling

## 🔒 Security Features

### Authentication & Authorization
- **Email-based Authentication**: Custom email backend for secure login
- **Email Verification**: Mandatory email verification for new users
- **Role-based Access Control**: Strict permission controls with middleware
- **Multi-role Support**: Admin, HOD, Faculty, Student, Accountant roles

### Security Measures
- **Login Attempt Protection**: Django Axes with 5-attempt limit and 1-hour cooldown
- **Password Validation**: Strong password requirements (8+ chars, complexity)
- **CSRF Protection**: Built-in Django CSRF protection with secure cookies
- **Session Security**: Secure session cookies with HTTP-only flags
- **File Upload Validation**: Secure file upload system with type validation

### Production Security
- **HTTPS Enforcement**: SSL redirect and HSTS headers in production
- **Security Headers**: XSS protection, content type sniffing prevention
- **Environment-based Config**: Sensitive settings via environment variables
- **Logging**: Comprehensive error and access logging

## 📱 Responsive Design

The system is fully responsive and works seamlessly across:
- Desktop computers
- Tablets
- Mobile devices

## 🚀 Production Deployment

### Production Dependencies
For production deployment, uncomment these lines in `requirements.txt`:
```bash
redis==5.0.1            # Redis cache backend
django-redis==5.4.0     # Django Redis integration
gunicorn==21.2.0        # WSGI HTTP Server
```

### Production Environment Variables
```env
DEBUG=False
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DATABASE_URL=postgres://user:password@localhost:5432/nitra_cms_prod
REDIS_URL=redis://127.0.0.1:6379/1
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-production-email-password
```

### Production Checklist
1. Set `DEBUG=False` in environment
2. Configure proper database (MySQL recommended)
3. Set up Redis for caching
4. Configure email settings
5. Set up SSL certificates
6. Run `python manage.py check --deploy`
7. Collect static files: `python manage.py collectstatic`
8. Set up proper logging and monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support & Troubleshooting

### Common Issues

#### Database Issues
```bash
# Reset database completely
python manage.py flush
python manage.py migrate
python manage.py add_dummy_data

# Fix migration conflicts
python manage.py migrate --fake-initial
```

#### Static Files Issues
```bash
# Clear and recollect static files
python manage.py collectstatic --clear --noinput
```

#### Email Configuration Issues
- Ensure Gmail App Password is used (not regular password)
- Check firewall settings for SMTP ports
- Verify EMAIL_USE_TLS=True for Gmail

#### Permission Issues
- Ensure proper file permissions for media directory
- Check that logs directory is writable
- Verify database file permissions (for SQLite)

### Getting Help
For support and questions:
- Create an issue in the repository with detailed error logs
- Check the `logs/` directory for error details
- Contact the development team
- Refer to `DUMMY_DATA_INSTRUCTIONS.md` for data setup issues

## 🏗️ Future Enhancements

### Planned Features
- **Mobile Application**: Native Android/iOS apps for students and faculty
- **Advanced Analytics**: Comprehensive dashboard with charts and insights
- **API Integration**: RESTful APIs for external system integration
- **Enhanced Reporting**: Advanced report generation with export options
- **Real-time Features**: Live chat, real-time notifications, and updates

### Technical Improvements
- **Microservices Architecture**: Break down into smaller, manageable services
- **Cloud Deployment**: AWS/Azure deployment with auto-scaling
- **Performance Optimization**: Database optimization and caching strategies
- **Testing Suite**: Comprehensive unit and integration tests
- **CI/CD Pipeline**: Automated testing and deployment pipeline

---

**NITRA TECHNICAL CAMPUS, GHAZIABAD**
*Empowering Education Through Technology*
