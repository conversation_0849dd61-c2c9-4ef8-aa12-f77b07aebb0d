from django.db.models import Q
from notifications.models import Notification

def unread_notifications(request):
    """
    Context processor to make unread notifications count and recent notifications
    available on all pages for all users.
    """
    # Default values
    unread_count = 0
    recent_notifications = []

    # Only process for authenticated users
    if not request.user.is_authenticated:
        return {
            'unread_notifications_count': unread_count,
            'recent_notifications': recent_notifications
        }

    # Get user type
    user_type = request.user.user_type

    # Calculate notifications based on user type
    if user_type == 4:  # Student
        try:
            student = request.user.studentprofile
            # Query for student notifications
            notifications_query = Q(recipient=request.user) | \
                                 Q(recipient_group='all_students') | \
                                 Q(recipient_group='faculty_and_students') | \
                                 Q(recipient_group='group_students', branch=student.branch,
                                   semester=student.current_semester, section=student.section)

            # Get unread count
            unread_count = Notification.objects.filter(
                notifications_query,
                is_read=False
            ).count()

            # Get recent notifications
            recent_notifications = Notification.objects.filter(
                notifications_query
            ).order_by('-created_at')[:5]

        except Exception:
            # Handle case where student profile doesn't exist
            pass

    elif user_type == 3:  # Faculty
        # Query for faculty notifications
        notifications_query = Q(recipient=request.user) | \
                             Q(recipient_group='all_faculty') | \
                             Q(recipient_group='faculty_and_students')

        # Get unread count
        unread_count = Notification.objects.filter(
            notifications_query,
            is_read=False
        ).count()

        # Get recent notifications
        recent_notifications = Notification.objects.filter(
            notifications_query
        ).order_by('-created_at')[:5]

    # elif user_type == 2:  # HOD
    #     # Get unread count
    #     unread_count = Notification.objects.filter(
    #         recipient=request.user,
    #         is_read=False
    #     ).count()

    #     # Get recent notifications
    #     recent_notifications = Notification.objects.filter(
    #         recipient=request.user
    #     ).order_by('-created_at')[:5]

    # elif user_type == 5:  # Accountant
    #     # Get unread count
    #     unread_count = Notification.objects.filter(
    #         recipient=request.user,
    #         is_read=False
    #     ).count()

    #     # Get recent notifications
    #     recent_notifications = Notification.objects.filter(
    #         recipient=request.user
    #     ).order_by('-created_at')[:5]

    # Return the count and recent notifications in the context
    return {
        'unread_notifications_count': unread_count,
        'recent_notifications': recent_notifications
    }
