{% extends "base.html" %}
{% load attendance_tags %}
{% block title %}View/Edit Attendance | College ERP{% endblock %}
{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}
{% block navbar %}{% include "partials/navbar.html" %}{% endblock %}
{% block sidebar %}{% include "partials/sidebar_faculty.html" %}{% endblock %}

{% block content %}
  <section class="content">
    <div class="container-fluid">
<!-- Page Header -->
{% with page_title="View/Edit Attendance" icon_class="fas fa-clipboard-check" show_actions=False %}
  {% include "partials/page_header.html" %}
{% endwith %}

<div class="row">
  <div class="col-md-12">
    <!-- Selection Form -->
    <div class="card card-primary card-outline shadow-sm">
<div class="card-header d-flex align-items-center">
  <h3 class="card-title font-weight-bold">View/Edit Attendance</h3>
</div>
<form method="get" id="selectionForm" class="needs-validation" novalidate>
  <div class="card-body">
    <div class="row">
      <div class="col-md-2">
        <div class="form-group">
          <label for="branch">Branch</label>
          <select name="branch" id="branch" class="form-control mb-2">
            <option value="">-- All Branches --</option>
            {% for branch in faculty_branches %}
              <option value="{{ branch.id }}" {% if branch.id|stringformat:"s" == selected_branch_id %}selected{% endif %}>
                {{ branch.name }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="semester">Semester</label>
          <select name="semester" id="semester" class="form-control mb-2">
            <option value="">-- Select Semester --</option>
            {% for semester in semesters %}
              <option value="{{ semester.id }}" {% if semester.id|stringformat:"s" == selected_semester_id %}selected{% endif %}>
                Semester {{ semester.number }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="section">Section</label>
          <select name="section" id="section" class="form-control mb-2">
            <option value="">-- Select Section --</option>
            {% for section in sections %}
              <option value="{{ section.id }}" {% if section.id|stringformat:"s" == selected_section_id %}selected{% endif %}>
                {{ section.name }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="subject">Subject</label>
          <select name="subject" id="subject" class="form-control mb-2">
            <option value="">-- All Subjects --</option>
            {% for subject in subjects %}
              <option value="{{ subject.id }}" {% if subject.id|stringformat:"s" == selected_subject_id %}selected{% endif %}>
                {{ subject.code }} - {{ subject.name }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="start_date">Start Date</label>
          {{ date_range_form.start_date }}
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="end_date">End Date</label>
          {{ date_range_form.end_date }}
        </div>
      </div>
    </div>

    <!-- Quick Date Range Buttons -->
    <div class="row mt-2">
      <div class="col-md-12">
        <div class="btn-group btn-group-sm" role="group">
          <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('today')">Today</button>
          <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('thisWeek')">This Week</button>
          <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('thisMonth')">This Month</button>
          <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('lastMonth')">Last Month</button>
          <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('last30Days')">Last 30 Days</button>
        </div>
      </div>
    </div>
  </div>
  <div class="card-footer text-right bg-light">
    <button type="submit" class="btn btn-primary btn-sm">
      <i class="fas fa-search mr-1"></i> View Attendance
    </button>
  </div>
</form>
    </div>

    <!-- Attendance Table -->
    {% if students and sessions %}
    <div class="card card-secondary card-outline shadow-sm mt-4">
<div class="card-header d-flex align-items-center justify-content-between">
  <h3 class="card-title font-weight-bold">
    Attendance Records
    {% if selected_subject %}for {{ selected_subject.code }} - {{ selected_subject.name }}{% endif %}
  </h3>
  <div class="card-tools">
    <span class="badge badge-info">{{ total_students }} Students</span>
    <span class="badge badge-success">{{ total_sessions }} Sessions</span>
  </div>
</div>
<form method="post" id="attendanceForm" class="needs-validation" novalidate>
  {% csrf_token %}
  <div class="card-body table-responsive">
    <table id="attendanceTable" class="table table-bordered table-hover table-striped mb-0" style="width:100%">
      <thead class="thead-dark">
        <tr>
          <th>Student Name</th>
          <th>Roll Number</th>
          <th>Branch</th>
          <th>Section</th>
          {% for session in sessions %}
            <th>
              {{ session.date|date:"M d, Y" }}<br>
              <small>{{ session.subject_allocation.subject.code }}</small><br>
              <small>({{ session.lecture_count }} lecture{{ session.lecture_count|pluralize }})</small>
            </th>
          {% endfor %}
        </tr>
      </thead>
      <tbody>
        {% for student in students %}
        <tr>
          <td>{{ student.user.get_full_name|default:student.user.username }}</td>
          <td>{{ student.roll_number|default:"N/A" }}</td>
          <td>{{ student.branch.code }}</td>
          <td>{{ student.section.name }}</td>
          {% for session in sessions %}
            {% with attendance=attendance_map|get_item:student.id|get_item:session.id %}
            <td>
              <div class="custom-control custom-switch">
                <input type="checkbox" name="is_present_{{ student.id }}_{{ session.id }}"
                       class="custom-control-input" id="is_present_{{ student.id }}_{{ session.id }}"
                       {% if attendance and attendance.is_present %}checked{% endif %}>
                <label class="custom-control-label" for="is_present_{{ student.id }}_{{ session.id }}">Present</label>
              </div>
              <input type="text" name="remarks_{{ student.id }}_{{ session.id }}"
                     class="form-control form-control-sm mt-1" maxlength="255"
                     value="{% if attendance %}{{ attendance.remarks|default:'' }}{% endif %}"
                     placeholder="Remarks">
            </td>
            {% endwith %}
          {% endfor %}
        </tr>
        {% empty %}
        <tr><td colspan="{{ sessions|length|add:4 }}" class="text-center text-muted">No students found for this section.</td></tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  <div class="card-footer text-right bg-light">
    <button type="submit" class="btn btn-success btn-sm">
      <i class="fas fa-save mr-1"></i> Save Changes
    </button>
  </div>
</form>
    </div>
    {% elif selected_branch_id or selected_semester_id or selected_section_id or selected_subject_id or selected_start_date or selected_end_date %}
    <div class="alert alert-info mt-4">
      <i class="fas fa-info-circle mr-2"></i>
      No attendance data found for the selected criteria. Try adjusting your filters or date range.
    </div>
    {% else %}
    <div class="alert alert-info mt-4">
      <i class="fas fa-info-circle mr-2"></i>
      <strong>Welcome to Attendance View/Edit!</strong><br>
      Select any combination of filters above to view and edit attendance data:
      <ul class="mb-0 mt-2">
        <li>Choose specific <strong>Branch, Semester, Section, or Subject</strong> to narrow down results</li>
        <li>Set a <strong>Date Range</strong> to view attendance for specific periods</li>
        <li>Use <strong>Quick Date Range</strong> buttons for common periods</li>
        <li>Leave filters empty to view all available data</li>
        <li><strong>Edit attendance</strong> by checking/unchecking presence and adding remarks</li>
      </ul>
    </div>
    {% endif %}
  </div>
</div>
    </div>
  </section>
{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
  .dataTables_scrollBody {
    min-height: 300px;
  }
  .custom-control-input:checked ~ .custom-control-label::before {
    background-color: #28a745;
    border-color: #28a745;
  }
  .custom-switch .custom-control-label::before {
    width: 2rem;
  }
  .custom-switch .custom-control-label::after {
    left: calc(-2.25rem + 2px);
  }
  .custom-switch .custom-control-input:checked ~ .custom-control-label::after {
    transform: translateX(1.5rem);
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
  $(document).ready(function () {
    // Helper function to reset dependent dropdowns
    function resetDependentDropdowns(dropdownIds, disable = true) {
      dropdownIds.forEach(function(id) {
        const dropdown = $(`#${id}`);
        dropdown.empty().append('<option value="">-- Select ' + id.charAt(0).toUpperCase() + id.slice(1) + ' --</option>');
        if (disable) {
          dropdown.prop('disabled', true);
        }
      });
    }

    // Helper function to validate date range
    function validateDateRange() {
      const startDate = $('#id_start_date').val();
      const endDate = $('#id_end_date').val();

      if (startDate && endDate) {
        $('#selectionForm').submit();
      }
    }

    // Branch change handler
    $('#branch').on('change', function() {
      const branchId = $(this).val();

      // Reset dependent dropdowns but keep them enabled
      resetDependentDropdowns(['semester', 'section', 'subject'], false);

      if (branchId) {
        loadSemesters(branchId);
      }
    });

    // Semester change handler
    $('#semester').on('change', function() {
      const semesterId = $(this).val();
      const branchId = $('#branch').val();

      // Reset dependent dropdowns but keep them enabled
      resetDependentDropdowns(['section', 'subject'], false);

      if (semesterId && branchId) {
        loadSections(branchId, semesterId);
      }
    });

    // Section change handler
    $('#section').on('change', function() {
      const sectionId = $(this).val();
      const semesterId = $('#semester').val();
      const branchId = $('#branch').val();

      // Reset dependent dropdowns but keep them enabled
      resetDependentDropdowns(['subject'], false);

      if (sectionId && semesterId && branchId) {
        loadSubjects(branchId, semesterId, sectionId);
      }
    });

    // Subject and date range change handlers
    $('#subject, #id_start_date, #id_end_date').on('change', function() {
      validateDateRange();
    });

    // Quick date range function
    window.setQuickDateRange = function(range) {
      const today = new Date();
      let startDate, endDate;

      switch(range) {
        case 'today':
          startDate = endDate = today;
          break;

        case 'thisWeek':
          const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
          const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));
          startDate = startOfWeek;
          endDate = endOfWeek;
          break;

        case 'thisMonth':
          startDate = new Date(today.getFullYear(), today.getMonth(), 1);
          endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
          break;

        case 'lastMonth':
          const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
          const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
          startDate = lastMonth;
          endDate = lastMonthEnd;
          break;

        case 'last30Days':
          startDate = new Date(today.setDate(today.getDate() - 30));
          endDate = new Date();
          break;
      }

      // Format dates as YYYY-MM-DD
      const formatDate = (date) => {
        return date.toISOString().split('T')[0];
      };

      $('#id_start_date').val(formatDate(startDate));
      $('#id_end_date').val(formatDate(endDate));

      validateDateRange();
    };

    // Function to load semesters based on branch
    function loadSemesters(branchId) {
      $.get('{% url "ajax_get_faculty_attendance_semesters" %}', {
        branch_id: branchId
      })
      .done(function(data) {
        const semesterSelect = $('#semester');
        semesterSelect.empty().append('<option value="">-- Select Semester --</option>');

        if (data.semesters && data.semesters.length > 0) {
          $.each(data.semesters, function(index, semester) {
            semesterSelect.append(`<option value="${semester.id}">Semester ${semester.number}</option>`);
          });

          // Restore selected value if exists
          const selectedSemester = '{{ selected_semester_id }}';
          if (selectedSemester) {
            semesterSelect.val(selectedSemester);
          }
        }
      })
      .fail(function() {
        console.error('Failed to load semesters');
      });
    }

    // Function to load sections based on branch and semester
    function loadSections(branchId, semesterId) {
      $.get('{% url "ajax_get_faculty_attendance_sections" %}', {
        branch_id: branchId,
        semester_id: semesterId
      })
      .done(function(data) {
        const sectionSelect = $('#section');
        sectionSelect.empty().append('<option value="">-- Select Section --</option>');

        if (data.sections && data.sections.length > 0) {
          $.each(data.sections, function(index, section) {
            sectionSelect.append(`<option value="${section.id}">${section.name}</option>`);
          });

          // Restore selected value if exists
          const selectedSection = '{{ selected_section_id }}';
          if (selectedSection) {
            sectionSelect.val(selectedSection);
          }
        }
      })
      .fail(function() {
        console.error('Failed to load sections');
      });
    }

    // Function to load subjects based on branch, semester, and section
    function loadSubjects(branchId, semesterId, sectionId) {
      $.get('{% url "ajax_get_faculty_attendance_subjects" %}', {
        branch_id: branchId,
        semester_id: semesterId,
        section_id: sectionId
      })
      .done(function(data) {
        const subjectSelect = $('#subject');
        subjectSelect.empty().append('<option value="">-- Select Subject --</option>');

        if (data.subjects && data.subjects.length > 0) {
          $.each(data.subjects, function(index, subject) {
            subjectSelect.append(`<option value="${subject.id}">${subject.code} - ${subject.name}</option>`);
          });

          // Restore selected value if exists
          const selectedSubject = '{{ selected_subject_id }}';
          if (selectedSubject) {
            subjectSelect.val(selectedSubject);
          }
        }
      })
      .fail(function() {
        console.error('Failed to load subjects');
      });
    }

    // Initialize DataTable using the global helper function with export buttons
    initDataTable('#attendanceTable', {
      "pageLength": 10,
      "scrollX": true,
      "scrollCollapse": true,
      "fixedColumns": {
        leftColumns: 4
      },
      "language": {
        "emptyTable": "No students found."
      },
      "columnDefs": [
        { "width": "20%", "targets": 0 },
        { "width": "10%", "targets": 1 },
        { "width": "10%", "targets": 2 },
        { "width": "10%", "targets": 3 }
      ],
      "dom": 'Bfrtip',
      "buttons": [
        {
          extend: 'csv',
          text: '<i class="fas fa-file-csv mr-1"></i> CSV',
          className: 'btn btn-sm btn-outline-secondary',
          exportOptions: { columns: ':visible' }
        },
        {
          extend: 'excel',
          text: '<i class="fas fa-file-excel mr-1"></i> Excel',
          className: 'btn btn-sm btn-outline-success',
          exportOptions: { columns: ':visible' }
        },
        {
          extend: 'pdf',
          text: '<i class="fas fa-file-pdf mr-1"></i> PDF',
          className: 'btn btn-sm btn-outline-danger',
          exportOptions: { columns: ':visible' },
          title: 'Attendance Report - NITRA TECHNICAL CAMPUS'
        },
        {
          extend: 'colvis',
          text: '<i class="fas fa-columns mr-1"></i> Columns',
          className: 'btn btn-sm btn-outline-primary'
        }
      ]
    });

    // Bootstrap form validation with SweetAlert2
    (function () {
      'use strict';
      var forms = document.querySelectorAll('.needs-validation');
      Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
          if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
            Swal.fire({
              icon: 'error',
              title: 'Validation Error',
              text: 'Please fill out all required fields correctly.',
            });
          } else if (form.id === 'attendanceForm') {
            event.preventDefault();
            Swal.fire({
              title: 'Save Attendance',
              text: 'Are you sure you want to save the attendance changes?',
              icon: 'question',
              showCancelButton: true,
              confirmButtonColor: '#3085d6',
              cancelButtonColor: '#d33',
              confirmButtonText: 'Yes, save it!'
            }).then((result) => {
              if (result.isConfirmed) {
                form.submit();
              }
            });
          }
          form.classList.add('was-validated');
        }, false);
      });
    })();
  });
</script>
{% endblock %}
