{% extends "base.html" %}
{% block title %}Student List | College ERP{% endblock %}
{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}
{% block navbar %}{% include "partials/navbar.html" %}{% endblock %}
{% block sidebar %}{% include "partials/sidebar_hod.html" %}{% endblock %}

{% block content %}
<!-- Page Header -->
{% with page_title="Student List" icon_class="fas fa-user-graduate" show_actions=False %}
  {% include "partials/page_header.html" %}
{% endwith %}

<!-- Filters -->
<div class="row">
  <div class="col-md-12">
    <div class="card card-primary card-outline shadow-sm">
<div class="card-header">
  <h3 class="card-title font-weight-bold">Filter Students</h3>
</div>
<form method="get" id="studentFilterForm" class="needs-validation" novalidate>
  <div class="card-body">
    <div class="row">
      <div class="col-md-2">
        <div class="form-group">
          <label for="branch">Branch</label>
          <select name="branch" id="branch" class="form-control mb-2">
            <option value="">-- All Branches --</option>
            {% for branch in hod_branches %}
              <option value="{{ branch.id }}" {% if branch.id|stringformat:"s" == selected_branch_id %}selected{% endif %}>
                {{ branch.name }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="semester">Semester</label>
          <select name="semester" id="semester" class="form-control mb-2">
            <option value="">-- All Semesters --</option>
            {% for semester in semesters %}
              <option value="{{ semester.id }}" {% if semester.id|stringformat:"s" == selected_semester_id %}selected{% endif %}>
                Semester {{ semester.number }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="section">Section</label>
          <select name="section" id="section" class="form-control mb-2">
            <option value="">-- All Sections --</option>
            {% for section in sections %}
              <option value="{{ section.id }}" {% if section.id|stringformat:"s" == selected_section_id %}selected{% endif %}>
                {{ section.name }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="year">Year</label>
          <select name="year" id="year" class="form-control mb-2">
            <option value="">-- All Years --</option>
            {% for year in years %}
              <option value="{{ year.id }}" {% if year.id|stringformat:"s" == selected_year_id %}selected{% endif %}>
                Year {{ year.number }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="fee_status">Fee Status</label>
          <select name="fee_status" id="fee_status" class="form-control mb-2">
            <option value="">-- All Status --</option>
            {% for status_code, status_display in fee_status_choices %}
              <option value="{{ status_code }}" {% if status_code == fee_status_filter %}selected{% endif %}>
                {{ status_display }}
              </option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <label for="search">Search</label>
          <input type="text" name="search" id="search" class="form-control mb-2"
                 placeholder="Name, Email, Roll No..." value="{{ search_query }}">
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <button type="submit" class="btn btn-primary" id="filterStudentsBtn">
          <i class="fas fa-search mr-1"></i> Filter Students
        </button>
        <button type="button" class="btn btn-secondary ml-2" onclick="resetForm()">
          <i class="fas fa-undo mr-1"></i> Reset
        </button>
        <button type="button" class="btn btn-info ml-2" onclick="showAllStudents()">
          <i class="fas fa-users mr-1"></i> Show All Students
        </button>
      </div>
    </div>
  </div>
</form>
    </div>
  </div>
</div>

<!-- Student List -->
{% if student_data %}
<div class="row">
  <div class="col-md-12">
    <div class="card card-success card-outline shadow-sm">
<div class="card-header d-flex align-items-center">
  <h3 class="card-title font-weight-bold">
    Student List
    {% if selected_branch %}({{ selected_branch.name }}{% endif %}
    {% if selected_semester %} - Semester {{ selected_semester.number }}{% endif %}
    {% if selected_section %} - {{ selected_section.name }}{% endif %}
    {% if selected_year %} - Year {{ selected_year.number }}{% endif %}
    {% if selected_branch %}){% endif %}
  </h3>
  {% if total_students %}
  <div class="card-tools">
    <span class="badge badge-success mr-2">{{ total_students }} Student{{ total_students|pluralize }}</span>
    {% if fee_summary %}
      {% for status, count in fee_summary.items %}
        <span class="badge badge-{% if status == 'confirmed' %}success{% elif status == 'partial' %}warning{% else %}danger{% endif %} mr-1">
          {{ count }} {{ status|title }}
        </span>
      {% endfor %}
    {% endif %}
  </div>
  {% endif %}
</div>
<div class="card-body table-responsive">
  <table id="studentTable" class="table table-bordered table-hover table-striped mb-0">
    <thead class="thead-dark">
      <tr>
        <th>Roll Number</th>
        <th>Name</th>
        <th>Email</th>
        <th>Branch</th>
        <th>Year</th>
        <th>Semester</th>
        <th>Section</th>
        <th>Fee Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for data in student_data %}
      <tr>
        <td>{{ data.student.roll_number|default:"-" }}</td>
        <td>{{ data.student.user.get_full_name|default:"-" }}</td>
        <td>{{ data.student.user.email|default:"-" }}</td>
        <td>{{ data.student.branch.name|default:"-" }}</td>
        <td>{{ data.student.current_year.number|default:"-" }}</td>
        <td>{{ data.student.current_semester.number|default:"-" }}</td>
        <td>{{ data.student.section.name|default:"-" }}</td>
        <td>
          <span class="badge badge-{% if data.fee_status_code == 'confirmed' %}success{% elif data.fee_status_code == 'partial' %}warning{% else %}danger{% endif %}">
            {{ data.fee_status }}
          </span>
        </td>
        <td>
          <a href="{% url 'hod_fee_status' %}?student={{ data.student.pk }}" class="btn btn-sm btn-info">
            <i class="fas fa-money-check-alt"></i> Fee Details
          </a>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
    </div>
  </div>
</div>
{% elif selected_branch_id or selected_semester_id or selected_section_id or selected_year_id or fee_status_filter or search_query %}
<div class="alert alert-info mt-4">
  <i class="fas fa-info-circle mr-2"></i>
  No students found for the selected criteria. Try adjusting your filters.
</div>
{% else %}
<div class="alert alert-light mt-4">
  <i class="fas fa-info-circle mr-2"></i>
  <strong>Welcome to Student List!</strong><br>
  Select any combination of filters above to view student data:
  <ul class="mb-0 mt-2">
    <li>Choose specific <strong>Branch, Semester, Section, or Year</strong> to narrow down results</li>
    <li>Filter by <strong>Fee Status</strong> to see payment information</li>
    <li>Use <strong>Search</strong> to find specific students by name, email, or roll number</li>
    <li>Leave filters empty and click "Show All Students" to view all students</li>
  </ul>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
  $(document).ready(function () {
    // Initialize form state
    // Enable semester dropdown if branch is already selected
    if ($('#branch').val()) {
      loadSemesters($('#branch').val());
    }

    // Enable section dropdown if semester is already selected
    if ($('#semester').val() && $('#branch').val()) {
      loadSections($('#branch').val(), $('#semester').val());
    }

    // Branch change handler
    $('#branch').on('change', function() {
      const branchId = $(this).val();

      // Reset dependent dropdowns but keep them enabled
      resetDependentDropdowns(['semester', 'section'], false);

      if (branchId) {
        loadSemesters(branchId);
      }
    });

    // Semester change handler
    $('#semester').on('change', function() {
      const semesterId = $(this).val();
      const branchId = $('#branch').val();

      // Reset dependent dropdowns but keep them enabled
      resetDependentDropdowns(['section'], false);

      if (semesterId && branchId) {
        loadSections(branchId, semesterId);
      }
    });

    // Function to load semesters based on branch
    function loadSemesters(branchId) {
      $.get('{% url "ajax_get_student_semesters" %}', {
        branch_id: branchId
      })
      .done(function(data) {
        const semesterSelect = $('#semester');
        semesterSelect.empty().append('<option value="">-- All Semesters --</option>');

        if (data.semesters && data.semesters.length > 0) {
          $.each(data.semesters, function(index, semester) {
            semesterSelect.append(`<option value="${semester.id}">Semester ${semester.number}</option>`);
          });

          // Restore selected value if exists
          const selectedSemester = '{{ selected_semester_id }}';
          if (selectedSemester) {
            semesterSelect.val(selectedSemester);
          }
        }
      })
      .fail(function() {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load semesters. Please try again.',
        });
      });
    }

    // Function to load sections based on branch and semester
    function loadSections(branchId, semesterId) {
      $.get('{% url "ajax_get_student_sections" %}', {
        branch_id: branchId,
        semester_id: semesterId
      })
      .done(function(data) {
        const sectionSelect = $('#section');
        sectionSelect.empty().append('<option value="">-- All Sections --</option>');

        if (data.sections && data.sections.length > 0) {
          $.each(data.sections, function(index, section) {
            sectionSelect.append(`<option value="${section.id}">${section.name}</option>`);
          });

          // Restore selected value if exists
          const selectedSection = '{{ selected_section_id }}';
          if (selectedSection) {
            sectionSelect.val(selectedSection);
          }
        }
      })
      .fail(function() {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load sections. Please try again.',
        });
      });
    }

    // Function to reset dependent dropdowns
    function resetDependentDropdowns(dropdowns, disable = true) {
      dropdowns.forEach(function(dropdown) {
        const label = dropdown === 'semester' ? 'All Semesters' :
                     dropdown === 'section' ? 'All Sections' :
                     `All ${dropdown.charAt(0).toUpperCase() + dropdown.slice(1)}s`;
        $(`#${dropdown}`).empty().append(`<option value="">-- ${label} --</option>`);
        if (disable) {
          $(`#${dropdown}`).prop('disabled', true);
        }
      });
    }

    // Reset form function
    window.resetForm = function() {
      $('#studentFilterForm')[0].reset();
      resetDependentDropdowns(['semester', 'section'], false);
    };

    // Show all students function
    window.showAllStudents = function() {
      // Set all filters to show all and submit
      $('#branch').val('');
      $('#semester').val('');
      $('#section').val('');
      $('#year').val('');
      $('#fee_status').val('');
      $('#search').val('');
      resetDependentDropdowns(['semester', 'section'], false);

      // Add a special parameter to show all students
      const form = $('#studentFilterForm');
      $('<input>').attr({
        type: 'hidden',
        name: 'show_all',
        value: 'true'
      }).appendTo(form);

      form.submit();
    };

    // Initialize DataTable using the global helper function with export buttons
    initDataTable('#studentTable', {
      "pageLength": 10,
      "order": [[0, "asc"]], // Sort by Roll Number
      "columnDefs": [
        { "orderable": false, "targets": 8 } // Disable sorting on actions column
      ],
      "dom": 'Bfrtip',
      "buttons": [
        {
          extend: 'csv',
          text: '<i class="fas fa-file-csv mr-1"></i> CSV',
          className: 'btn btn-sm btn-outline-secondary',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7] // Exclude actions column
          }
        },
        {
          extend: 'excel',
          text: '<i class="fas fa-file-excel mr-1"></i> Excel',
          className: 'btn btn-sm btn-outline-success',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7] // Exclude actions column
          }
        },
        {
          extend: 'pdf',
          text: '<i class="fas fa-file-pdf mr-1"></i> PDF',
          className: 'btn btn-sm btn-outline-danger',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7] // Exclude actions column
          },
          title: 'Student List - NITRA TECHNICAL CAMPUS'
        },
        {
          extend: 'colvis',
          text: '<i class="fas fa-columns mr-1"></i> Columns',
          className: 'btn btn-sm btn-outline-primary'
        }
      ]
    });
  });
</script>
{% endblock %}
