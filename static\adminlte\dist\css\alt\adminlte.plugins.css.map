{"version": 3, "sources": ["../../../build/scss/parts/adminlte.plugins.scss", "adminlte.plugins.css", "../../../build/scss/mixins/_animations.scss", "../../../build/scss/plugins/_fullcalendar.scss", "../../../build/scss/_bootstrap-variables.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "../../../build/scss/mixins/_miscellaneous.scss", "../../../node_modules/bootstrap/scss/mixins/_box-shadow.scss", "../../../build/scss/_variables.scss", "../../../build/scss/plugins/_select2.scss", "../../../build/scss/plugins/_mixins.scss", "../../../build/scss/mixins/_dark-mode.scss", "../../../build/scss/_variables-alt.scss", "../../../build/scss/plugins/_bootstrap-slider.scss", "../../../build/scss/plugins/_icheck-bootstrap.scss", "../../../build/scss/plugins/_mapael.scss", "../../../node_modules/bootstrap/scss/mixins/_reset-text.scss", "../../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../../build/scss/plugins/_jqvmap.scss", "../../../build/scss/plugins/_sweetalert2.scss", "../../../build/scss/plugins/_toastr.scss", "../../../build/scss/plugins/_pace.scss", "../../../build/scss/plugins/_bootstrap-switch.scss", "../../../build/scss/plugins/_daterangepicker.scss", "../../../build/scss/plugins/_miscellaneous.scss"], "names": [], "mappings": "AAAA;;;;;;ECME;ACDF;EACE;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,mCAAmC;IACnC,UAAU;EDGZ;ECAA;IACE,+DAAuD;IAAvD,uDAAuD;IACvD,mCAAmC;EDErC;ECCA;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,UAAU;EDCZ;ECEA;IACE,8DAAsD;IAAtD,sDAAsD;EDAxD;ECGA;IACE,qCAA6B;IAA7B,6BAA6B;EDD/B;AACF;ACtBA;EACE;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,mCAAmC;IACnC,UAAU;EDGZ;ECAA;IACE,+DAAuD;IAAvD,uDAAuD;IACvD,mCAAmC;EDErC;ECCA;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,UAAU;EDCZ;ECEA;IACE,8DAAsD;IAAtD,sDAAsD;EDAxD;ECGA;IACE,qCAA6B;IAA7B,6BAA6B;EDD/B;AACF;;ACKA;EACE;IACE,UAAU;EDFZ;ECKA;IACE,UAAU;EDHZ;AACF;;ACJA;EACE;IACE,UAAU;EDFZ;ECKA;IACE,UAAU;EDHZ;AACF;;ACMA;EACE;IACE,UAAU;EDHZ;ECMA;IACE,UAAU;EDJZ;AACF;;ACHA;EACE;IACE,UAAU;EDHZ;ECMA;IACE,UAAU;EDJZ;AACF;;ACOA;EACE;IACE,mDAA2C;IAA3C,2CAA2C;EDJ7C;ECMA;IACE,sDAA8C;IAA9C,8CAA8C;EDJhD;ECMA;IACE,kDAA0C;IAA1C,0CAA0C;EDJ5C;ECMA;IACE,iDAAyC;IAAzC,yCAAyC;EDJ3C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;ECMA;IACE,mDAA2C;IAA3C,2CAA2C;EDJ7C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;AACF;;AC7BA;EACE;IACE,mDAA2C;IAA3C,2CAA2C;EDJ7C;ECMA;IACE,sDAA8C;IAA9C,8CAA8C;EDJhD;ECMA;IACE,kDAA0C;IAA1C,0CAA0C;EDJ5C;ECMA;IACE,iDAAyC;IAAzC,yCAAyC;EDJ3C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;ECMA;IACE,mDAA2C;IAA3C,2CAA2C;EDJ7C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;AACF;;ACOA;EACE;IACE,uBAAe;IAAf,eAAe;EDJjB;ECOA;IACE,mEAA2D;IAA3D,2DAA2D;EDL7D;ECQA;IACE,iEAAyD;IAAzD,yDAAyD;EDN3D;ECSA;IACE,mEAA2D;IAA3D,2DAA2D;EDP7D;ECUA;IACE,iEAAyD;IAAzD,yDAAyD;EDR3D;ECWA;IACE,kEAA0D;IAA1D,0DAA0D;EDT5D;ECYA;IACE,uBAAe;IAAf,eAAe;EDVjB;AACF;;ACjBA;EACE;IACE,uBAAe;IAAf,eAAe;EDJjB;ECOA;IACE,mEAA2D;IAA3D,2DAA2D;EDL7D;ECQA;IACE,iEAAyD;IAAzD,yDAAyD;EDN3D;ECSA;IACE,mEAA2D;IAA3D,2DAA2D;EDP7D;ECUA;IACE,iEAAyD;IAAzD,yDAAyD;EDR3D;ECWA;IACE,kEAA0D;IAA1D,0DAA0D;EDT5D;ECYA;IACE,uBAAe;IAAf,eAAe;EDVjB;AACF;;AEpGA;EACE,mBCMgB;EDLhB,sBAAsB;EACtB,yBAAyB;EACzB,kBAAkB;EAClB,cCQgB;AH+FlB;;AE5GA;EAUI,yBAAyB;AFsG7B;;AEjGA;EACE,WAAW;EACX,eAAe;EACf,kBAAkB;EAClB,iBAAiB;AFoGnB;;AEjGA;EACE,mBAAmB;AFoGrB;;AEjGA;EACE,kBAAkB;AFoGpB;;AEhGA;EACE,mBAAmB;AFmGrB;;AEhGA;EACE,SAAS;EACT,WAAW;AFmGb;;AEhGA;;EAEE,cAAc;EACd,eAAe;AFmGjB;;AEhGA;;EAEE,eAAe;AFmGjB;;AEhGA;;EAEE,SAAS;EACT,aAAa;AFmGf;;AIrFI;EFVF;IACE,0BAAsB;IAAtB,sBAAsB;EFmGxB;EEpGA;IAII,iBAAQ;IAAR,QAAQ;IACR,oBAAoB;EFmGxB;EExGA;IASI,iBAAQ;IAAR,QAAQ;IACR,sBAAsB;EFkG1B;EE5GA;IAcI,iBAAQ;IAAR,QAAQ;EFiGZ;AACF;;AE7FA;EACE,eAAe;EACf,gBAAgB;EAChB,mBAAmB;AFgGrB;;AE7FA;EACE,gBAAgB;EAChB,SAAS;EACT,UAAU;AFgGZ;;AEnGA;EAMI,WAAW;EACX,eAAe;EACf,iBAAiB;EACjB,iBAAiB;AFiGrB;;AE1GA;;;;;;;;EAmBM,wCAAgC;EAAhC,gCAAgC;EAAhC,8DAAgC;AFkGtC;;AErHA;;;;;;;;EGrEE,gCAAyB;EAAzB,wBAAyB;ALqM3B;;AEpGA;EACE,0BAA0B;AFuG5B;;AEpGA;EIxGM,sECsIgE;EL3BpE,sBCoGkC;EDnGlC,YAAY;EACZ,gBAAgB;EAChB,kBAAkB;EAClB,iBAAiB;AFsGnB;;AE7GA;EIxGM,6CJkHmD;AFuGzD;;AQ3NA;EAEI,yBLDc;EKGd,2BLiUgC;EKhUhC,2BL+Z0F;AHnM9F;;AQjOA;EAUM,qBAAoC;AR2N1C;;AQrOA;EAeI,yBLdc;AHwOlB;;AQzOA;EAoBI,iBAAiB;EACjB,yBAAiB;EAAjB,sBAAiB;EAAjB,qBAAiB;EAAjB,iBAAiB;ARyNrB;;AQ9OA;EAyBI,eAAe;EAEf,YAAY;EACZ,gBAAgB;ARwNpB;;AQpPA;EAgCI,kBAAkB;EAClB,kBAAkB;ARwNtB;;AQzPA;EAqCI,YAAY;EACZ,UAAU;ARwNd;;AQ9PA;EA0CI,aAAa;ARwNjB;;AQlQA;;EAgDM,yBL/CY;AHsQlB;;AQvQA;;EAmDQ,aAAa;EACb,yBLmWkE;AH1I1E;;AQ7QA;EA2DM,aAAa;ARsNnB;;AQjRA;EA+DM,gBAAgB;ARsNtB;;AQrRA;EAqEM,cLlEY;AHsRlB;;AQzRA;EA2EM,yBL3EY;AH6RlB;;AQ7RA;EA+EQ,cLAe;AHkNvB;;AQjSA;EAsFI,yBLhEa;EKiEb,WL1FW;AHySf;;AQtSA;EA8FQ,yBAJwB;EAKxB,WLlGO;AH8Sf;;AQ3SA;EAuGM,yBLtGY;EKuGZ,+BL4TwF;AHpH9F;;AQhTA;EA2GQ,qBL4SkE;AHnG1E;;AQpTA;EA+GQ,4BLqN6B;EKpN7B,wBAAsC;ARyM9C;;AQzTA;EAmHU,WAAW;EACX,qBAAmC;AR0M7C;;AQ9TA;EAuHY,sBAAsB;AR2MlC;;AQlUA;EA8HY,SAAS;EACT,eAAe;ARwM3B;;AQvUA;EAqIQ,yBL/GS;EKgHT,qBAAkC;EAClC,WL1IO;EK2IP,eAAe;EACf,kBAAkB;ARsM1B;;AQ/UA;EA6IQ,+BAA+B;EAC/B,YAAY;EACZ,gBAAgB;EAChB,kBAAkB;ARsM1B;;AQtVA;EAmJU,WLtJK;AH6Vf;;AQnMM;EAIM,eAAe;ARmM3B;;AQvMM;EASI,iBAAiB;ARkM3B;;AQlWA;;EAwKQ,qBL+OkE;AHhD1E;;AQvWA;EA4KQ,SAAS;AR+LjB;;AQ3WA;EAkLI,mBAAmB;AR6LvB;;AQ1LE;EAEI,4BAA4B;EAC5B,yBAAyB;AR4L/B;;AQxLE;EAEI,6BAA6B;EAC7B,0BAA0B;AR0LhC;;AQpLA;EAEI,gBAAgB;ARsLpB;;AQjLA;EAEI,cAAc;ARmLlB;;AQ/KA;;EAIM,6BLiN2F;AHjCjG;;AQpLA;;EAOQ,kBAAkB;ARkL1B;;AQzLA;;EAWQ,YAAY;ARmLpB;;AQ9LA;;EAgBM,iCLqM2F;AHlBjG;;AQnMA;;EAmBQ,0BLuG4B;EKtG5B,mBAAsC;ARqL9C;;AQzMA;;EAuBU,oBAAsC;ARuLhD;;AQ9MA;;EA4BY,eAAe;ARuL3B;;AQ9KA;EACE,aAAa;ARiLf;;ASjbE;EAKQ,qBAAkC;ATgb5C;;ASrbE;EAUM,qBAAkC;AT+a1C;;AS3aI;;;;;;EAOQ,yBAAsD;AT6alE;;ASpbI;;EAaI,yBNGS;EMFT,WNvBO;AHmcf;;AS1bI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH0cf;;ASjcI;;EA6BQ,qBAAkC;ATya9C;;AStcI;;EAiCQ,yBNjBK;EMkBL,qBAAgC;EAChC,WN5CG;AHsdf;;AS7cI;;EAuCQ,+BNhDG;AH2df;;ASldI;;EA0CU,WNnDC;AHgef;;ASvdI;;EAgDM,qBAAkC;AT4a5C;;AS1eE;EAKQ,qBAAkC;ATye5C;;AS9eE;EAUM,qBAAkC;ATwe1C;;ASpeI;;;;;;EAOQ,yBAAsD;ATselE;;AS7eI;;EAaI,yBNhBU;EMiBV,WNvBO;AH4ff;;ASnfI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHmgBf;;AS1fI;;EA6BQ,qBAAkC;ATke9C;;AS/fI;;EAiCQ,yBNpCM;EMqCN,qBAAgC;EAChC,WN5CG;AH+gBf;;AStgBI;;EAuCQ,+BNhDG;AHohBf;;AS3gBI;;EA0CU,WNnDC;AHyhBf;;AShhBI;;EAgDM,qBAAkC;ATqe5C;;ASniBE;EAKQ,qBAAkC;ATkiB5C;;ASviBE;EAUM,qBAAkC;ATiiB1C;;AS7hBI;;;;;;EAOQ,yBAAsD;AT+hBlE;;AStiBI;;EAaI,yBNUS;EMTT,WNvBO;AHqjBf;;AS5iBI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH4jBf;;ASnjBI;;EA6BQ,qBAAkC;AT2hB9C;;ASxjBI;;EAiCQ,yBNVK;EMWL,qBAAgC;EAChC,WN5CG;AHwkBf;;AS/jBI;;EAuCQ,+BNhDG;AH6kBf;;ASpkBI;;EA0CU,WNnDC;AHklBf;;ASzkBI;;EAgDM,qBAAkC;AT8hB5C;;AS5lBE;EAKQ,qBAAkC;AT2lB5C;;AShmBE;EAUM,qBAAkC;AT0lB1C;;AStlBI;;;;;;EAOQ,yBAAsD;ATwlBlE;;AS/lBI;;EAaI,yBNYS;EMXT,WNvBO;AH8mBf;;ASrmBI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHqnBf;;AS5mBI;;EA6BQ,qBAAkC;ATolB9C;;ASjnBI;;EAiCQ,yBNRK;EMSL,qBAAgC;EAChC,WN5CG;AHioBf;;ASxnBI;;EAuCQ,+BNhDG;AHsoBf;;AS7nBI;;EA0CU,WNnDC;AH2oBf;;ASloBI;;EAgDM,qBAAkC;ATulB5C;;ASrpBE;EAKQ,qBAAkC;ATopB5C;;ASzpBE;EAUM,qBAAkC;ATmpB1C;;AS/oBI;;;;;;EAOQ,yBAAsD;ATipBlE;;ASxpBI;;EAaI,yBNSS;EMRT,cN2De;AHqlBvB;;AS9pBI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AH4lBvB;;ASrqBI;;EA6BQ,qBAAkC;AT6oB9C;;AS1qBI;;EAiCQ,yBNXK;EMYL,qBAAgC;EAChC,cNsCW;AHwmBvB;;ASjrBI;;EAuCQ,4BNkCW;AH6mBvB;;AStrBI;;EA0CU,cN+BS;AHknBvB;;AS3rBI;;EAgDM,qBAAkC;ATgpB5C;;AS9sBE;EAKQ,qBAAkC;AT6sB5C;;ASltBE;EAUM,qBAAkC;AT4sB1C;;ASxsBI;;;;;;EAOQ,yBAAsD;AT0sBlE;;ASjtBI;;EAaI,yBNOS;EMNT,WNvBO;AHguBf;;ASvtBI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHuuBf;;AS9tBI;;EA6BQ,qBAAkC;ATssB9C;;ASnuBI;;EAiCQ,yBNbK;EMcL,qBAAgC;EAChC,WN5CG;AHmvBf;;AS1uBI;;EAuCQ,+BNhDG;AHwvBf;;AS/uBI;;EA0CU,WNnDC;AH6vBf;;ASpvBI;;EAgDM,qBAAkC;ATysB5C;;ASvwBE;EAKQ,mBAAkC;ATswB5C;;AS3wBE;EAUM,mBAAkC;ATqwB1C;;ASjwBI;;;;;;EAOQ,uBAAsD;ATmwBlE;;AS1wBI;;EAaI,yBNrBU;EMsBV,cN2De;AHusBvB;;AShxBI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AH8sBvB;;ASvxBI;;EA6BQ,mBAAkC;AT+vB9C;;AS5xBI;;EAiCQ,yBNzCM;EM0CN,qBAAgC;EAChC,cNsCW;AH0tBvB;;ASnyBI;;EAuCQ,4BNkCW;AH+tBvB;;ASxyBI;;EA0CU,cN+BS;AHouBvB;;AS7yBI;;EAgDM,mBAAkC;ATkwB5C;;ASh0BE;EAKQ,qBAAkC;AT+zB5C;;ASp0BE;EAUM,qBAAkC;AT8zB1C;;AS1zBI;;;;;;EAOQ,yBAAsD;AT4zBlE;;ASn0BI;;EAaI,yBNdU;EMeV,WNvBO;AHk1Bf;;ASz0BI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHy1Bf;;ASh1BI;;EA6BQ,qBAAkC;ATwzB9C;;ASr1BI;;EAiCQ,yBNlCM;EMmCN,qBAAgC;EAChC,WN5CG;AHq2Bf;;AS51BI;;EAuCQ,+BNhDG;AH02Bf;;ASj2BI;;EA0CU,WNnDC;AH+2Bf;;ASt2BI;;EAgDM,qBAAkC;AT2zB5C;;ASz3BE;EAKQ,qBAAkC;ATw3B5C;;AS73BE;EAUM,qBAAkC;ATu3B1C;;ASn3BI;;;;;;EAOQ,yBAAsD;ATq3BlE;;AS53BI;;EAaI,yBF1BW;EE2BX,WNvBO;AH24Bf;;ASl4BI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHk5Bf;;ASz4BI;;EA6BQ,qBAAkC;ATi3B9C;;AS94BI;;EAiCQ,yBF9CO;EE+CP,qBAAgC;EAChC,WN5CG;AH85Bf;;ASr5BI;;EAuCQ,+BNhDG;AHm6Bf;;AS15BI;;EA0CU,WNnDC;AHw6Bf;;AS/5BI;;EAgDM,qBAAkC;ATo3B5C;;ASl7BE;EAKQ,qBAAkC;ATi7B5C;;ASt7BE;EAUM,qBAAkC;ATg7B1C;;AS56BI;;;;;;EAOQ,yBAAsD;AT86BlE;;ASr7BI;;EAaI,yBFzBM;EE0BN,WNvBO;AHo8Bf;;AS37BI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH28Bf;;ASl8BI;;EA6BQ,qBAAkC;AT06B9C;;ASv8BI;;EAiCQ,yBF7CE;EE8CF,qBAAgC;EAChC,WN5CG;AHu9Bf;;AS98BI;;EAuCQ,+BNhDG;AH49Bf;;ASn9BI;;EA0CU,WNnDC;AHi+Bf;;ASx9BI;;EAgDM,qBAAkC;AT66B5C;;AS3+BE;EAKQ,qBAAkC;AT0+B5C;;AS/+BE;EAUM,qBAAkC;ATy+B1C;;ASr+BI;;;;;;EAOQ,yBAAsD;ATu+BlE;;AS9+BI;;EAaI,yBFvBO;EEwBP,WNvBO;AH6/Bf;;ASp/BI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHogCf;;AS3/BI;;EA6BQ,qBAAkC;ATm+B9C;;AShgCI;;EAiCQ,yBF3CG;EE4CH,qBAAgC;EAChC,WN5CG;AHghCf;;ASvgCI;;EAuCQ,+BNhDG;AHqhCf;;AS5gCI;;EA0CU,WNnDC;AH0hCf;;ASjhCI;;EAgDM,qBAAkC;ATs+B5C;;ASpiCE;EAKQ,qBAAkC;ATmiC5C;;ASxiCE;EAUM,qBAAkC;ATkiC1C;;AS9hCI;;;;;;EAOQ,yBAAsD;ATgiClE;;ASviCI;;EAaI,yBFtBM;EEuBN,cN2De;AHo+BvB;;AS7iCI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AH2+BvB;;ASpjCI;;EA6BQ,qBAAkC;AT4hC9C;;ASzjCI;;EAiCQ,yBF1CE;EE2CF,qBAAgC;EAChC,cNsCW;AHu/BvB;;AShkCI;;EAuCQ,4BNkCW;AH4/BvB;;ASrkCI;;EA0CU,cN+BS;AHigCvB;;AS1kCI;;EAgDM,qBAAkC;AT+hC5C;;AS7lCE;EAKQ,qBAAkC;AT4lC5C;;ASjmCE;EAUM,qBAAkC;AT2lC1C;;ASvlCI;;;;;;EAOQ,yBAAsD;ATylClE;;AShmCI;;EAaI,yBFpBS;EEqBT,WNvBO;AH+mCf;;AStmCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHsnCf;;AS7mCI;;EA6BQ,qBAAkC;ATqlC9C;;ASlnCI;;EAiCQ,yBFxCK;EEyCL,qBAAgC;EAChC,WN5CG;AHkoCf;;ASznCI;;EAuCQ,+BNhDG;AHuoCf;;AS9nCI;;EA0CU,WNnDC;AH4oCf;;ASnoCI;;EAgDM,qBAAkC;ATwlC5C;;AStpCE;EAKQ,qBAAkC;ATqpC5C;;AS1pCE;EAUM,qBAAkC;ATopC1C;;AShpCI;;;;;;EAOQ,yBAAsD;ATkpClE;;ASzpCI;;EAaI,yBFlBQ;EEmBR,WNvBO;AHwqCf;;AS/pCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH+qCf;;AStqCI;;EA6BQ,qBAAkC;AT8oC9C;;AS3qCI;;EAiCQ,yBFtCI;EEuCJ,qBAAgC;EAChC,WN5CG;AH2rCf;;ASlrCI;;EAuCQ,+BNhDG;AHgsCf;;ASvrCI;;EA0CU,WNnDC;AHqsCf;;AS5rCI;;EAgDM,qBAAkC;ATipC5C;;AS/sCE;EAKQ,qBAAkC;AT8sC5C;;ASntCE;EAUM,qBAAkC;AT6sC1C;;ASzsCI;;;;;;EAOQ,yBAAsD;AT2sClE;;ASltCI;;EAaI,yBNGS;EMFT,WNvBO;AHiuCf;;ASxtCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHwuCf;;AS/tCI;;EA6BQ,qBAAkC;ATusC9C;;ASpuCI;;EAiCQ,yBNjBK;EMkBL,qBAAgC;EAChC,WN5CG;AHovCf;;AS3uCI;;EAuCQ,+BNhDG;AHyvCf;;AShvCI;;EA0CU,WNnDC;AH8vCf;;ASrvCI;;EAgDM,qBAAkC;AT0sC5C;;ASxwCE;EAKQ,qBAAkC;ATuwC5C;;AS5wCE;EAUM,qBAAkC;ATswC1C;;ASlwCI;;;;;;EAOQ,yBAAsD;ATowClE;;AS3wCI;;EAaI,yBNIS;EMHT,WNvBO;AH0xCf;;ASjxCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHiyCf;;ASxxCI;;EA6BQ,qBAAkC;ATgwC9C;;AS7xCI;;EAiCQ,yBNhBK;EMiBL,qBAAgC;EAChC,WN5CG;AH6yCf;;ASpyCI;;EAuCQ,+BNhDG;AHkzCf;;ASzyCI;;EA0CU,WNnDC;AHuzCf;;AS9yCI;;EAgDM,qBAAkC;ATmwC5C;;ASj0CE;EAKQ,qBAAkC;ATg0C5C;;ASr0CE;EAUM,qBAAkC;AT+zC1C;;AS3zCI;;;;;;EAOQ,yBAAsD;AT6zClE;;ASp0CI;;EAaI,yBNKS;EMJT,WNvBO;AHm1Cf;;AS10CI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH01Cf;;ASj1CI;;EA6BQ,qBAAkC;ATyzC9C;;ASt1CI;;EAiCQ,yBNfK;EMgBL,qBAAgC;EAChC,WN5CG;AHs2Cf;;AS71CI;;EAuCQ,+BNhDG;AH22Cf;;ASl2CI;;EA0CU,WNnDC;AHg3Cf;;ASv2CI;;EAgDM,qBAAkC;AT4zC5C;;AS13CE;EAKQ,qBAAkC;ATy3C5C;;AS93CE;EAUM,qBAAkC;ATw3C1C;;ASp3CI;;;;;;EAOQ,yBAAsD;ATs3ClE;;AS73CI;;EAaI,yBNMS;EMLT,WNvBO;AH44Cf;;ASn4CI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHm5Cf;;AS14CI;;EA6BQ,qBAAkC;ATk3C9C;;AS/4CI;;EAiCQ,yBNdK;EMeL,qBAAgC;EAChC,WN5CG;AH+5Cf;;ASt5CI;;EAuCQ,+BNhDG;AHo6Cf;;AS35CI;;EA0CU,WNnDC;AHy6Cf;;ASh6CI;;EAgDM,qBAAkC;ATq3C5C;;ASn7CE;EAKQ,qBAAkC;ATk7C5C;;ASv7CE;EAUM,qBAAkC;ATi7C1C;;AS76CI;;;;;;EAOQ,yBAAsD;AT+6ClE;;ASt7CI;;EAaI,yBNOS;EMNT,WNvBO;AHq8Cf;;AS57CI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH48Cf;;ASn8CI;;EA6BQ,qBAAkC;AT26C9C;;ASx8CI;;EAiCQ,yBNbK;EMcL,qBAAgC;EAChC,WN5CG;AHw9Cf;;AS/8CI;;EAuCQ,+BNhDG;AH69Cf;;ASp9CI;;EA0CU,WNnDC;AHk+Cf;;ASz9CI;;EAgDM,qBAAkC;AT86C5C;;AS5+CE;EAKQ,qBAAkC;AT2+C5C;;ASh/CE;EAUM,qBAAkC;AT0+C1C;;ASt+CI;;;;;;EAOQ,yBAAsD;ATw+ClE;;AS/+CI;;EAaI,yBNQS;EMPT,cN2De;AH46CvB;;ASr/CI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHqgDf;;AS5/CI;;EA6BQ,qBAAkC;ATo+C9C;;ASjgDI;;EAiCQ,yBNZK;EMaL,qBAAgC;EAChC,cNsCW;AH+7CvB;;ASxgDI;;EAuCQ,4BNkCW;AHo8CvB;;AS7gDI;;EA0CU,cN+BS;AHy8CvB;;ASlhDI;;EAgDM,qBAAkC;ATu+C5C;;ASriDE;EAKQ,qBAAkC;AToiD5C;;ASziDE;EAUM,qBAAkC;ATmiD1C;;AS/hDI;;;;;;EAOQ,yBAAsD;ATiiDlE;;ASxiDI;;EAaI,yBNSS;EMRT,cN2De;AHq+CvB;;AS9iDI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AH4+CvB;;ASrjDI;;EA6BQ,qBAAkC;AT6hD9C;;AS1jDI;;EAiCQ,yBNXK;EMYL,qBAAgC;EAChC,cNsCW;AHw/CvB;;ASjkDI;;EAuCQ,4BNkCW;AH6/CvB;;AStkDI;;EA0CU,cN+BS;AHkgDvB;;AS3kDI;;EAgDM,qBAAkC;ATgiD5C;;AS9lDE;EAKQ,qBAAkC;AT6lD5C;;ASlmDE;EAUM,qBAAkC;AT4lD1C;;ASxlDI;;;;;;EAOQ,yBAAsD;AT0lDlE;;ASjmDI;;EAaI,yBNUS;EMTT,WNvBO;AHgnDf;;ASvmDI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHunDf;;AS9mDI;;EA6BQ,qBAAkC;ATslD9C;;ASnnDI;;EAiCQ,yBNVK;EMWL,qBAAgC;EAChC,WN5CG;AHmoDf;;AS1nDI;;EAuCQ,+BNhDG;AHwoDf;;AS/nDI;;EA0CU,WNnDC;AH6oDf;;ASpoDI;;EAgDM,qBAAkC;ATylD5C;;ASvpDE;EAKQ,qBAAkC;ATspD5C;;AS3pDE;EAUM,qBAAkC;ATqpD1C;;ASjpDI;;;;;;EAOQ,yBAAsD;ATmpDlE;;AS1pDI;;EAaI,yBNWS;EMVT,WNvBO;AHyqDf;;AShqDI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHgrDf;;ASvqDI;;EA6BQ,qBAAkC;AT+oD9C;;AS5qDI;;EAiCQ,yBNTK;EMUL,qBAAgC;EAChC,WN5CG;AH4rDf;;ASnrDI;;EAuCQ,+BNhDG;AHisDf;;ASxrDI;;EA0CU,WNnDC;AHssDf;;AS7rDI;;EAgDM,qBAAkC;ATkpD5C;;AShtDE;EAKQ,qBAAkC;AT+sD5C;;ASptDE;EAUM,qBAAkC;AT8sD1C;;AS1sDI;;;;;;EAOQ,yBAAsD;AT4sDlE;;ASntDI;;EAaI,yBNYS;EMXT,WNvBO;AHkuDf;;ASztDI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHyuDf;;AShuDI;;EA6BQ,qBAAkC;ATwsD9C;;ASruDI;;EAiCQ,yBNRK;EMSL,qBAAgC;EAChC,WN5CG;AHqvDf;;AS5uDI;;EAuCQ,+BNhDG;AH0vDf;;ASjvDI;;EA0CU,WNnDC;AH+vDf;;AStvDI;;EAgDM,qBAAkC;AT2sD5C;;ASzwDE;EAKQ,mBAAkC;ATwwD5C;;AS7wDE;EAUM,mBAAkC;ATuwD1C;;ASnwDI;;;;;;EAOQ,uBAAsD;ATqwDlE;;AS5wDI;;EAaI,sBNtBO;EMuBP,cN2De;AHysDvB;;ASlxDI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AHgtDvB;;ASzxDI;;EA6BQ,mBAAkC;ATiwD9C;;AS9xDI;;EAiCQ,sBN1CG;EM2CH,qBAAgC;EAChC,cNsCW;AH4tDvB;;ASryDI;;EAuCQ,4BNkCW;AHiuDvB;;AS1yDI;;EA0CU,cN+BS;AHsuDvB;;AS/yDI;;EAgDM,mBAAkC;ATowD5C;;ASl0DE;EAKQ,qBAAkC;ATi0D5C;;ASt0DE;EAUM,qBAAkC;ATg0D1C;;AS5zDI;;;;;;EAOQ,yBAAsD;AT8zDlE;;ASr0DI;;EAaI,yBNhBU;EMiBV,WNvBO;AHo1Df;;AS30DI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH21Df;;ASl1DI;;EA6BQ,qBAAkC;AT0zD9C;;ASv1DI;;EAiCQ,yBNpCM;EMqCN,qBAAgC;EAChC,WN5CG;AHu2Df;;AS91DI;;EAuCQ,+BNhDG;AH42Df;;ASn2DI;;EA0CU,WNnDC;AHi3Df;;ASx2DI;;EAgDM,qBAAkC;AT6zD5C;;AS33DE;EAKQ,qBAAkC;AT03D5C;;AS/3DE;EAUM,qBAAkC;ATy3D1C;;ASr3DI;;;;;;EAOQ,yBAAsD;ATu3DlE;;AS93DI;;EAaI,yBNdU;EMeV,WNvBO;AH64Df;;ASp4DI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHo5Df;;AS34DI;;EA6BQ,qBAAkC;ATm3D9C;;ASh5DI;;EAiCQ,yBNlCM;EMmCN,qBAAgC;EAChC,WN5CG;AHg6Df;;ASv5DI;;EAuCQ,+BNhDG;AHq6Df;;AS55DI;;EA0CU,WNnDC;AH06Df;;ASj6DI;;EAgDM,qBAAkC;ATs3D5C;;AUp7DI;EF+QA,yBLlQc;EKmQd,qBLrQc;AH86DlB;;AUz7DI;EFoRA,yBAAsC;ARyqD1C;;AU77DI;EFwRA,yBL3Qc;EK4Qd,qBL9Qc;AHu7DlB;;AUl8DI;EF4RE,WLvRS;AHi8Df;;AUt8DI;;EFiSA,yBLpRc;EKqRd,qBLvRc;EKwRd,YAAY;AR0qDhB;;AU78DI;EFsSA,yBLzRc;EK0Rd,qBL5Rc;EK6Rd,YAAY;AR2qDhB;;AUn9DI;EF2SA,oCAA+C;EAC/C,cLpSc;AHg9DlB;;AUx9DI;EF+SA,6BAA6B;EAC7B,WL3SW;AHw9Df;;AU79DI;EFoTA,WL/SW;AH49Df;;AUj+DI;EDKM,qBAAkC;ATg+D5C;;AUr+DI;EDUI,qBAAkC;AT+9D1C;;AS39DI;;;;;;EAOQ,yBAAsD;AT69DlE;;ASp+DI;;EAaI,yBEEa;EFDb,WNvBO;AHm/Df;;AS1+DI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH0/Df;;ASj/DI;;EA6BQ,qBAAkC;ATy9D9C;;ASt/DI;;EAiCQ,yBElBS;EFmBT,qBAAgC;EAChC,WN5CG;AHsgEf;;AS7/DI;;EAuCQ,+BNhDG;AH2gEf;;ASlgEI;;EA0CU,WNnDC;AHghEf;;ASvgEI;;EAgDM,qBAAkC;AT49D5C;;AU1hEI;EDKM,qBAAkC;ATyhE5C;;AU9hEI;EDUI,qBAAkC;ATwhE1C;;ASphEI;;;;;;EAOQ,yBAAsD;ATshElE;;AS7hEI;;EAaI,yBNhBU;EMiBV,WNvBO;AH4iEf;;ASniEI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHmjEf;;AS1iEI;;EA6BQ,qBAAkC;ATkhE9C;;AS/iEI;;EAiCQ,yBNpCM;EMqCN,qBAAgC;EAChC,WN5CG;AH+jEf;;AStjEI;;EAuCQ,+BNhDG;AHokEf;;AS3jEI;;EA0CU,WNnDC;AHykEf;;AShkEI;;EAgDM,qBAAkC;ATqhE5C;;AUnlEI;EDKM,qBAAkC;ATklE5C;;AUvlEI;EDUI,qBAAkC;ATilE1C;;AS7kEI;;;;;;EAOQ,yBAAsD;AT+kElE;;AStlEI;;EAaI,yBESa;EFRb,WNvBO;AHqmEf;;AS5lEI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH4mEf;;ASnmEI;;EA6BQ,qBAAkC;AT2kE9C;;ASxmEI;;EAiCQ,yBEXS;EFYT,qBAAgC;EAChC,WN5CG;AHwnEf;;AS/mEI;;EAuCQ,+BNhDG;AH6nEf;;ASpnEI;;EA0CU,WNnDC;AHkoEf;;ASznEI;;EAgDM,qBAAkC;AT8kE5C;;AU5oEI;EDKM,qBAAkC;AT2oE5C;;AUhpEI;EDUI,qBAAkC;AT0oE1C;;AStoEI;;;;;;EAOQ,yBAAsD;ATwoElE;;AS/oEI;;EAaI,yBEWa;EFVb,WNvBO;AH8pEf;;ASrpEI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHqqEf;;AS5pEI;;EA6BQ,qBAAkC;ATooE9C;;ASjqEI;;EAiCQ,yBETS;EFUT,qBAAgC;EAChC,WN5CG;AHirEf;;ASxqEI;;EAuCQ,+BNhDG;AHsrEf;;AS7qEI;;EA0CU,WNnDC;AH2rEf;;ASlrEI;;EAgDM,qBAAkC;ATuoE5C;;AUrsEI;EDKM,qBAAkC;ATosE5C;;AUzsEI;EDUI,qBAAkC;ATmsE1C;;AS/rEI;;;;;;EAOQ,yBAAsD;ATisElE;;ASxsEI;;EAaI,yBEQa;EFPb,cN2De;AHqoEvB;;AS9sEI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AH4oEvB;;ASrtEI;;EA6BQ,qBAAkC;AT6rE9C;;AS1tEI;;EAiCQ,yBEZS;EFaT,qBAAgC;EAChC,cNsCW;AHwpEvB;;ASjuEI;;EAuCQ,4BNkCW;AH6pEvB;;AStuEI;;EA0CU,cN+BS;AHkqEvB;;AS3uEI;;EAgDM,qBAAkC;ATgsE5C;;AU9vEI;EDKM,qBAAkC;AT6vE5C;;AUlwEI;EDUI,qBAAkC;AT4vE1C;;ASxvEI;;;;;;EAOQ,yBAAsD;AT0vElE;;ASjwEI;;EAaI,yBEMa;EFLb,WNvBO;AHgxEf;;ASvwEI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHuxEf;;AS9wEI;;EA6BQ,qBAAkC;ATsvE9C;;ASnxEI;;EAiCQ,yBEdS;EFeT,qBAAgC;EAChC,WN5CG;AHmyEf;;AS1xEI;;EAuCQ,+BNhDG;AHwyEf;;AS/xEI;;EA0CU,WNnDC;AH6yEf;;ASpyEI;;EAgDM,qBAAkC;ATyvE5C;;AUvzEI;EDKM,mBAAkC;ATszE5C;;AU3zEI;EDUI,mBAAkC;ATqzE1C;;ASjzEI;;;;;;EAOQ,uBAAsD;ATmzElE;;AS1zEI;;EAaI,yBNrBU;EMsBV,cN2De;AHuvEvB;;ASh0EI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AH8vEvB;;ASv0EI;;EA6BQ,mBAAkC;AT+yE9C;;AS50EI;;EAiCQ,yBNzCM;EM0CN,qBAAgC;EAChC,cNsCW;AH0wEvB;;ASn1EI;;EAuCQ,4BNkCW;AH+wEvB;;ASx1EI;;EA0CU,cN+BS;AHoxEvB;;AS71EI;;EAgDM,mBAAkC;ATkzE5C;;AUh3EI;EDKM,qBAAkC;AT+2E5C;;AUp3EI;EDUI,qBAAkC;AT82E1C;;AS12EI;;;;;;EAOQ,yBAAsD;AT42ElE;;ASn3EI;;EAaI,yBNdU;EMeV,WNvBO;AHk4Ef;;ASz3EI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHy4Ef;;ASh4EI;;EA6BQ,qBAAkC;ATw2E9C;;ASr4EI;;EAiCQ,yBNlCM;EMmCN,qBAAgC;EAChC,WN5CG;AHq5Ef;;AS54EI;;EAuCQ,+BNhDG;AH05Ef;;ASj5EI;;EA0CU,WNnDC;AH+5Ef;;ASt5EI;;EAgDM,qBAAkC;AT22E5C;;AUz6EI;EDKM,qBAAkC;ATw6E5C;;AU76EI;EDUI,qBAAkC;ATu6E1C;;ASn6EI;;;;;;EAOQ,yBAAsD;ATq6ElE;;AS56EI;;EAaI,yBEc6B;EFb7B,cN2De;AHy2EvB;;ASl7EI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AHg3EvB;;ASz7EI;;EA6BQ,qBAAkC;ATi6E9C;;AS97EI;;EAiCQ,yBENyB;EFOzB,qBAAgC;EAChC,cNsCW;AH43EvB;;ASr8EI;;EAuCQ,4BNkCW;AHi4EvB;;AS18EI;;EA0CU,cN+BS;AHs4EvB;;AS/8EI;;EAgDM,qBAAkC;ATo6E5C;;AUl+EI;EDKM,qBAAkC;ATi+E5C;;AUt+EI;EDUI,qBAAkC;ATg+E1C;;AS59EI;;;;;;EAOQ,yBAAsD;AT89ElE;;ASr+EI;;EAaI,yBEeuB;EFdvB,WNvBO;AHo/Ef;;AS3+EI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH2/Ef;;ASl/EI;;EA6BQ,qBAAkC;AT09E9C;;ASv/EI;;EAiCQ,yBELmB;EFMnB,qBAAgC;EAChC,WN5CG;AHugFf;;AS9/EI;;EAuCQ,+BNhDG;AH4gFf;;ASngFI;;EA0CU,WNnDC;AHihFf;;ASxgFI;;EAgDM,qBAAkC;AT69E5C;;AU3hFI;EDKM,qBAAkC;AT0hF5C;;AU/hFI;EDUI,qBAAkC;ATyhF1C;;ASrhFI;;;;;;EAOQ,yBAAsD;ATuhFlE;;AS9hFI;;EAaI,yBEgByB;EFfzB,cN2De;AH29EvB;;ASpiFI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AHk+EvB;;AS3iFI;;EA6BQ,qBAAkC;ATmhF9C;;AShjFI;;EAiCQ,yBEJqB;EFKrB,qBAAgC;EAChC,cNsCW;AH8+EvB;;ASvjFI;;EAuCQ,4BNkCW;AHm/EvB;;AS5jFI;;EA0CU,cN+BS;AHw/EvB;;ASjkFI;;EAgDM,qBAAkC;ATshF5C;;AUplFI;EDKM,qBAAkC;ATmlF5C;;AUxlFI;EDUI,qBAAkC;ATklF1C;;AS9kFI;;;;;;EAOQ,yBAAsD;ATglFlE;;ASvlFI;;EAaI,yBEiBwB;EFhBxB,cN2De;AHohFvB;;AS7lFI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AH2hFvB;;ASpmFI;;EA6BQ,qBAAkC;AT4kF9C;;ASzmFI;;EAiCQ,yBEHoB;EFIpB,qBAAgC;EAChC,cNsCW;AHuiFvB;;AShnFI;;EAuCQ,4BNkCW;AH4iFvB;;ASrnFI;;EA0CU,cN+BS;AHijFvB;;AS1nFI;;EAgDM,qBAAkC;AT+kF5C;;AU7oFI;EDKM,qBAAkC;AT4oF5C;;AUjpFI;EDUI,qBAAkC;AT2oF1C;;ASvoFI;;;;;;EAOQ,yBAAsD;ATyoFlE;;AShpFI;;EAaI,yBEkB2B;EFjB3B,cN2De;AH6kFvB;;AStpFI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AHolFvB;;AS7pFI;;EA6BQ,qBAAkC;ATqoF9C;;ASlqFI;;EAiCQ,yBEFuB;EFGvB,qBAAgC;EAChC,cNsCW;AHgmFvB;;ASzqFI;;EAuCQ,4BNkCW;AHqmFvB;;AS9qFI;;EA0CU,cN+BS;AH0mFvB;;ASnrFI;;EAgDM,qBAAkC;ATwoF5C;;AUtsFI;EDKM,qBAAkC;ATqsF5C;;AU1sFI;EDUI,qBAAkC;ATosF1C;;AShsFI;;;;;;EAOQ,yBAAsD;ATksFlE;;ASzsFI;;EAaI,yBEmB0B;EFlB1B,cN2De;AHsoFvB;;AS/sFI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH+tFf;;ASttFI;;EA6BQ,qBAAkC;AT8rF9C;;AS3tFI;;EAiCQ,yBEDsB;EFEtB,qBAAgC;EAChC,cNsCW;AHypFvB;;ASluFI;;EAuCQ,4BNkCW;AH8pFvB;;ASvuFI;;EA0CU,cN+BS;AHmqFvB;;AS5uFI;;EAgDM,qBAAkC;ATisF5C;;AU/vFI;EDKM,qBAAkC;AT8vF5C;;AUnwFI;EDUI,qBAAkC;AT6vF1C;;ASzvFI;;;;;;EAOQ,yBAAsD;AT2vFlE;;ASlwFI;;EAaI,yBEEa;EFDb,WNvBO;AHixFf;;ASxwFI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHwxFf;;AS/wFI;;EA6BQ,qBAAkC;ATuvF9C;;ASpxFI;;EAiCQ,yBElBS;EFmBT,qBAAgC;EAChC,WN5CG;AHoyFf;;AS3xFI;;EAuCQ,+BNhDG;AHyyFf;;AShyFI;;EA0CU,WNnDC;AH8yFf;;ASryFI;;EAgDM,qBAAkC;AT0vF5C;;AUxzFI;EDKM,qBAAkC;ATuzF5C;;AU5zFI;EDUI,qBAAkC;ATszF1C;;ASlzFI;;;;;;EAOQ,yBAAsD;ATozFlE;;AS3zFI;;EAaI,yBEGa;EFFb,WNvBO;AH00Ff;;ASj0FI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHi1Ff;;ASx0FI;;EA6BQ,qBAAkC;ATgzF9C;;AS70FI;;EAiCQ,yBEjBS;EFkBT,qBAAgC;EAChC,WN5CG;AH61Ff;;ASp1FI;;EAuCQ,+BNhDG;AHk2Ff;;ASz1FI;;EA0CU,WNnDC;AHu2Ff;;AS91FI;;EAgDM,qBAAkC;ATmzF5C;;AUj3FI;EDKM,qBAAkC;ATg3F5C;;AUr3FI;EDUI,qBAAkC;AT+2F1C;;AS32FI;;;;;;EAOQ,yBAAsD;AT62FlE;;ASp3FI;;EAaI,yBEIa;EFHb,WNvBO;AHm4Ff;;AS13FI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH04Ff;;ASj4FI;;EA6BQ,qBAAkC;ATy2F9C;;ASt4FI;;EAiCQ,yBEhBS;EFiBT,qBAAgC;EAChC,WN5CG;AHs5Ff;;AS74FI;;EAuCQ,+BNhDG;AH25Ff;;ASl5FI;;EA0CU,WNnDC;AHg6Ff;;ASv5FI;;EAgDM,qBAAkC;AT42F5C;;AU16FI;EDKM,qBAAkC;ATy6F5C;;AU96FI;EDUI,qBAAkC;ATw6F1C;;ASp6FI;;;;;;EAOQ,yBAAsD;ATs6FlE;;AS76FI;;EAaI,yBEKa;EFJb,WNvBO;AH47Ff;;ASn7FI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHm8Ff;;AS17FI;;EA6BQ,qBAAkC;ATk6F9C;;AS/7FI;;EAiCQ,yBEfS;EFgBT,qBAAgC;EAChC,WN5CG;AH+8Ff;;ASt8FI;;EAuCQ,+BNhDG;AHo9Ff;;AS38FI;;EA0CU,WNnDC;AHy9Ff;;ASh9FI;;EAgDM,qBAAkC;ATq6F5C;;AUn+FI;EDKM,qBAAkC;ATk+F5C;;AUv+FI;EDUI,qBAAkC;ATi+F1C;;AS79FI;;;;;;EAOQ,yBAAsD;AT+9FlE;;ASt+FI;;EAaI,yBEMa;EFLb,WNvBO;AHq/Ff;;AS5+FI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH4/Ff;;ASn/FI;;EA6BQ,qBAAkC;AT29F9C;;ASx/FI;;EAiCQ,yBEdS;EFeT,qBAAgC;EAChC,WN5CG;AHwgGf;;AS//FI;;EAuCQ,+BNhDG;AH6gGf;;ASpgGI;;EA0CU,WNnDC;AHkhGf;;ASzgGI;;EAgDM,qBAAkC;AT89F5C;;AU5hGI;EDKM,qBAAkC;AT2hG5C;;AUhiGI;EDUI,qBAAkC;AT0hG1C;;ASthGI;;;;;;EAOQ,yBAAsD;ATwhGlE;;AS/hGI;;EAaI,yBEOa;EFNb,cN2De;AH49FvB;;ASriGI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHqjGf;;AS5iGI;;EA6BQ,qBAAkC;ATohG9C;;ASjjGI;;EAiCQ,yBEbS;EFcT,qBAAgC;EAChC,cNsCW;AH++FvB;;ASxjGI;;EAuCQ,4BNkCW;AHo/FvB;;AS7jGI;;EA0CU,cN+BS;AHy/FvB;;ASlkGI;;EAgDM,qBAAkC;ATuhG5C;;AUrlGI;EDKM,qBAAkC;ATolG5C;;AUzlGI;EDUI,qBAAkC;ATmlG1C;;AS/kGI;;;;;;EAOQ,yBAAsD;ATilGlE;;ASxlGI;;EAaI,yBEQa;EFPb,cN2De;AHqhGvB;;AS9lGI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AH4hGvB;;ASrmGI;;EA6BQ,qBAAkC;AT6kG9C;;AS1mGI;;EAiCQ,yBEZS;EFaT,qBAAgC;EAChC,cNsCW;AHwiGvB;;ASjnGI;;EAuCQ,4BNkCW;AH6iGvB;;AStnGI;;EA0CU,cN+BS;AHkjGvB;;AS3nGI;;EAgDM,qBAAkC;ATglG5C;;AU9oGI;EDKM,qBAAkC;AT6oG5C;;AUlpGI;EDUI,qBAAkC;AT4oG1C;;ASxoGI;;;;;;EAOQ,yBAAsD;AT0oGlE;;ASjpGI;;EAaI,yBESa;EFRb,WNvBO;AHgqGf;;ASvpGI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHuqGf;;AS9pGI;;EA6BQ,qBAAkC;ATsoG9C;;ASnqGI;;EAiCQ,yBEXS;EFYT,qBAAgC;EAChC,WN5CG;AHmrGf;;AS1qGI;;EAuCQ,+BNhDG;AHwrGf;;AS/qGI;;EA0CU,WNnDC;AH6rGf;;ASprGI;;EAgDM,qBAAkC;ATyoG5C;;AUvsGI;EDKM,qBAAkC;ATssG5C;;AU3sGI;EDUI,qBAAkC;ATqsG1C;;ASjsGI;;;;;;EAOQ,yBAAsD;ATmsGlE;;AS1sGI;;EAaI,yBEUa;EFTb,WNvBO;AHytGf;;AShtGI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHguGf;;ASvtGI;;EA6BQ,qBAAkC;AT+rG9C;;AS5tGI;;EAiCQ,yBEVS;EFWT,qBAAgC;EAChC,WN5CG;AH4uGf;;ASnuGI;;EAuCQ,+BNhDG;AHivGf;;ASxuGI;;EA0CU,WNnDC;AHsvGf;;AS7uGI;;EAgDM,qBAAkC;ATksG5C;;AUhwGI;EDKM,qBAAkC;AT+vG5C;;AUpwGI;EDUI,qBAAkC;AT8vG1C;;AS1vGI;;;;;;EAOQ,yBAAsD;AT4vGlE;;ASnwGI;;EAaI,yBEWa;EFVb,WNvBO;AHkxGf;;ASzwGI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHyxGf;;AShxGI;;EA6BQ,qBAAkC;ATwvG9C;;ASrxGI;;EAiCQ,yBETS;EFUT,qBAAgC;EAChC,WN5CG;AHqyGf;;AS5xGI;;EAuCQ,+BNhDG;AH0yGf;;ASjyGI;;EA0CU,WNnDC;AH+yGf;;AStyGI;;EAgDM,qBAAkC;AT2vG5C;;AUzzGI;EDKM,mBAAkC;ATwzG5C;;AU7zGI;EDUI,mBAAkC;ATuzG1C;;ASnzGI;;;;;;EAOQ,uBAAsD;ATqzGlE;;AS5zGI;;EAaI,sBNtBO;EMuBP,cN2De;AHyvGvB;;ASl0GI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AHgwGvB;;ASz0GI;;EA6BQ,mBAAkC;ATizG9C;;AS90GI;;EAiCQ,sBN1CG;EM2CH,qBAAgC;EAChC,cNsCW;AH4wGvB;;ASr1GI;;EAuCQ,4BNkCW;AHixGvB;;AS11GI;;EA0CU,cN+BS;AHsxGvB;;AS/1GI;;EAgDM,mBAAkC;ATozG5C;;AUl3GI;EDKM,qBAAkC;ATi3G5C;;AUt3GI;EDUI,qBAAkC;ATg3G1C;;AS52GI;;;;;;EAOQ,yBAAsD;AT82GlE;;ASr3GI;;EAaI,yBNhBU;EMiBV,WNvBO;AHo4Gf;;AS33GI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AH24Gf;;ASl4GI;;EA6BQ,qBAAkC;AT02G9C;;ASv4GI;;EAiCQ,yBNpCM;EMqCN,qBAAgC;EAChC,WN5CG;AHu5Gf;;AS94GI;;EAuCQ,+BNhDG;AH45Gf;;ASn5GI;;EA0CU,WNnDC;AHi6Gf;;ASx5GI;;EAgDM,qBAAkC;AT62G5C;;AU36GI;EDKM,qBAAkC;AT06G5C;;AU/6GI;EDUI,qBAAkC;ATy6G1C;;ASr6GI;;;;;;EAOQ,yBAAsD;ATu6GlE;;AS96GI;;EAaI,yBNdU;EMeV,WNvBO;AH67Gf;;ASp7GI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AHo8Gf;;AS37GI;;EA6BQ,qBAAkC;ATm6G9C;;ASh8GI;;EAiCQ,yBNlCM;EMmCN,qBAAgC;EAChC,WN5CG;AHg9Gf;;ASv8GI;;EAuCQ,+BNhDG;AHq9Gf;;AS58GI;;EA0CU,WNnDC;AH09Gf;;ASj9GI;;EAgDM,qBAAkC;ATs6G5C;;AYr+GA;EACE,YTyrB8B;AH+yFhC;;AYp+GA;EAEI,YAAY;AZs+GhB;;AYx+GA;EAKI,WAAW;AZu+Gf;;AYj+GE;EAEI,mBTaW;AHs9GjB;;AYr+GE;EAEI,mBTNY;AH6+GlB;;AYz+GE;EAEI,mBToBW;AHu9GjB;;AY7+GE;EAEI,mBTsBW;AHy9GjB;;AYj/GE;EAEI,mBTmBW;AHg+GjB;;AYr/GE;EAEI,mBTiBW;AHs+GjB;;AYz/GE;EAEI,mBTXY;AHsgHlB;;AY7/GE;EAEI,mBTJY;AHmgHlB;;AYz/GE;EAEI,mBLxBa;APmhHnB;;AY7/GE;EAEI,mBLvBQ;APshHd;;AYjgHE;EAEI,mBLrBS;APwhHf;;AYrgHE;EAEI,mBLpBQ;AP2hHd;;AYzgHE;EAEI,mBLlBW;AP6hHjB;;AY7gHE;EAEI,mBLhBU;AP+hHhB;;AYjhHE;EAEI,mBTKW;AH8gHjB;;AYrhHE;EAEI,mBTMW;AHihHjB;;AYzhHE;EAEI,mBTOW;AHohHjB;;AY7hHE;EAEI,mBTQW;AHuhHjB;;AYjiHE;EAEI,mBTSW;AH0hHjB;;AYriHE;EAEI,mBTUW;AH6hHjB;;AYziHE;EAEI,mBTWW;AHgiHjB;;AY7iHE;EAEI,mBTYW;AHmiHjB;;AYjjHE;EAEI,mBTaW;AHsiHjB;;AYrjHE;EAEI,mBTcW;AHyiHjB;;AYzjHE;EAEI,gBTpBS;AH+kHf;;AY7jHE;EAEI,mBTdY;AH6kHlB;;AYjkHE;EAEI,mBTZY;AH+kHlB;;AU5lHI;EEgCA,yBAAqC;EACrC,sBAAsB;AZgkH1B;;AUjmHI;EEuCI,mBDVa;AXwkHrB;;AUrmHI;EEuCI,mBT5BU;AH8lHlB;;AUzmHI;EEuCI,mBDHa;AXykHrB;;AU7mHI;EEuCI,mBDDa;AX2kHrB;;AUjnHI;EEuCI,mBDJa;AXklHrB;;AUrnHI;EEuCI,mBDNa;AXwlHrB;;AUznHI;EEuCI,mBTjCU;AHunHlB;;AU7nHI;EEuCI,mBT1BU;AHonHlB;;AUjoHI;EE+CI,mBDN6B;AX4lHrC;;AUroHI;EE+CI,mBDLuB;AX+lH/B;;AUzoHI;EE+CI,mBDJyB;AXkmHjC;;AU7oHI;EE+CI,mBDHwB;AXqmHhC;;AUjpHI;EE+CI,mBDF2B;AXwmHnC;;AUrpHI;EE+CI,mBDD0B;AX2mHlC;;AUzpHI;EE+CI,mBDlBa;AXgoHrB;;AU7pHI;EE+CI,mBDjBa;AXmoHrB;;AUjqHI;EE+CI,mBDhBa;AXsoHrB;;AUrqHI;EE+CI,mBDfa;AXyoHrB;;AUzqHI;EE+CI,mBDda;AX4oHrB;;AU7qHI;EE+CI,mBDba;AX+oHrB;;AUjrHI;EE+CI,mBDZa;AXkpHrB;;AUrrHI;EE+CI,mBDXa;AXqpHrB;;AUzrHI;EE+CI,mBDVa;AXwpHrB;;AU7rHI;EE+CI,mBDTa;AX2pHrB;;AUjsHI;EE+CI,gBT1CO;AHgsHf;;AUrsHI;EE+CI,mBTpCU;AH8rHlB;;AUzsHI;EE+CI,mBTlCU;AHgsHlB;;Aa7sHE;;EAEE,qBAAc;AbgtHlB;;Aa7sHE;;EAEE,qBAAc;AbgtHlB;;Aa7sHE;;EAEE,yBAAkB;EAClB,qBAAc;AbgtHlB;;Aa7tHE;;EAEE,qBAAc;AbguHlB;;Aa7tHE;;EAEE,qBAAc;AbguHlB;;Aa7tHE;;EAEE,yBAAkB;EAClB,qBAAc;AbguHlB;;Aa7uHE;;EAEE,qBAAc;AbgvHlB;;Aa7uHE;;EAEE,qBAAc;AbgvHlB;;Aa7uHE;;EAEE,yBAAkB;EAClB,qBAAc;AbgvHlB;;Aa7vHE;;EAEE,qBAAc;AbgwHlB;;Aa7vHE;;EAEE,qBAAc;AbgwHlB;;Aa7vHE;;EAEE,yBAAkB;EAClB,qBAAc;AbgwHlB;;Aa7wHE;;EAEE,qBAAc;AbgxHlB;;Aa7wHE;;EAEE,qBAAc;AbgxHlB;;Aa7wHE;;EAEE,yBAAkB;EAClB,qBAAc;AbgxHlB;;Aa7xHE;;EAEE,qBAAc;AbgyHlB;;Aa7xHE;;EAEE,qBAAc;AbgyHlB;;Aa7xHE;;EAEE,yBAAkB;EAClB,qBAAc;AbgyHlB;;Aa7yHE;;EAEE,qBAAc;AbgzHlB;;Aa7yHE;;EAEE,qBAAc;AbgzHlB;;Aa7yHE;;EAEE,yBAAkB;EAClB,qBAAc;AbgzHlB;;Aa7zHE;;EAEE,qBAAc;Abg0HlB;;Aa7zHE;;EAEE,qBAAc;Abg0HlB;;Aa7zHE;;EAEE,yBAAkB;EAClB,qBAAc;Abg0HlB;;Aa1zHE;;EAEE,qBAAc;Ab6zHlB;;Aa1zHE;;EAEE,qBAAc;Ab6zHlB;;Aa1zHE;;EAEE,yBAAkB;EAClB,qBAAc;Ab6zHlB;;Aa10HE;;EAEE,qBAAc;Ab60HlB;;Aa10HE;;EAEE,qBAAc;Ab60HlB;;Aa10HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab60HlB;;Aa11HE;;EAEE,qBAAc;Ab61HlB;;Aa11HE;;EAEE,qBAAc;Ab61HlB;;Aa11HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab61HlB;;Aa12HE;;EAEE,qBAAc;Ab62HlB;;Aa12HE;;EAEE,qBAAc;Ab62HlB;;Aa12HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab62HlB;;Aa13HE;;EAEE,qBAAc;Ab63HlB;;Aa13HE;;EAEE,qBAAc;Ab63HlB;;Aa13HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab63HlB;;Aa14HE;;EAEE,qBAAc;Ab64HlB;;Aa14HE;;EAEE,qBAAc;Ab64HlB;;Aa14HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab64HlB;;Aa15HE;;EAEE,qBAAc;Ab65HlB;;Aa15HE;;EAEE,qBAAc;Ab65HlB;;Aa15HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab65HlB;;Aa16HE;;EAEE,qBAAc;Ab66HlB;;Aa16HE;;EAEE,qBAAc;Ab66HlB;;Aa16HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab66HlB;;Aa17HE;;EAEE,qBAAc;Ab67HlB;;Aa17HE;;EAEE,qBAAc;Ab67HlB;;Aa17HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab67HlB;;Aa18HE;;EAEE,qBAAc;Ab68HlB;;Aa18HE;;EAEE,qBAAc;Ab68HlB;;Aa18HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab68HlB;;Aa19HE;;EAEE,qBAAc;Ab69HlB;;Aa19HE;;EAEE,qBAAc;Ab69HlB;;Aa19HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab69HlB;;Aa1+HE;;EAEE,qBAAc;Ab6+HlB;;Aa1+HE;;EAEE,qBAAc;Ab6+HlB;;Aa1+HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab6+HlB;;Aa1/HE;;EAEE,qBAAc;Ab6/HlB;;Aa1/HE;;EAEE,qBAAc;Ab6/HlB;;Aa1/HE;;EAEE,yBAAkB;EAClB,qBAAc;Ab6/HlB;;Aa1gIE;;EAEE,qBAAc;Ab6gIlB;;Aa1gIE;;EAEE,qBAAc;Ab6gIlB;;Aa1gIE;;EAEE,yBAAkB;EAClB,qBAAc;Ab6gIlB;;Aa1hIE;;EAEE,qBAAc;Ab6hIlB;;Aa1hIE;;EAEE,qBAAc;Ab6hIlB;;Aa1hIE;;EAEE,yBAAkB;EAClB,qBAAc;Ab6hIlB;;Aa1iIE;;EAEE,qBAAc;Ab6iIlB;;Aa1iIE;;EAEE,qBAAc;Ab6iIlB;;Aa1iIE;;EAEE,yBAAkB;EAClB,qBAAc;Ab6iIlB;;Aa1jIE;;EAEE,kBAAc;Ab6jIlB;;Aa1jIE;;EAEE,kBAAc;Ab6jIlB;;Aa1jIE;;EAEE,sBAAkB;EAClB,kBAAc;Ab6jIlB;;Aa1kIE;;EAEE,qBAAc;Ab6kIlB;;Aa1kIE;;EAEE,qBAAc;Ab6kIlB;;Aa1kIE;;EAEE,yBAAkB;EAClB,qBAAc;Ab6kIlB;;Aa1lIE;;EAEE,qBAAc;Ab6lIlB;;Aa1lIE;;EAEE,qBAAc;Ab6lIlB;;Aa1lIE;;EAEE,yBAAkB;EAClB,qBAAc;Ab6lIlB;;AU7nII;;EGwCE,qBV7BY;AHunIlB;;AUloII;;EG+CE,qBAAc;AbwlIpB;;AUvoII;;EGoDE,qBAAc;AbwlIpB;;AU5oII;;EGyDE,yBAAkB;EAClB,qBAAc;AbwlIpB;;AUlpII;;EG+CE,qBAAc;AbwmIpB;;AUvpII;;EGoDE,qBAAc;AbwmIpB;;AU5pII;;EGyDE,yBAAkB;EAClB,qBAAc;AbwmIpB;;AUlqII;;EG+CE,qBAAc;AbwnIpB;;AUvqII;;EGoDE,qBAAc;AbwnIpB;;AU5qII;;EGyDE,yBAAkB;EAClB,qBAAc;AbwnIpB;;AUlrII;;EG+CE,qBAAc;AbwoIpB;;AUvrII;;EGoDE,qBAAc;AbwoIpB;;AU5rII;;EGyDE,yBAAkB;EAClB,qBAAc;AbwoIpB;;AUlsII;;EG+CE,qBAAc;AbwpIpB;;AUvsII;;EGoDE,qBAAc;AbwpIpB;;AU5sII;;EGyDE,yBAAkB;EAClB,qBAAc;AbwpIpB;;AUltII;;EG+CE,qBAAc;AbwqIpB;;AUvtII;;EGoDE,qBAAc;AbwqIpB;;AU5tII;;EGyDE,yBAAkB;EAClB,qBAAc;AbwqIpB;;AUluII;;EG+CE,qBAAc;AbwrIpB;;AUvuII;;EGoDE,qBAAc;AbwrIpB;;AU5uII;;EGyDE,yBAAkB;EAClB,qBAAc;AbwrIpB;;AUlvII;;EG+CE,qBAAc;AbwsIpB;;AUvvII;;EGoDE,qBAAc;AbwsIpB;;AU5vII;;EGyDE,yBAAkB;EAClB,qBAAc;AbwsIpB;;AUlwII;;EGkEE,qBAAc;AbqsIpB;;AUvwII;;EGuEE,qBAAc;AbqsIpB;;AU5wII;;EG4EE,yBAAkB;EAClB,qBAAc;AbqsIpB;;AUlxII;;EGkEE,qBAAc;AbqtIpB;;AUvxII;;EGuEE,qBAAc;AbqtIpB;;AU5xII;;EG4EE,yBAAkB;EAClB,qBAAc;AbqtIpB;;AUlyII;;EGkEE,qBAAc;AbquIpB;;AUvyII;;EGuEE,qBAAc;AbquIpB;;AU5yII;;EG4EE,yBAAkB;EAClB,qBAAc;AbquIpB;;AUlzII;;EGkEE,qBAAc;AbqvIpB;;AUvzII;;EGuEE,qBAAc;AbqvIpB;;AU5zII;;EG4EE,yBAAkB;EAClB,qBAAc;AbqvIpB;;AUl0II;;EGkEE,qBAAc;AbqwIpB;;AUv0II;;EGuEE,qBAAc;AbqwIpB;;AU50II;;EG4EE,yBAAkB;EAClB,qBAAc;AbqwIpB;;AUl1II;;EGkEE,qBAAc;AbqxIpB;;AUv1II;;EGuEE,qBAAc;AbqxIpB;;AU51II;;EG4EE,yBAAkB;EAClB,qBAAc;AbqxIpB;;AUl2II;;EGkEE,qBAAc;AbqyIpB;;AUv2II;;EGuEE,qBAAc;AbqyIpB;;AU52II;;EG4EE,yBAAkB;EAClB,qBAAc;AbqyIpB;;AUl3II;;EGkEE,qBAAc;AbqzIpB;;AUv3II;;EGuEE,qBAAc;AbqzIpB;;AU53II;;EG4EE,yBAAkB;EAClB,qBAAc;AbqzIpB;;AUl4II;;EGkEE,qBAAc;Abq0IpB;;AUv4II;;EGuEE,qBAAc;Abq0IpB;;AU54II;;EG4EE,yBAAkB;EAClB,qBAAc;Abq0IpB;;AUl5II;;EGkEE,qBAAc;Abq1IpB;;AUv5II;;EGuEE,qBAAc;Abq1IpB;;AU55II;;EG4EE,yBAAkB;EAClB,qBAAc;Abq1IpB;;AUl6II;;EGkEE,qBAAc;Abq2IpB;;AUv6II;;EGuEE,qBAAc;Abq2IpB;;AU56II;;EG4EE,yBAAkB;EAClB,qBAAc;Abq2IpB;;AUl7II;;EGkEE,qBAAc;Abq3IpB;;AUv7II;;EGuEE,qBAAc;Abq3IpB;;AU57II;;EG4EE,yBAAkB;EAClB,qBAAc;Abq3IpB;;AUl8II;;EGkEE,qBAAc;Abq4IpB;;AUv8II;;EGuEE,qBAAc;Abq4IpB;;AU58II;;EG4EE,yBAAkB;EAClB,qBAAc;Abq4IpB;;AUl9II;;EGkEE,qBAAc;Abq5IpB;;AUv9II;;EGuEE,qBAAc;Abq5IpB;;AU59II;;EG4EE,yBAAkB;EAClB,qBAAc;Abq5IpB;;AUl+II;;EGkEE,qBAAc;Abq6IpB;;AUv+II;;EGuEE,qBAAc;Abq6IpB;;AU5+II;;EG4EE,yBAAkB;EAClB,qBAAc;Abq6IpB;;AUl/II;;EGkEE,qBAAc;Abq7IpB;;AUv/II;;EGuEE,qBAAc;Abq7IpB;;AU5/II;;EG4EE,yBAAkB;EAClB,qBAAc;Abq7IpB;;AUlgJI;;EGkEE,kBAAc;Abq8IpB;;AUvgJI;;EGuEE,kBAAc;Abq8IpB;;AU5gJI;;EG4EE,sBAAkB;EAClB,kBAAc;Abq8IpB;;AUlhJI;;EGkEE,qBAAc;Abq9IpB;;AUvhJI;;EGuEE,qBAAc;Abq9IpB;;AU5hJI;;EG4EE,yBAAkB;EAClB,qBAAc;Abq9IpB;;AUliJI;;EGkEE,qBAAc;Abq+IpB;;AUviJI;;EGuEE,qBAAc;Abq+IpB;;AU5iJI;;EG4EE,yBAAkB;EAClB,qBAAc;Abq+IpB;;AcpjJA;EAEI,kBAAkB;AdsjJtB;;AcxjJA;ECHE,oLZiPmM;EY/OnM,kBAAkB;EAClB,gBZwP+B;EYvP/B,gBZ2P+B;EY1P/B,gBAAgB;EAChB,iBAAiB;EACjB,qBAAqB;EACrB,iBAAiB;EACjB,oBAAoB;EACpB,sBAAsB;EACtB,kBAAkB;EAClB,oBAAoB;EACpB,mBAAmB;EACnB,gBAAgB;ECGd,sBb6MgC;Ec3C9B,mBAAW;EHvKb,sBXQW;EWPX,WXHW;EWIX,cAAc;EACd,gBX2qB+B;EW1qB/B,uBXgrB+B;EW/qB/B,kBAAkB;EAClB,kBAAkB;EAClB,qBAAqB;EACrB,aXsjBoC;AH6gIxC;;AcplJA;EAqBI,yBXbc;EWcd,yBXVc;EWWd,aAAa;EACb,YAAY;AdmkJhB;;Ac3lJA;EA4BI,yBXpBc;EWqBd,sBPiK8B;EOhK9B,sBX6LgC;EW5LhC,WP8JuB;EO7JvB,eAAe;EACf,gBAAgB;EAChB,YAAY;EACZ,UAAU;EACV,iBAAiB;EACjB,iBAAiB;EACjB,kBAAkB;EAClB,kBAAkB;EAClB,MAAM;EAEN,yBAAiB;EAAjB,sBAAiB;EAAjB,qBAAiB;EAAjB,iBAAiB;EACjB,WAAW;AdkkJf;;Ac7mJA;EAgDM,yBAA8D;EAC9D,cAAyC;AdikJ/C;;AclnJA;EAsDI,iBAAiB;EACjB,SAAS;AdgkJb;;AcvnJA;EA2DI,SAAS;AdgkJb;;Ac3nJA;EA+DI,SAAS;AdgkJb;;AkB9nJA;;EAEE,yBfKgB;EeJhB,sBX0LgC;EWzLhC,sBfsNkC;EerNlC,WXuLyB;EWtLzB,YAAY;EACZ,WAAW;EACX,gBAAgB;AlBioJlB;;AkBzoJA;;;;EAaI,yBAA8D;EAC9D,cAAyC;AlBmoJ7C;;AmBjpJA;EAEI,mCAAiC;EACjC,chBqCa;AH8mJjB;;AmBtpJA;EAOI,mCAAoC;EACpC,chB6Ba;AHsnJjB;;AmB3pJA;EAYI,mCAAmC;EACnC,chBsBa;AH6nJjB;;AmBhqJA;EAiBI,mCAAsC;EACtC,chBNc;AHypJlB;;AmBrqJA;EAsBI,mCAAoC;EACpC,chBea;AHooJjB;;AmB1qJA;EA0BM,mCAAoC;AnBopJ1C;;AmB9qJA;EA8BM,yBhBQW;AH4oJjB;;AUjrJI;ESoCA,yBhBvBc;EgBwBd,chB9Bc;AH+qJlB;;AUtrJI;;ESyCE,chBlCY;AHorJlB;;AoBjqJA;EAGI,yBjBCa;AHiqJjB;;AoBrqJA;EAOI,yBjBIa;AH8pJjB;;AoBzqJA;EAWI,yBjBHa;AHqqJjB;;AoB7qJA;EAeI,yBjBFa;AHoqJjB;;AoBjrJA;EAmBI,yBjBTa;AH2qJjB;;AoB7pJA;;EAEE,kBAAkB;ApBgqJpB;;AqBttJA;EACE,alBkkBsC;AHupIxC;;AqB1tJA;EAII,alB+jBoC;AH2pIxC;;AqB9tJA;EAQI,alB2jBoC;AH+pIxC;;AqBptJE;EAGM,mBlBeS;AHssJjB;;AqBhtJE;EAEI,gBlBjBS;AHmuJf;;AqBptJE;EAKM,mBlBKS;AH8sJjB;;AqBxtJE;EASM,kMAA8M;ArBmtJtN;;AqB9sJE;EAGM,6BlBPS;AHstJjB;;AqB1sJE;EAGM,mBlBfS;AH0tJjB;;AqBtsJE;EAEI,aAAa;EACb,WAAW;ArBwsJjB;;AqB3sJE;EAMM,mBlB1BS;EkB2BT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBysJxB;;AqBntJE;EAeI,qBlBnCW;AH2uJjB;;AqBvtJE;EAmBM,qBlBvCS;AH+uJjB;;AqBnsJE;EAGM,kClB/CS;EkBgDT,WlBzEO;AH6wJf;;AqB/rJE;EAGM,6CAA4C;ArBgsJpD;;AqBnsJE;EAOM,6CAA4C;ArBgsJpD;;AqB3rJE;EAEI,gBlB5FS;EkB6FT,qBlBpEW;AHiwJjB;;AqBhsJE;EAMM,mBlBvES;AHqwJjB;;AqBzrJE;EAEI,clB9EW;AHywJjB;;AqBvrJE;EAGM,mBlBrFS;AH6wJjB;;AqB3rJE;;EAQM,sBlBnHO;AH2yJf;;AqBhsJE;EAaQ,0ClB/FO;EkBgGP,yClBhGO;AHuxJjB;;AqBrsJE;EAkBQ,wClBpGO;EkBqGP,2ClBrGO;AH4xJjB;;AqBlrJE;EAGM,wClB7GS;AHgyJjB;;AqB9qJE;EAGM,mBlBrHS;AHoyJjB;;AqBlrJE;EAOM,6ClBzHS;AHwyJjB;;AqBtrJE;EAWM,yBlB7HS;EkB8HT,0BlB9HS;AH6yJjB;;AqB1qJE;EAGM,mBlBtIS;EkBuIT,clBvIS;EkBwIT,sClBjKO;AH40Jf;;AqBhrJE;EASM,yDlBrKO;AHg1Jf;;AqBtqJE;EAGM,yBlBpJS;EkBqJT,kIlB9KO;AHq1Jf;;AqB3qJE;EAQM,+FAAqG;EACrG,YAAY;ArBuqJpB;;AqBlqJE;EAEI,clBjKW;AHq0JjB;;AqBv1JE;EAGM,mBlBJU;AH41JlB;;AqBn1JE;EAEI,gBlBjBS;AHs2Jf;;AqBv1JE;EAKM,mBlBdU;AHo2JlB;;AqB31JE;EASM,kMAA8M;ArBs1JtN;;AqBj1JE;EAGM,+BlB1BU;AH42JlB;;AqB70JE;EAGM,mBlBlCU;AHg3JlB;;AqBz0JE;EAEI,aAAa;EACb,WAAW;ArB20JjB;;AqB90JE;EAMM,mBlB7CU;EkB8CV,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB40JxB;;AqBt1JE;EAeI,qBlBtDY;AHi4JlB;;AqB11JE;EAmBM,qBlB1DU;AHq4JlB;;AqBt0JE;EAGM,oClBlEU;EkBmEV,WlBzEO;AHg5Jf;;AqBl0JE;EAGM,6CAA4C;ArBm0JpD;;AqBt0JE;EAOM,6CAA4C;ArBm0JpD;;AqB9zJE;EAEI,gBlB5FS;EkB6FT,qBlBvFY;AHu5JlB;;AqBn0JE;EAMM,mBlB1FU;AH25JlB;;AqB5zJE;EAEI,clBjGY;AH+5JlB;;AqB1zJE;EAGM,mBlBxGU;AHm6JlB;;AqB9zJE;;EAQM,sBlBnHO;AH86Jf;;AqBn0JE;EAaQ,4ClBlHQ;EkBmHR,2ClBnHQ;AH66JlB;;AqBx0JE;EAkBQ,0ClBvHQ;EkBwHR,6ClBxHQ;AHk7JlB;;AqBrzJE;EAGM,0ClBhIU;AHs7JlB;;AqBjzJE;EAGM,mBlBxIU;AH07JlB;;AqBrzJE;EAOM,6ClB5IU;AH87JlB;;AqBzzJE;EAWM,yBlBhJU;EkBiJV,0BlBjJU;AHm8JlB;;AqB7yJE;EAGM,mBlBzJU;EkB0JV,clB1JU;EkB2JV,sClBjKO;AH+8Jf;;AqBnzJE;EASM,yDlBrKO;AHm9Jf;;AqBzyJE;EAGM,yBlBvKU;EkBwKV,kIlB9KO;AHw9Jf;;AqB9yJE;EAQM,+FAAqG;EACrG,YAAY;ArB0yJpB;;AqBryJE;EAEI,clBpLY;AH29JlB;;AqB19JE;EAGM,mBlBsBS;AHq8JjB;;AqBt9JE;EAEI,gBlBjBS;AHy+Jf;;AqB19JE;EAKM,mBlBYS;AH68JjB;;AqB99JE;EASM,kMAA8M;ArBy9JtN;;AqBp9JE;EAGM,6BlBAS;AHq9JjB;;AqBh9JE;EAGM,mBlBRS;AHy9JjB;;AqB58JE;EAEI,aAAa;EACb,WAAW;ArB88JjB;;AqBj9JE;EAMM,mBlBnBS;EkBoBT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB+8JxB;;AqBz9JE;EAeI,qBlB5BW;AH0+JjB;;AqB79JE;EAmBM,qBlBhCS;AH8+JjB;;AqBz8JE;EAGM,kClBxCS;EkByCT,WlBzEO;AHmhKf;;AqBr8JE;EAGM,6CAA4C;ArBs8JpD;;AqBz8JE;EAOM,6CAA4C;ArBs8JpD;;AqBj8JE;EAEI,gBlB5FS;EkB6FT,qBlB7DW;AHggKjB;;AqBt8JE;EAMM,mBlBhES;AHogKjB;;AqB/7JE;EAEI,clBvEW;AHwgKjB;;AqB77JE;EAGM,mBlB9ES;AH4gKjB;;AqBj8JE;;EAQM,sBlBnHO;AHijKf;;AqBt8JE;EAaQ,0ClBxFO;EkByFP,yClBzFO;AHshKjB;;AqB38JE;EAkBQ,wClB7FO;EkB8FP,2ClB9FO;AH2hKjB;;AqBx7JE;EAGM,wClBtGS;AH+hKjB;;AqBp7JE;EAGM,mBlB9GS;AHmiKjB;;AqBx7JE;EAOM,6ClBlHS;AHuiKjB;;AqB57JE;EAWM,yBlBtHS;EkBuHT,0BlBvHS;AH4iKjB;;AqBh7JE;EAGM,mBlB/HS;EkBgIT,clBhIS;EkBiIT,sClBjKO;AHklKf;;AqBt7JE;EASM,yDlBrKO;AHslKf;;AqB56JE;EAGM,yBlB7IS;EkB8IT,kIlB9KO;AH2lKf;;AqBj7JE;EAQM,+FAAqG;EACrG,YAAY;ArB66JpB;;AqBx6JE;EAEI,clB1JW;AHokKjB;;AqB7lKE;EAGM,mBlBwBS;AHskKjB;;AqBzlKE;EAEI,gBlBjBS;AH4mKf;;AqB7lKE;EAKM,mBlBcS;AH8kKjB;;AqBjmKE;EASM,kMAA8M;ArB4lKtN;;AqBvlKE;EAGM,8BlBES;AHslKjB;;AqBnlKE;EAGM,mBlBNS;AH0lKjB;;AqB/kKE;EAEI,aAAa;EACb,WAAW;ArBilKjB;;AqBplKE;EAMM,mBlBjBS;EkBkBT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBklKxB;;AqB5lKE;EAeI,qBlB1BW;AH2mKjB;;AqBhmKE;EAmBM,qBlB9BS;AH+mKjB;;AqB5kKE;EAGM,mClBtCS;EkBuCT,WlBzEO;AHspKf;;AqBxkKE;EAGM,6CAA4C;ArBykKpD;;AqB5kKE;EAOM,6CAA4C;ArBykKpD;;AqBpkKE;EAEI,gBlB5FS;EkB6FT,qBlB3DW;AHioKjB;;AqBzkKE;EAMM,mBlB9DS;AHqoKjB;;AqBlkKE;EAEI,clBrEW;AHyoKjB;;AqBhkKE;EAGM,mBlB5ES;AH6oKjB;;AqBpkKE;;EAQM,sBlBnHO;AHorKf;;AqBzkKE;EAaQ,2ClBtFO;EkBuFP,0ClBvFO;AHupKjB;;AqB9kKE;EAkBQ,yClB3FO;EkB4FP,4ClB5FO;AH4pKjB;;AqB3jKE;EAGM,yClBpGS;AHgqKjB;;AqBvjKE;EAGM,mBlB5GS;AHoqKjB;;AqB3jKE;EAOM,6ClBhHS;AHwqKjB;;AqB/jKE;EAWM,yBlBpHS;EkBqHT,0BlBrHS;AH6qKjB;;AqBnjKE;EAGM,mBlB7HS;EkB8HT,clB9HS;EkB+HT,sClBjKO;AHqtKf;;AqBzjKE;EASM,yDlBrKO;AHytKf;;AqB/iKE;EAGM,yBlB3IS;EkB4IT,kIlB9KO;AH8tKf;;AqBpjKE;EAQM,+FAAqG;EACrG,YAAY;ArBgjKpB;;AqB3iKE;EAEI,clBxJW;AHqsKjB;;AqBhuKE;EAGM,mBlBqBS;AH4sKjB;;AqB5tKE;EAEI,mBlBiEiB;AH6pKvB;;AqBhuKE;EAKM,mBlBWS;AHotKjB;;AqBpuKE;EASM,yLAA8M;ArB+tKtN;;AqB1tKE;EAGM,6BlBDS;AH4tKjB;;AqBttKE;EAGM,mBlBTS;AHguKjB;;AqBltKE;EAEI,aAAa;EACb,WAAW;ArBotKjB;;AqBvtKE;EAMM,mBlBpBS;EkBqBT,clB8Be;EkB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBqtKxB;;AqB/tKE;EAeI,qBlB7BW;AHivKjB;;AqBnuKE;EAmBM,qBlBjCS;AHqvKjB;;AqB/sKE;EAGM,kClBzCS;EkB0CT,clBSe;AHusKvB;;AqB3sKE;EAGM,6CAA4C;ArB4sKpD;;AqB/sKE;EAOM,6CAA4C;ArB4sKpD;;AqBvsKE;EAEI,mBlBViB;EkBWjB,qBlB9DW;AHuwKjB;;AqB5sKE;EAMM,mBlBjES;AH2wKjB;;AqBrsKE;EAEI,clBxEW;AH+wKjB;;AqBnsKE;EAGM,mBlB/ES;AHmxKjB;;AqBvsKE;;EAQM,yBlBjCe;AHquKvB;;AqB5sKE;EAaQ,0ClBzFO;EkB0FP,yClB1FO;AH6xKjB;;AqBjtKE;EAkBQ,wClB9FO;EkB+FP,2ClB/FO;AHkyKjB;;AqB9rKE;EAGM,wClBvGS;AHsyKjB;;AqB1rKE;EAGM,mBlB/GS;AH0yKjB;;AqB9rKE;EAOM,6ClBnHS;AH8yKjB;;AqBlsKE;EAWM,yBlBvHS;EkBwHT,0BlBxHS;AHmzKjB;;AqBtrKE;EAGM,mBlBhIS;EkBiIT,clBjIS;EkBkIT,4ClB/Ee;AHswKvB;;AqB5rKE;EASM,4DlBnFe;AH0wKvB;;AqBlrKE;EAGM,yBlB9IS;EkB+IT,4HlB5Fe;AH+wKvB;;AqBvrKE;EAQM,yFAAqG;EACrG,YAAY;ArBmrKpB;;AqB9qKE;EAEI,clB3JW;AH20KjB;;AqBn2KE;EAGM,mBlBmBS;AHi1KjB;;AqB/1KE;EAEI,gBlBjBS;AHk3Kf;;AqBn2KE;EAKM,mBlBSS;AHy1KjB;;AqBv2KE;EASM,kMAA8M;ArBk2KtN;;AqB71KE;EAGM,6BlBHS;AHi2KjB;;AqBz1KE;EAGM,mBlBXS;AHq2KjB;;AqBr1KE;EAEI,aAAa;EACb,WAAW;ArBu1KjB;;AqB11KE;EAMM,mBlBtBS;EkBuBT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBw1KxB;;AqBl2KE;EAeI,qBlB/BW;AHs3KjB;;AqBt2KE;EAmBM,qBlBnCS;AH03KjB;;AqBl1KE;EAGM,kClB3CS;EkB4CT,WlBzEO;AH45Kf;;AqB90KE;EAGM,6CAA4C;ArB+0KpD;;AqBl1KE;EAOM,6CAA4C;ArB+0KpD;;AqB10KE;EAEI,gBlB5FS;EkB6FT,qBlBhEW;AH44KjB;;AqB/0KE;EAMM,mBlBnES;AHg5KjB;;AqBx0KE;EAEI,clB1EW;AHo5KjB;;AqBt0KE;EAGM,mBlBjFS;AHw5KjB;;AqB10KE;;EAQM,sBlBnHO;AH07Kf;;AqB/0KE;EAaQ,0ClB3FO;EkB4FP,yClB5FO;AHk6KjB;;AqBp1KE;EAkBQ,wClBhGO;EkBiGP,2ClBjGO;AHu6KjB;;AqBj0KE;EAGM,wClBzGS;AH26KjB;;AqB7zKE;EAGM,mBlBjHS;AH+6KjB;;AqBj0KE;EAOM,6ClBrHS;AHm7KjB;;AqBr0KE;EAWM,yBlBzHS;EkB0HT,0BlB1HS;AHw7KjB;;AqBzzKE;EAGM,mBlBlIS;EkBmIT,clBnIS;EkBoIT,sClBjKO;AH29Kf;;AqB/zKE;EASM,yDlBrKO;AH+9Kf;;AqBrzKE;EAGM,yBlBhJS;EkBiJT,kIlB9KO;AHo+Kf;;AqB1zKE;EAQM,+FAAqG;EACrG,YAAY;ArBszKpB;;AqBjzKE;EAEI,clB7JW;AHg9KjB;;AqBt+KE;EAGM,mBlBTU;AHg/KlB;;AqBl+KE;EAEI,mBlBiEiB;AHm6KvB;;AqBt+KE;EAKM,mBlBnBU;AHw/KlB;;AqB1+KE;EASM,yLAA8M;ArBq+KtN;;AqBh+KE;EAGM,+BlB/BU;AHggLlB;;AqB59KE;EAGM,mBlBvCU;AHogLlB;;AqBx9KE;EAEI,aAAa;EACb,WAAW;ArB09KjB;;AqB79KE;EAMM,mBlBlDU;EkBmDV,clB8Be;EkB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB29KxB;;AqBr+KE;EAeI,qBlB3DY;AHqhLlB;;AqBz+KE;EAmBM,qBlB/DU;AHyhLlB;;AqBr9KE;EAGM,oClBvEU;EkBwEV,clBSe;AH68KvB;;AqBj9KE;EAGM,6CAA4C;ArBk9KpD;;AqBr9KE;EAOM,6CAA4C;ArBk9KpD;;AqB78KE;EAEI,mBlBViB;EkBWjB,qBlB5FY;AH2iLlB;;AqBl9KE;EAMM,mBlB/FU;AH+iLlB;;AqB38KE;EAEI,clBtGY;AHmjLlB;;AqBz8KE;EAGM,mBlB7GU;AHujLlB;;AqB78KE;;EAQM,yBlBjCe;AH2+KvB;;AqBl9KE;EAaQ,4ClBvHQ;EkBwHR,2ClBxHQ;AHikLlB;;AqBv9KE;EAkBQ,0ClB5HQ;EkB6HR,6ClB7HQ;AHskLlB;;AqBp8KE;EAGM,0ClBrIU;AH0kLlB;;AqBh8KE;EAGM,mBlB7IU;AH8kLlB;;AqBp8KE;EAOM,6ClBjJU;AHklLlB;;AqBx8KE;EAWM,yBlBrJU;EkBsJV,0BlBtJU;AHulLlB;;AqB57KE;EAGM,mBlB9JU;EkB+JV,clB/JU;EkBgKV,4ClB/Ee;AH4gLvB;;AqBl8KE;EASM,4DlBnFe;AHghLvB;;AqBx7KE;EAGM,yBlB5KU;EkB6KV,4HlB5Fe;AHqhLvB;;AqB77KE;EAQM,yFAAqG;EACrG,YAAY;ArBy7KpB;;AqBp7KE;EAEI,clBzLY;AH+mLlB;;AqBzmLE;EAGM,mBlBFU;AH4mLlB;;AqBrmLE;EAEI,gBlBjBS;AHwnLf;;AqBzmLE;EAKM,mBlBZU;AHonLlB;;AqB7mLE;EASM,kMAA8M;ArBwmLtN;;AqBnmLE;EAGM,4BlBxBU;AH4nLlB;;AqB/lLE;EAGM,mBlBhCU;AHgoLlB;;AqB3lLE;EAEI,aAAa;EACb,WAAW;ArB6lLjB;;AqBhmLE;EAMM,mBlB3CU;EkB4CV,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB8lLxB;;AqBxmLE;EAeI,qBlBpDY;AHipLlB;;AqB5mLE;EAmBM,qBlBxDU;AHqpLlB;;AqBxlLE;EAGM,iClBhEU;EkBiEV,WlBzEO;AHkqLf;;AqBplLE;EAGM,6CAA4C;ArBqlLpD;;AqBxlLE;EAOM,6CAA4C;ArBqlLpD;;AqBhlLE;EAEI,gBlB5FS;EkB6FT,qBlBrFY;AHuqLlB;;AqBrlLE;EAMM,mBlBxFU;AH2qLlB;;AqB9kLE;EAEI,clB/FY;AH+qLlB;;AqB5kLE;EAGM,mBlBtGU;AHmrLlB;;AqBhlLE;;EAQM,sBlBnHO;AHgsLf;;AqBrlLE;EAaQ,yClBhHQ;EkBiHR,wClBjHQ;AH6rLlB;;AqB1lLE;EAkBQ,uClBrHQ;EkBsHR,0ClBtHQ;AHksLlB;;AqBvkLE;EAGM,uClB9HU;AHssLlB;;AqBnkLE;EAGM,mBlBtIU;AH0sLlB;;AqBvkLE;EAOM,6ClB1IU;AH8sLlB;;AqB3kLE;EAWM,yBlB9IU;EkB+IV,0BlB/IU;AHmtLlB;;AqB/jLE;EAGM,mBlBvJU;EkBwJV,clBxJU;EkByJV,sClBjKO;AHiuLf;;AqBrkLE;EASM,yDlBrKO;AHquLf;;AqB3jLE;EAGM,yBlBrKU;EkBsKV,kIlB9KO;AH0uLf;;AqBhkLE;EAQM,+FAAqG;EACrG,YAAY;ArB4jLpB;;AqBvjLE;EAEI,clBlLY;AH2uLlB;;AqB5uLE;EAGM,mBddW;AP2vLnB;;AqBxuLE;EAEI,gBlBjBS;AH2vLf;;AqB5uLE;EAKM,mBdxBW;APmwLnB;;AqBhvLE;EASM,kMAA8M;ArB2uLtN;;AqBtuLE;EAGM,8BdpCW;AP2wLnB;;AqBluLE;EAGM,mBd5CW;AP+wLnB;;AqB9tLE;EAEI,aAAa;EACb,WAAW;ArBguLjB;;AqBnuLE;EAMM,mBdvDW;EcwDX,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBiuLxB;;AqB3uLE;EAeI,qBdhEa;APgyLnB;;AqB/uLE;EAmBM,qBdpEW;APoyLnB;;AqB3tLE;EAGM,mCd5EW;Ec6EX,WlBzEO;AHqyLf;;AqBvtLE;EAGM,6CAA4C;ArBwtLpD;;AqB3tLE;EAOM,6CAA4C;ArBwtLpD;;AqBntLE;EAEI,gBlB5FS;EkB6FT,qBdjGa;APszLnB;;AqBxtLE;EAMM,mBdpGW;AP0zLnB;;AqBjtLE;EAEI,cd3Ga;AP8zLnB;;AqB/sLE;EAGM,mBdlHW;APk0LnB;;AqBntLE;;EAQM,sBlBnHO;AHm0Lf;;AqBxtLE;EAaQ,2Cd5HS;Ec6HT,0Cd7HS;AP40LnB;;AqB7tLE;EAkBQ,yCdjIS;EckIT,4CdlIS;APi1LnB;;AqB1sLE;EAGM,yCd1IW;APq1LnB;;AqBtsLE;EAGM,mBdlJW;APy1LnB;;AqB1sLE;EAOM,6CdtJW;AP61LnB;;AqB9sLE;EAWM,yBd1JW;Ec2JX,0Bd3JW;APk2LnB;;AqBlsLE;EAGM,mBdnKW;EcoKX,cdpKW;EcqKX,sClBjKO;AHo2Lf;;AqBxsLE;EASM,yDlBrKO;AHw2Lf;;AqB9rLE;EAGM,yBdjLW;EckLX,kIlB9KO;AH62Lf;;AqBnsLE;EAQM,+FAAqG;EACrG,YAAY;ArB+rLpB;;AqB1rLE;EAEI,cd9La;AP03LnB;;AqB/2LE;EAGM,mBdbM;AP63Ld;;AqB32LE;EAEI,gBlBjBS;AH83Lf;;AqB/2LE;EAKM,mBdvBM;APq4Ld;;AqBn3LE;EASM,kMAA8M;ArB82LtN;;AqBz2LE;EAGM,2BdnCM;AP64Ld;;AqBr2LE;EAGM,mBd3CM;APi5Ld;;AqBj2LE;EAEI,aAAa;EACb,WAAW;ArBm2LjB;;AqBt2LE;EAMM,mBdtDM;EcuDN,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBo2LxB;;AqB92LE;EAeI,qBd/DQ;APk6Ld;;AqBl3LE;EAmBM,qBdnEM;APs6Ld;;AqB91LE;EAGM,gCd3EM;Ec4EN,WlBzEO;AHw6Lf;;AqB11LE;EAGM,6CAA4C;ArB21LpD;;AqB91LE;EAOM,6CAA4C;ArB21LpD;;AqBt1LE;EAEI,gBlB5FS;EkB6FT,qBdhGQ;APw7Ld;;AqB31LE;EAMM,mBdnGM;AP47Ld;;AqBp1LE;EAEI,cd1GQ;APg8Ld;;AqBl1LE;EAGM,mBdjHM;APo8Ld;;AqBt1LE;;EAQM,sBlBnHO;AHs8Lf;;AqB31LE;EAaQ,wCd3HI;Ec4HJ,uCd5HI;AP88Ld;;AqBh2LE;EAkBQ,sCdhII;EciIJ,yCdjII;APm9Ld;;AqB70LE;EAGM,sCdzIM;APu9Ld;;AqBz0LE;EAGM,mBdjJM;AP29Ld;;AqB70LE;EAOM,6CdrJM;AP+9Ld;;AqBj1LE;EAWM,yBdzJM;Ec0JN,0Bd1JM;APo+Ld;;AqBr0LE;EAGM,mBdlKM;EcmKN,cdnKM;EcoKN,sClBjKO;AHu+Lf;;AqB30LE;EASM,yDlBrKO;AH2+Lf;;AqBj0LE;EAGM,yBdhLM;EciLN,kIlB9KO;AHg/Lf;;AqBt0LE;EAQM,+FAAqG;EACrG,YAAY;ArBk0LpB;;AqB7zLE;EAEI,cd7LQ;AP4/Ld;;AqBl/LE;EAGM,mBdXO;AP8/Lf;;AqB9+LE;EAEI,gBlBjBS;AHigMf;;AqBl/LE;EAKM,mBdrBO;APsgMf;;AqBt/LE;EASM,kMAA8M;ArBi/LtN;;AqB5+LE;EAGM,8BdjCO;AP8gMf;;AqBx+LE;EAGM,mBdzCO;APkhMf;;AqBp+LE;EAEI,aAAa;EACb,WAAW;ArBs+LjB;;AqBz+LE;EAMM,mBdpDO;EcqDP,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBu+LxB;;AqBj/LE;EAeI,qBd7DS;APmiMf;;AqBr/LE;EAmBM,qBdjEO;APuiMf;;AqBj+LE;EAGM,mCdzEO;Ec0EP,WlBzEO;AH2iMf;;AqB79LE;EAGM,6CAA4C;ArB89LpD;;AqBj+LE;EAOM,6CAA4C;ArB89LpD;;AqBz9LE;EAEI,gBlB5FS;EkB6FT,qBd9FS;APyjMf;;AqB99LE;EAMM,mBdjGO;AP6jMf;;AqBv9LE;EAEI,cdxGS;APikMf;;AqBr9LE;EAGM,mBd/GO;APqkMf;;AqBz9LE;;EAQM,sBlBnHO;AHykMf;;AqB99LE;EAaQ,2CdzHK;Ec0HL,0Cd1HK;AP+kMf;;AqBn+LE;EAkBQ,yCd9HK;Ec+HL,4Cd/HK;APolMf;;AqBh9LE;EAGM,yCdvIO;APwlMf;;AqB58LE;EAGM,mBd/IO;AP4lMf;;AqBh9LE;EAOM,6CdnJO;APgmMf;;AqBp9LE;EAWM,yBdvJO;EcwJP,0BdxJO;APqmMf;;AqBx8LE;EAGM,mBdhKO;EciKP,cdjKO;EckKP,sClBjKO;AH0mMf;;AqB98LE;EASM,yDlBrKO;AH8mMf;;AqBp8LE;EAGM,yBd9KO;Ec+KP,kIlB9KO;AHmnMf;;AqBz8LE;EAQM,+FAAqG;EACrG,YAAY;ArBq8LpB;;AqBh8LE;EAEI,cd3LS;AP6nMf;;AqBrnME;EAGM,mBdVM;APgoMd;;AqBjnME;EAEI,mBlBiEiB;AHkjMvB;;AqBrnME;EAKM,mBdpBM;APwoMd;;AqBznME;EASM,yLAA8M;ArBonMtN;;AqB/mME;EAGM,6BdhCM;APgpMd;;AqB3mME;EAGM,mBdxCM;APopMd;;AqBvmME;EAEI,aAAa;EACb,WAAW;ArBymMjB;;AqB5mME;EAMM,mBdnDM;EcoDN,clB8Be;EkB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB0mMxB;;AqBpnME;EAeI,qBd5DQ;APqqMd;;AqBxnME;EAmBM,qBdhEM;APyqMd;;AqBpmME;EAGM,kCdxEM;EcyEN,clBSe;AH4lMvB;;AqBhmME;EAGM,6CAA4C;ArBimMpD;;AqBpmME;EAOM,6CAA4C;ArBimMpD;;AqB5lME;EAEI,mBlBViB;EkBWjB,qBd7FQ;AP2rMd;;AqBjmME;EAMM,mBdhGM;AP+rMd;;AqB1lME;EAEI,cdvGQ;APmsMd;;AqBxlME;EAGM,mBd9GM;APusMd;;AqB5lME;;EAQM,yBlBjCe;AH0nMvB;;AqBjmME;EAaQ,0CdxHI;EcyHJ,yCdzHI;APitMd;;AqBtmME;EAkBQ,wCd7HI;Ec8HJ,2Cd9HI;APstMd;;AqBnlME;EAGM,wCdtIM;AP0tMd;;AqB/kME;EAGM,mBd9IM;AP8tMd;;AqBnlME;EAOM,6CdlJM;APkuMd;;AqBvlME;EAWM,yBdtJM;EcuJN,0BdvJM;APuuMd;;AqB3kME;EAGM,mBd/JM;EcgKN,cdhKM;EciKN,4ClB/Ee;AH2pMvB;;AqBjlME;EASM,4DlBnFe;AH+pMvB;;AqBvkME;EAGM,yBd7KM;Ec8KN,4HlB5Fe;AHoqMvB;;AqB5kME;EAQM,yFAAqG;EACrG,YAAY;ArBwkMpB;;AqBnkME;EAEI,cd1LQ;AP+vMd;;AqBxvME;EAGM,mBdRS;APiwMjB;;AqBpvME;EAEI,gBlBjBS;AHuwMf;;AqBxvME;EAKM,mBdlBS;APywMjB;;AqB5vME;EASM,kMAA8M;ArBuvMtN;;AqBlvME;EAGM,8Bd9BS;APixMjB;;AqB9uME;EAGM,mBdtCS;APqxMjB;;AqB1uME;EAEI,aAAa;EACb,WAAW;ArB4uMjB;;AqB/uME;EAMM,mBdjDS;EckDT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB6uMxB;;AqBvvME;EAeI,qBd1DW;APsyMjB;;AqB3vME;EAmBM,qBd9DS;AP0yMjB;;AqBvuME;EAGM,mCdtES;EcuET,WlBzEO;AHizMf;;AqBnuME;EAGM,6CAA4C;ArBouMpD;;AqBvuME;EAOM,6CAA4C;ArBouMpD;;AqB/tME;EAEI,gBlB5FS;EkB6FT,qBd3FW;AP4zMjB;;AqBpuME;EAMM,mBd9FS;APg0MjB;;AqB7tME;EAEI,cdrGW;APo0MjB;;AqB3tME;EAGM,mBd5GS;APw0MjB;;AqB/tME;;EAQM,sBlBnHO;AH+0Mf;;AqBpuME;EAaQ,2CdtHO;EcuHP,0CdvHO;APk1MjB;;AqBzuME;EAkBQ,yCd3HO;Ec4HP,4Cd5HO;APu1MjB;;AqBttME;EAGM,yCdpIS;AP21MjB;;AqBltME;EAGM,mBd5IS;AP+1MjB;;AqBttME;EAOM,6CdhJS;APm2MjB;;AqB1tME;EAWM,yBdpJS;EcqJT,0BdrJS;APw2MjB;;AqB9sME;EAGM,mBd7JS;Ec8JT,cd9JS;Ec+JT,sClBjKO;AHg3Mf;;AqBptME;EASM,yDlBrKO;AHo3Mf;;AqB1sME;EAGM,yBd3KS;Ec4KT,kIlB9KO;AHy3Mf;;AqB/sME;EAQM,+FAAqG;EACrG,YAAY;ArB2sMpB;;AqBtsME;EAEI,cdxLW;APg4MjB;;AqB33ME;EAGM,mBdNQ;APk4MhB;;AqBv3ME;EAEI,gBlBjBS;AH04Mf;;AqB33ME;EAKM,mBdhBQ;AP04MhB;;AqB/3ME;EASM,kMAA8M;ArB03MtN;;AqBr3ME;EAGM,6Bd5BQ;APk5MhB;;AqBj3ME;EAGM,mBdpCQ;APs5MhB;;AqB72ME;EAEI,aAAa;EACb,WAAW;ArB+2MjB;;AqBl3ME;EAMM,mBd/CQ;EcgDR,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBg3MxB;;AqB13ME;EAeI,qBdxDU;APu6MhB;;AqB93ME;EAmBM,qBd5DQ;AP26MhB;;AqB12ME;EAGM,kCdpEQ;EcqER,WlBzEO;AHo7Mf;;AqBt2ME;EAGM,6CAA4C;ArBu2MpD;;AqB12ME;EAOM,6CAA4C;ArBu2MpD;;AqBl2ME;EAEI,gBlB5FS;EkB6FT,qBdzFU;AP67MhB;;AqBv2ME;EAMM,mBd5FQ;APi8MhB;;AqBh2ME;EAEI,cdnGU;APq8MhB;;AqB91ME;EAGM,mBd1GQ;APy8MhB;;AqBl2ME;;EAQM,sBlBnHO;AHk9Mf;;AqBv2ME;EAaQ,0CdpHM;EcqHN,yCdrHM;APm9MhB;;AqB52ME;EAkBQ,wCdzHM;Ec0HN,2Cd1HM;APw9MhB;;AqBz1ME;EAGM,wCdlIQ;AP49MhB;;AqBr1ME;EAGM,mBd1IQ;APg+MhB;;AqBz1ME;EAOM,6Cd9IQ;APo+MhB;;AqB71ME;EAWM,yBdlJQ;EcmJR,0BdnJQ;APy+MhB;;AqBj1ME;EAGM,mBd3JQ;Ec4JR,cd5JQ;Ec6JR,sClBjKO;AHm/Mf;;AqBv1ME;EASM,yDlBrKO;AHu/Mf;;AqB70ME;EAGM,yBdzKQ;Ec0KR,kIlB9KO;AH4/Mf;;AqBl1ME;EAQM,+FAAqG;EACrG,YAAY;ArB80MpB;;AqBz0ME;EAEI,cdtLU;APigNhB;;AqB9/ME;EAGM,mBlBeS;AHg/MjB;;AqB1/ME;EAEI,gBlBjBS;AH6gNf;;AqB9/ME;EAKM,mBlBKS;AHw/MjB;;AqBlgNE;EASM,kMAA8M;ArB6/MtN;;AqBx/ME;EAGM,6BlBPS;AHggNjB;;AqBp/ME;EAGM,mBlBfS;AHogNjB;;AqBh/ME;EAEI,aAAa;EACb,WAAW;ArBk/MjB;;AqBr/ME;EAMM,mBlB1BS;EkB2BT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBm/MxB;;AqB7/ME;EAeI,qBlBnCW;AHqhNjB;;AqBjgNE;EAmBM,qBlBvCS;AHyhNjB;;AqB7+ME;EAGM,kClB/CS;EkBgDT,WlBzEO;AHujNf;;AqBz+ME;EAGM,6CAA4C;ArB0+MpD;;AqB7+ME;EAOM,6CAA4C;ArB0+MpD;;AqBr+ME;EAEI,gBlB5FS;EkB6FT,qBlBpEW;AH2iNjB;;AqB1+ME;EAMM,mBlBvES;AH+iNjB;;AqBn+ME;EAEI,clB9EW;AHmjNjB;;AqBj+ME;EAGM,mBlBrFS;AHujNjB;;AqBr+ME;;EAQM,sBlBnHO;AHqlNf;;AqB1+ME;EAaQ,0ClB/FO;EkBgGP,yClBhGO;AHikNjB;;AqB/+ME;EAkBQ,wClBpGO;EkBqGP,2ClBrGO;AHskNjB;;AqB59ME;EAGM,wClB7GS;AH0kNjB;;AqBx9ME;EAGM,mBlBrHS;AH8kNjB;;AqB59ME;EAOM,6ClBzHS;AHklNjB;;AqBh+ME;EAWM,yBlB7HS;EkB8HT,0BlB9HS;AHulNjB;;AqBp9ME;EAGM,mBlBtIS;EkBuIT,clBvIS;EkBwIT,sClBjKO;AHsnNf;;AqB19ME;EASM,yDlBrKO;AH0nNf;;AqBh9ME;EAGM,yBlBpJS;EkBqJT,kIlB9KO;AH+nNf;;AqBr9ME;EAQM,+FAAqG;EACrG,YAAY;ArBi9MpB;;AqB58ME;EAEI,clBjKW;AH+mNjB;;AqBjoNE;EAGM,mBlBgBS;AHknNjB;;AqB7nNE;EAEI,gBlBjBS;AHgpNf;;AqBjoNE;EAKM,mBlBMS;AH0nNjB;;AqBroNE;EASM,kMAA8M;ArBgoNtN;;AqB3nNE;EAGM,8BlBNS;AHkoNjB;;AqBvnNE;EAGM,mBlBdS;AHsoNjB;;AqBnnNE;EAEI,aAAa;EACb,WAAW;ArBqnNjB;;AqBxnNE;EAMM,mBlBzBS;EkB0BT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBsnNxB;;AqBhoNE;EAeI,qBlBlCW;AHupNjB;;AqBpoNE;EAmBM,qBlBtCS;AH2pNjB;;AqBhnNE;EAGM,mClB9CS;EkB+CT,WlBzEO;AH0rNf;;AqB5mNE;EAGM,6CAA4C;ArB6mNpD;;AqBhnNE;EAOM,6CAA4C;ArB6mNpD;;AqBxmNE;EAEI,gBlB5FS;EkB6FT,qBlBnEW;AH6qNjB;;AqB7mNE;EAMM,mBlBtES;AHirNjB;;AqBtmNE;EAEI,clB7EW;AHqrNjB;;AqBpmNE;EAGM,mBlBpFS;AHyrNjB;;AqBxmNE;;EAQM,sBlBnHO;AHwtNf;;AqB7mNE;EAaQ,2ClB9FO;EkB+FP,0ClB/FO;AHmsNjB;;AqBlnNE;EAkBQ,yClBnGO;EkBoGP,4ClBpGO;AHwsNjB;;AqB/lNE;EAGM,yClB5GS;AH4sNjB;;AqB3lNE;EAGM,mBlBpHS;AHgtNjB;;AqB/lNE;EAOM,6ClBxHS;AHotNjB;;AqBnmNE;EAWM,yBlB5HS;EkB6HT,0BlB7HS;AHytNjB;;AqBvlNE;EAGM,mBlBrIS;EkBsIT,clBtIS;EkBuIT,sClBjKO;AHyvNf;;AqB7lNE;EASM,yDlBrKO;AH6vNf;;AqBnlNE;EAGM,yBlBnJS;EkBoJT,kIlB9KO;AHkwNf;;AqBxlNE;EAQM,+FAAqG;EACrG,YAAY;ArBolNpB;;AqB/kNE;EAEI,clBhKW;AHivNjB;;AqBpwNE;EAGM,mBlBiBS;AHovNjB;;AqBhwNE;EAEI,gBlBjBS;AHmxNf;;AqBpwNE;EAKM,mBlBOS;AH4vNjB;;AqBxwNE;EASM,kMAA8M;ArBmwNtN;;AqB9vNE;EAGM,8BlBLS;AHowNjB;;AqB1vNE;EAGM,mBlBbS;AHwwNjB;;AqBtvNE;EAEI,aAAa;EACb,WAAW;ArBwvNjB;;AqB3vNE;EAMM,mBlBxBS;EkByBT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArByvNxB;;AqBnwNE;EAeI,qBlBjCW;AHyxNjB;;AqBvwNE;EAmBM,qBlBrCS;AH6xNjB;;AqBnvNE;EAGM,mClB7CS;EkB8CT,WlBzEO;AH6zNf;;AqB/uNE;EAGM,6CAA4C;ArBgvNpD;;AqBnvNE;EAOM,6CAA4C;ArBgvNpD;;AqB3uNE;EAEI,gBlB5FS;EkB6FT,qBlBlEW;AH+yNjB;;AqBhvNE;EAMM,mBlBrES;AHmzNjB;;AqBzuNE;EAEI,clB5EW;AHuzNjB;;AqBvuNE;EAGM,mBlBnFS;AH2zNjB;;AqB3uNE;;EAQM,sBlBnHO;AH21Nf;;AqBhvNE;EAaQ,2ClB7FO;EkB8FP,0ClB9FO;AHq0NjB;;AqBrvNE;EAkBQ,yClBlGO;EkBmGP,4ClBnGO;AH00NjB;;AqBluNE;EAGM,yClB3GS;AH80NjB;;AqB9tNE;EAGM,mBlBnHS;AHk1NjB;;AqBluNE;EAOM,6ClBvHS;AHs1NjB;;AqBtuNE;EAWM,yBlB3HS;EkB4HT,0BlB5HS;AH21NjB;;AqB1tNE;EAGM,mBlBpIS;EkBqIT,clBrIS;EkBsIT,sClBjKO;AH43Nf;;AqBhuNE;EASM,yDlBrKO;AHg4Nf;;AqBttNE;EAGM,yBlBlJS;EkBmJT,kIlB9KO;AHq4Nf;;AqB3tNE;EAQM,+FAAqG;EACrG,YAAY;ArButNpB;;AqBltNE;EAEI,clB/JW;AHm3NjB;;AqBv4NE;EAGM,mBlBkBS;AHs3NjB;;AqBn4NE;EAEI,gBlBjBS;AHs5Nf;;AqBv4NE;EAKM,mBlBQS;AH83NjB;;AqB34NE;EASM,kMAA8M;ArBs4NtN;;AqBj4NE;EAGM,8BlBJS;AHs4NjB;;AqB73NE;EAGM,mBlBZS;AH04NjB;;AqBz3NE;EAEI,aAAa;EACb,WAAW;ArB23NjB;;AqB93NE;EAMM,mBlBvBS;EkBwBT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB43NxB;;AqBt4NE;EAeI,qBlBhCW;AH25NjB;;AqB14NE;EAmBM,qBlBpCS;AH+5NjB;;AqBt3NE;EAGM,mClB5CS;EkB6CT,WlBzEO;AHg8Nf;;AqBl3NE;EAGM,6CAA4C;ArBm3NpD;;AqBt3NE;EAOM,6CAA4C;ArBm3NpD;;AqB92NE;EAEI,gBlB5FS;EkB6FT,qBlBjEW;AHi7NjB;;AqBn3NE;EAMM,mBlBpES;AHq7NjB;;AqB52NE;EAEI,clB3EW;AHy7NjB;;AqB12NE;EAGM,mBlBlFS;AH67NjB;;AqB92NE;;EAQM,sBlBnHO;AH89Nf;;AqBn3NE;EAaQ,2ClB5FO;EkB6FP,0ClB7FO;AHu8NjB;;AqBx3NE;EAkBQ,yClBjGO;EkBkGP,4ClBlGO;AH48NjB;;AqBr2NE;EAGM,yClB1GS;AHg9NjB;;AqBj2NE;EAGM,mBlBlHS;AHo9NjB;;AqBr2NE;EAOM,6ClBtHS;AHw9NjB;;AqBz2NE;EAWM,yBlB1HS;EkB2HT,0BlB3HS;AH69NjB;;AqB71NE;EAGM,mBlBnIS;EkBoIT,clBpIS;EkBqIT,sClBjKO;AH+/Nf;;AqBn2NE;EASM,yDlBrKO;AHmgOf;;AqBz1NE;EAGM,yBlBjJS;EkBkJT,kIlB9KO;AHwgOf;;AqB91NE;EAQM,+FAAqG;EACrG,YAAY;ArB01NpB;;AqBr1NE;EAEI,clB9JW;AHq/NjB;;AqB1gOE;EAGM,mBlBmBS;AHw/NjB;;AqBtgOE;EAEI,gBlBjBS;AHyhOf;;AqB1gOE;EAKM,mBlBSS;AHggOjB;;AqB9gOE;EASM,kMAA8M;ArBygOtN;;AqBpgOE;EAGM,6BlBHS;AHwgOjB;;AqBhgOE;EAGM,mBlBXS;AH4gOjB;;AqB5/NE;EAEI,aAAa;EACb,WAAW;ArB8/NjB;;AqBjgOE;EAMM,mBlBtBS;EkBuBT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB+/NxB;;AqBzgOE;EAeI,qBlB/BW;AH6hOjB;;AqB7gOE;EAmBM,qBlBnCS;AHiiOjB;;AqBz/NE;EAGM,kClB3CS;EkB4CT,WlBzEO;AHmkOf;;AqBr/NE;EAGM,6CAA4C;ArBs/NpD;;AqBz/NE;EAOM,6CAA4C;ArBs/NpD;;AqBj/NE;EAEI,gBlB5FS;EkB6FT,qBlBhEW;AHmjOjB;;AqBt/NE;EAMM,mBlBnES;AHujOjB;;AqB/+NE;EAEI,clB1EW;AH2jOjB;;AqB7+NE;EAGM,mBlBjFS;AH+jOjB;;AqBj/NE;;EAQM,sBlBnHO;AHimOf;;AqBt/NE;EAaQ,0ClB3FO;EkB4FP,yClB5FO;AHykOjB;;AqB3/NE;EAkBQ,wClBhGO;EkBiGP,2ClBjGO;AH8kOjB;;AqBx+NE;EAGM,wClBzGS;AHklOjB;;AqBp+NE;EAGM,mBlBjHS;AHslOjB;;AqBx+NE;EAOM,6ClBrHS;AH0lOjB;;AqB5+NE;EAWM,yBlBzHS;EkB0HT,0BlB1HS;AH+lOjB;;AqBh+NE;EAGM,mBlBlIS;EkBmIT,clBnIS;EkBoIT,sClBjKO;AHkoOf;;AqBt+NE;EASM,yDlBrKO;AHsoOf;;AqB59NE;EAGM,yBlBhJS;EkBiJT,kIlB9KO;AH2oOf;;AqBj+NE;EAQM,+FAAqG;EACrG,YAAY;ArB69NpB;;AqBx9NE;EAEI,clB7JW;AHunOjB;;AqB7oOE;EAGM,mBlBoBS;AH0nOjB;;AqBzoOE;EAEI,mBlBiEiB;AH0kOvB;;AqB7oOE;EAKM,mBlBUS;AHkoOjB;;AqBjpOE;EASM,yLAA8M;ArB4oOtN;;AqBvoOE;EAGM,8BlBFS;AH0oOjB;;AqBnoOE;EAGM,mBlBVS;AH8oOjB;;AqB/nOE;EAEI,aAAa;EACb,WAAW;ArBioOjB;;AqBpoOE;EAMM,mBlBrBS;EkBsBT,clB8Be;EkB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBkoOxB;;AqB5oOE;EAeI,qBlB9BW;AH+pOjB;;AqBhpOE;EAmBM,qBlBlCS;AHmqOjB;;AqB5nOE;EAGM,mClB1CS;EkB2CT,clBSe;AHonOvB;;AqBxnOE;EAGM,6CAA4C;ArBynOpD;;AqB5nOE;EAOM,6CAA4C;ArBynOpD;;AqBpnOE;EAEI,mBlBViB;EkBWjB,qBlB/DW;AHqrOjB;;AqBznOE;EAMM,mBlBlES;AHyrOjB;;AqBlnOE;EAEI,clBzEW;AH6rOjB;;AqBhnOE;EAGM,mBlBhFS;AHisOjB;;AqBpnOE;;EAQM,yBlBjCe;AHkpOvB;;AqBznOE;EAaQ,2ClB1FO;EkB2FP,0ClB3FO;AH2sOjB;;AqB9nOE;EAkBQ,yClB/FO;EkBgGP,4ClBhGO;AHgtOjB;;AqB3mOE;EAGM,yClBxGS;AHotOjB;;AqBvmOE;EAGM,mBlBhHS;AHwtOjB;;AqB3mOE;EAOM,6ClBpHS;AH4tOjB;;AqB/mOE;EAWM,yBlBxHS;EkByHT,0BlBzHS;AHiuOjB;;AqBnmOE;EAGM,mBlBjIS;EkBkIT,clBlIS;EkBmIT,4ClB/Ee;AHmrOvB;;AqBzmOE;EASM,4DlBnFe;AHurOvB;;AqB/lOE;EAGM,yBlB/IS;EkBgJT,4HlB5Fe;AH4rOvB;;AqBpmOE;EAQM,yFAAqG;EACrG,YAAY;ArBgmOpB;;AqB3lOE;EAEI,clB5JW;AHyvOjB;;AqBhxOE;EAGM,mBlBqBS;AH4vOjB;;AqB5wOE;EAEI,mBlBiEiB;AH6sOvB;;AqBhxOE;EAKM,mBlBWS;AHowOjB;;AqBpxOE;EASM,yLAA8M;ArB+wOtN;;AqB1wOE;EAGM,6BlBDS;AH4wOjB;;AqBtwOE;EAGM,mBlBTS;AHgxOjB;;AqBlwOE;EAEI,aAAa;EACb,WAAW;ArBowOjB;;AqBvwOE;EAMM,mBlBpBS;EkBqBT,clB8Be;EkB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBqwOxB;;AqB/wOE;EAeI,qBlB7BW;AHiyOjB;;AqBnxOE;EAmBM,qBlBjCS;AHqyOjB;;AqB/vOE;EAGM,kClBzCS;EkB0CT,clBSe;AHuvOvB;;AqB3vOE;EAGM,6CAA4C;ArB4vOpD;;AqB/vOE;EAOM,6CAA4C;ArB4vOpD;;AqBvvOE;EAEI,mBlBViB;EkBWjB,qBlB9DW;AHuzOjB;;AqB5vOE;EAMM,mBlBjES;AH2zOjB;;AqBrvOE;EAEI,clBxEW;AH+zOjB;;AqBnvOE;EAGM,mBlB/ES;AHm0OjB;;AqBvvOE;;EAQM,yBlBjCe;AHqxOvB;;AqB5vOE;EAaQ,0ClBzFO;EkB0FP,yClB1FO;AH60OjB;;AqBjwOE;EAkBQ,wClB9FO;EkB+FP,2ClB/FO;AHk1OjB;;AqB9uOE;EAGM,wClBvGS;AHs1OjB;;AqB1uOE;EAGM,mBlB/GS;AH01OjB;;AqB9uOE;EAOM,6ClBnHS;AH81OjB;;AqBlvOE;EAWM,yBlBvHS;EkBwHT,0BlBxHS;AHm2OjB;;AqBtuOE;EAGM,mBlBhIS;EkBiIT,clBjIS;EkBkIT,4ClB/Ee;AHszOvB;;AqB5uOE;EASM,4DlBnFe;AH0zOvB;;AqBluOE;EAGM,yBlB9IS;EkB+IT,4HlB5Fe;AH+zOvB;;AqBvuOE;EAQM,yFAAqG;EACrG,YAAY;ArBmuOpB;;AqB9tOE;EAEI,clB3JW;AH23OjB;;AqBn5OE;EAGM,mBlBsBS;AH83OjB;;AqB/4OE;EAEI,gBlBjBS;AHk6Of;;AqBn5OE;EAKM,mBlBYS;AHs4OjB;;AqBv5OE;EASM,kMAA8M;ArBk5OtN;;AqB74OE;EAGM,6BlBAS;AH84OjB;;AqBz4OE;EAGM,mBlBRS;AHk5OjB;;AqBr4OE;EAEI,aAAa;EACb,WAAW;ArBu4OjB;;AqB14OE;EAMM,mBlBnBS;EkBoBT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBw4OxB;;AqBl5OE;EAeI,qBlB5BW;AHm6OjB;;AqBt5OE;EAmBM,qBlBhCS;AHu6OjB;;AqBl4OE;EAGM,kClBxCS;EkByCT,WlBzEO;AH48Of;;AqB93OE;EAGM,6CAA4C;ArB+3OpD;;AqBl4OE;EAOM,6CAA4C;ArB+3OpD;;AqB13OE;EAEI,gBlB5FS;EkB6FT,qBlB7DW;AHy7OjB;;AqB/3OE;EAMM,mBlBhES;AH67OjB;;AqBx3OE;EAEI,clBvEW;AHi8OjB;;AqBt3OE;EAGM,mBlB9ES;AHq8OjB;;AqB13OE;;EAQM,sBlBnHO;AH0+Of;;AqB/3OE;EAaQ,0ClBxFO;EkByFP,yClBzFO;AH+8OjB;;AqBp4OE;EAkBQ,wClB7FO;EkB8FP,2ClB9FO;AHo9OjB;;AqBj3OE;EAGM,wClBtGS;AHw9OjB;;AqB72OE;EAGM,mBlB9GS;AH49OjB;;AqBj3OE;EAOM,6ClBlHS;AHg+OjB;;AqBr3OE;EAWM,yBlBtHS;EkBuHT,0BlBvHS;AHq+OjB;;AqBz2OE;EAGM,mBlB/HS;EkBgIT,clBhIS;EkBiIT,sClBjKO;AH2gPf;;AqB/2OE;EASM,yDlBrKO;AH+gPf;;AqBr2OE;EAGM,yBlB7IS;EkB8IT,kIlB9KO;AHohPf;;AqB12OE;EAQM,+FAAqG;EACrG,YAAY;ArBs2OpB;;AqBj2OE;EAEI,clB1JW;AH6/OjB;;AqBthPE;EAGM,mBlBuBS;AHggPjB;;AqBlhPE;EAEI,gBlBjBS;AHqiPf;;AqBthPE;EAKM,mBlBaS;AHwgPjB;;AqB1hPE;EASM,kMAA8M;ArBqhPtN;;AqBhhPE;EAGM,8BlBCS;AHghPjB;;AqB5gPE;EAGM,mBlBPS;AHohPjB;;AqBxgPE;EAEI,aAAa;EACb,WAAW;ArB0gPjB;;AqB7gPE;EAMM,mBlBlBS;EkBmBT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB2gPxB;;AqBrhPE;EAeI,qBlB3BW;AHqiPjB;;AqBzhPE;EAmBM,qBlB/BS;AHyiPjB;;AqBrgPE;EAGM,mClBvCS;EkBwCT,WlBzEO;AH+kPf;;AqBjgPE;EAGM,6CAA4C;ArBkgPpD;;AqBrgPE;EAOM,6CAA4C;ArBkgPpD;;AqB7/OE;EAEI,gBlB5FS;EkB6FT,qBlB5DW;AH2jPjB;;AqBlgPE;EAMM,mBlB/DS;AH+jPjB;;AqB3/OE;EAEI,clBtEW;AHmkPjB;;AqBz/OE;EAGM,mBlB7ES;AHukPjB;;AqB7/OE;;EAQM,sBlBnHO;AH6mPf;;AqBlgPE;EAaQ,2ClBvFO;EkBwFP,0ClBxFO;AHilPjB;;AqBvgPE;EAkBQ,yClB5FO;EkB6FP,4ClB7FO;AHslPjB;;AqBp/OE;EAGM,yClBrGS;AH0lPjB;;AqBh/OE;EAGM,mBlB7GS;AH8lPjB;;AqBp/OE;EAOM,6ClBjHS;AHkmPjB;;AqBx/OE;EAWM,yBlBrHS;EkBsHT,0BlBtHS;AHumPjB;;AqB5+OE;EAGM,mBlB9HS;EkB+HT,clB/HS;EkBgIT,sClBjKO;AH8oPf;;AqBl/OE;EASM,yDlBrKO;AHkpPf;;AqBx+OE;EAGM,yBlB5IS;EkB6IT,kIlB9KO;AHupPf;;AqB7+OE;EAQM,+FAAqG;EACrG,YAAY;ArBy+OpB;;AqBp+OE;EAEI,clBzJW;AH+nPjB;;AqBzpPE;EAGM,mBlBwBS;AHkoPjB;;AqBrpPE;EAEI,gBlBjBS;AHwqPf;;AqBzpPE;EAKM,mBlBcS;AH0oPjB;;AqB7pPE;EASM,kMAA8M;ArBwpPtN;;AqBnpPE;EAGM,8BlBES;AHkpPjB;;AqB/oPE;EAGM,mBlBNS;AHspPjB;;AqB3oPE;EAEI,aAAa;EACb,WAAW;ArB6oPjB;;AqBhpPE;EAMM,mBlBjBS;EkBkBT,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArB8oPxB;;AqBxpPE;EAeI,qBlB1BW;AHuqPjB;;AqB5pPE;EAmBM,qBlB9BS;AH2qPjB;;AqBxoPE;EAGM,mClBtCS;EkBuCT,WlBzEO;AHktPf;;AqBpoPE;EAGM,6CAA4C;ArBqoPpD;;AqBxoPE;EAOM,6CAA4C;ArBqoPpD;;AqBhoPE;EAEI,gBlB5FS;EkB6FT,qBlB3DW;AH6rPjB;;AqBroPE;EAMM,mBlB9DS;AHisPjB;;AqB9nPE;EAEI,clBrEW;AHqsPjB;;AqB5nPE;EAGM,mBlB5ES;AHysPjB;;AqBhoPE;;EAQM,sBlBnHO;AHgvPf;;AqBroPE;EAaQ,2ClBtFO;EkBuFP,0ClBvFO;AHmtPjB;;AqB1oPE;EAkBQ,yClB3FO;EkB4FP,4ClB5FO;AHwtPjB;;AqBvnPE;EAGM,yClBpGS;AH4tPjB;;AqBnnPE;EAGM,mBlB5GS;AHguPjB;;AqBvnPE;EAOM,6ClBhHS;AHouPjB;;AqB3nPE;EAWM,yBlBpHS;EkBqHT,0BlBrHS;AHyuPjB;;AqB/mPE;EAGM,mBlB7HS;EkB8HT,clB9HS;EkB+HT,sClBjKO;AHixPf;;AqBrnPE;EASM,yDlBrKO;AHqxPf;;AqB3mPE;EAGM,yBlB3IS;EkB4IT,kIlB9KO;AH0xPf;;AqBhnPE;EAQM,+FAAqG;EACrG,YAAY;ArB4mPpB;;AqBvmPE;EAEI,clBxJW;AHiwPjB;;AqB5xPE;EAGM,gBlBVO;AHuyPf;;AqBxxPE;EAEI,mBlBiEiB;AHytPvB;;AqB5xPE;EAKM,gBlBpBO;AH+yPf;;AqBhyPE;EASM,yLAA8M;ArB2xPtN;;AqBtxPE;EAGM,+BlBhCO;AHuzPf;;AqBlxPE;EAGM,gBlBxCO;AH2zPf;;AqB9wPE;EAEI,aAAa;EACb,WAAW;ArBgxPjB;;AqBnxPE;EAMM,gBlBnDO;EkBoDP,clB8Be;EkB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBixPxB;;AqB3xPE;EAeI,kBlB5DS;AH40Pf;;AqB/xPE;EAmBM,kBlBhEO;AHg1Pf;;AqB3wPE;EAGM,oClBxEO;EkByEP,clBSe;AHmwPvB;;AqBvwPE;EAGM,0CAA4C;ArBwwPpD;;AqB3wPE;EAOM,0CAA4C;ArBwwPpD;;AqBnwPE;EAEI,mBlBViB;EkBWjB,kBlB7FS;AHk2Pf;;AqBxwPE;EAMM,gBlBhGO;AHs2Pf;;AqBjwPE;EAEI,WlBvGS;AH02Pf;;AqB/vPE;EAGM,gBlB9GO;AH82Pf;;AqBnwPE;;EAQM,yBlBjCe;AHiyPvB;;AqBxwPE;EAaQ,4ClBxHK;EkByHL,2ClBzHK;AHw3Pf;;AqB7wPE;EAkBQ,0ClB7HK;EkB8HL,6ClB9HK;AH63Pf;;AqB1vPE;EAGM,0ClBtIO;AHi4Pf;;AqBtvPE;EAGM,gBlB9IO;AHq4Pf;;AqB1vPE;EAOM,uClBlJO;AHy4Pf;;AqB9vPE;EAWM,sBlBtJO;EkBuJP,uBlBvJO;AH84Pf;;AqBlvPE;EAGM,gBlB/JO;EkBgKP,WlBhKO;EkBiKP,4ClB/Ee;AHk0PvB;;AqBxvPE;EASM,yDlBnFe;AHs0PvB;;AqB9uPE;EAGM,sBlB7KO;EkB8KP,sHlB5Fe;AH20PvB;;AqBnvPE;EAQM,yFAAqG;EACrG,YAAY;ArB+uPpB;;AqB1uPE;EAEI,WlB1LS;AHs6Pf;;AqB/5PE;EAGM,mBlBJU;AHo6PlB;;AqB35PE;EAEI,gBlBjBS;AH86Pf;;AqB/5PE;EAKM,mBlBdU;AH46PlB;;AqBn6PE;EASM,kMAA8M;ArB85PtN;;AqBz5PE;EAGM,+BlB1BU;AHo7PlB;;AqBr5PE;EAGM,mBlBlCU;AHw7PlB;;AqBj5PE;EAEI,aAAa;EACb,WAAW;ArBm5PjB;;AqBt5PE;EAMM,mBlB7CU;EkB8CV,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBo5PxB;;AqB95PE;EAeI,qBlBtDY;AHy8PlB;;AqBl6PE;EAmBM,qBlB1DU;AH68PlB;;AqB94PE;EAGM,oClBlEU;EkBmEV,WlBzEO;AHw9Pf;;AqB14PE;EAGM,6CAA4C;ArB24PpD;;AqB94PE;EAOM,6CAA4C;ArB24PpD;;AqBt4PE;EAEI,gBlB5FS;EkB6FT,qBlBvFY;AH+9PlB;;AqB34PE;EAMM,mBlB1FU;AHm+PlB;;AqBp4PE;EAEI,clBjGY;AHu+PlB;;AqBl4PE;EAGM,mBlBxGU;AH2+PlB;;AqBt4PE;;EAQM,sBlBnHO;AHs/Pf;;AqB34PE;EAaQ,4ClBlHQ;EkBmHR,2ClBnHQ;AHq/PlB;;AqBh5PE;EAkBQ,0ClBvHQ;EkBwHR,6ClBxHQ;AH0/PlB;;AqB73PE;EAGM,0ClBhIU;AH8/PlB;;AqBz3PE;EAGM,mBlBxIU;AHkgQlB;;AqB73PE;EAOM,6ClB5IU;AHsgQlB;;AqBj4PE;EAWM,yBlBhJU;EkBiJV,0BlBjJU;AH2gQlB;;AqBr3PE;EAGM,mBlBzJU;EkB0JV,clB1JU;EkB2JV,sClBjKO;AHuhQf;;AqB33PE;EASM,yDlBrKO;AH2hQf;;AqBj3PE;EAGM,yBlBvKU;EkBwKV,kIlB9KO;AHgiQf;;AqBt3PE;EAQM,+FAAqG;EACrG,YAAY;ArBk3PpB;;AqB72PE;EAEI,clBpLY;AHmiQlB;;AqBliQE;EAGM,mBlBFU;AHqiQlB;;AqB9hQE;EAEI,gBlBjBS;AHijQf;;AqBliQE;EAKM,mBlBZU;AH6iQlB;;AqBtiQE;EASM,kMAA8M;ArBiiQtN;;AqB5hQE;EAGM,4BlBxBU;AHqjQlB;;AqBxhQE;EAGM,mBlBhCU;AHyjQlB;;AqBphQE;EAEI,aAAa;EACb,WAAW;ArBshQjB;;AqBzhQE;EAMM,mBlB3CU;EkB4CV,WlBpDO;EkBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;ArBuhQxB;;AqBjiQE;EAeI,qBlBpDY;AH0kQlB;;AqBriQE;EAmBM,qBlBxDU;AH8kQlB;;AqBjhQE;EAGM,iClBhEU;EkBiEV,WlBzEO;AH2lQf;;AqB7gQE;EAGM,6CAA4C;ArB8gQpD;;AqBjhQE;EAOM,6CAA4C;ArB8gQpD;;AqBzgQE;EAEI,gBlB5FS;EkB6FT,qBlBrFY;AHgmQlB;;AqB9gQE;EAMM,mBlBxFU;AHomQlB;;AqBvgQE;EAEI,clB/FY;AHwmQlB;;AqBrgQE;EAGM,mBlBtGU;AH4mQlB;;AqBzgQE;;EAQM,sBlBnHO;AHynQf;;AqB9gQE;EAaQ,yClBhHQ;EkBiHR,wClBjHQ;AHsnQlB;;AqBnhQE;EAkBQ,uClBrHQ;EkBsHR,0ClBtHQ;AH2nQlB;;AqBhgQE;EAGM,uClB9HU;AH+nQlB;;AqB5/PE;EAGM,mBlBtIU;AHmoQlB;;AqBhgQE;EAOM,6ClB1IU;AHuoQlB;;AqBpgQE;EAWM,yBlB9IU;EkB+IV,0BlB/IU;AH4oQlB;;AqBx/PE;EAGM,mBlBvJU;EkBwJV,clBxJU;EkByJV,sClBjKO;AH0pQf;;AqB9/PE;EASM,yDlBrKO;AH8pQf;;AqBp/PE;EAGM,yBlBrKU;EkBsKV,kIlB9KO;AHmqQf;;AqBz/PE;EAQM,+FAAqG;EACrG,YAAY;ArBq/PpB;;AqBh/PE;EAEI,clBlLY;AHoqQlB;;AsBvrQA;;;;;;;GtBgsQG;AsBprQH;EACE,yBnBEgB;EmBDhB,sBnBiNkC;EmBhNlC,eAAe;EACf,cAAc;EACd,qBAAqB;EACrB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;EAChB,sEAAsE;EACtE,yBAAiB;EAAjB,sBAAiB;EAAjB,qBAAiB;EAAjB,iBAAiB;EACjB,sBAAsB;EACtB,UAAU;AtBsrQZ;;AsBnsQA;EAgBI,sBnBmMgC;EmBlMhC,qBAAqB;EACrB,MAAM;EACN,uCAA+B;EAA/B,+BAA+B;AtBurQnC;;AsB1sQA;EAwBI,gDnBAa;AHsrQjB;;AsB9sQA;;;EA8BI,sBAAsB;EACtB,eAAe;EACf,mBAAmB;EACnB,eAAe;EACf,gBAAgB;EAChB,mBAAmB;EACnB,qBAAqB;EACrB,sBAAsB;AtBsrQ1B;;AsB3tQA;;EA0CI,kBAAkB;EAClB,UAAU;AtBsrQd;;AsBjuQA;;EA8CM,mBnB7CY;EmB8CZ,cnBkCiB;AHspQvB;;AsBvuQA;;EAoDQ,mBnB5BS;EmB6BT,WnBtDO;AH8uQf;;AsB7uQA;;EAoDQ,mBnB/CU;EmBgDV,WnBtDO;AHovQf;;AsBnvQA;;EAoDQ,mBnBrBS;EmBsBT,WnBtDO;AH0vQf;;AsBzvQA;;EAoDQ,mBnBnBS;EmBoBT,WnBtDO;AHgwQf;;AsB/vQA;;EAoDQ,mBnBtBS;EmBuBT,cnB4Be;AHorQvB;;AsBrwQA;;EAoDQ,mBnBxBS;EmByBT,WnBtDO;AH4wQf;;AsB3wQA;;EAoDQ,mBnBpDU;EmBqDV,cnB4Be;AHgsQvB;;AsBjxQA;;EAoDQ,mBnB7CU;EmB8CV,WnBtDO;AHwxQf;;AsBvxQA;;EA2DQ,mBfhEW;EeiEX,WnB7DO;AH8xQf;;AsB7xQA;;EA2DQ,mBf/DM;EegEN,WnB7DO;AHoyQf;;AsBnyQA;;EA2DQ,mBf7DO;Ee8DP,WnB7DO;AH0yQf;;AsBzyQA;;EA2DQ,mBf5DM;Ee6DN,cnBqBe;AH8tQvB;;AsB/yQA;;EA2DQ,mBf1DS;Ee2DT,WnB7DO;AHszQf;;AsBrzQA;;EA2DQ,mBfxDQ;EeyDR,WnB7DO;AH4zQf;;AsB3zQA;;EA2DQ,mBnBnCS;EmBoCT,WnB7DO;AHk0Qf;;AsBj0QA;;EA2DQ,mBnBlCS;EmBmCT,WnB7DO;AHw0Qf;;AsBv0QA;;EA2DQ,mBnBjCS;EmBkCT,WnB7DO;AH80Qf;;AsB70QA;;EA2DQ,mBnBhCS;EmBiCT,WnB7DO;AHo1Qf;;AsBn1QA;;EA2DQ,mBnB/BS;EmBgCT,WnB7DO;AH01Qf;;AsBz1QA;;EA2DQ,mBnB9BS;EmB+BT,cnBqBe;AH8wQvB;;AsB/1QA;;EA2DQ,mBnB7BS;EmB8BT,cnBqBe;AHoxQvB;;AsBr2QA;;EA2DQ,mBnB5BS;EmB6BT,WnB7DO;AH42Qf;;AsB32QA;;EA2DQ,mBnB3BS;EmB4BT,WnB7DO;AHk3Qf;;AsBj3QA;;EA2DQ,mBnB1BS;EmB2BT,WnB7DO;AHw3Qf;;AsBv3QA;;EA2DQ,gBnB5DO;EmB6DP,cnBqBe;AH4yQvB;;AsB73QA;;EA2DQ,mBnBtDU;EmBuDV,WnB7DO;AHo4Qf;;AsBn4QA;;EA2DQ,mBnBpDU;EmBqDV,WnB7DO;AH04Qf;;AsBz4QA;EAkEI,iCApEyC;EAqEzC,8BArEyC;AtBg5Q7C;;AsB94QA;EAuEI,kCAzEyC;EA0EzC,+BA1EyC;AtBq5Q7C;;AsBn5QA;;EA6EI,wBAAwB;EACxB,OAAO;EACP,SAAS;EACT,UAAU;EACV,kBAAkB;EAClB,MAAM;EACN,kBAAkB;EAClB,WAAW;AtB20Qf;;AsB/5QA;;;EA2FM,kBAAkB;EAClB,gBAAgB;EAChB,oBAAoB;AtB00Q1B;;AsBv6QA;;;EAqGM,kBAAkB;EAClB,gBAAgB;EAChB,oBAAoB;AtBw0Q1B;;AsB/6QA;;;EA+GM,kBAAkB;EAClB,yBAAyB;EACzB,oBAAoB;AtBs0Q1B;;AsBv7QA;EAwHI,eAAe;AtBm0QnB;;AsB37QA;;;;;;;EA6HM,eAAe;EACf,yBAAyB;EACzB,WAAW;AtBw0QjB;;AsBv8QA;EAoII,2BAA2B;AtBu0Q/B;;AsB38QA;EAyIM,gCAAgG;AtBs0QtG;;AsB/8QA;EA6IM,gCA/IuC;AtBq9Q7C;;AsBn9QA;;EAyJI,kCA3JyC;EA4JzC,+BA5JyC;AtB29Q7C;;AsBz9QA;;EA+JI,iCAjKyC;EAkKzC,8BAlKyC;AtBi+Q7C;;AUr+QI;EY4KA,qBnBjKc;AH89QlB;;AUz+QI;;EYgLE,yBAAsC;EACtC,WnB5KS;EmB6KT,qBAAkC;AtB8zQxC;;AUh/QI;;EYwLM,mBX3JW;EW4JX,WnBpLK;AHi/Qf;;AUt/QI;;EYwLM,mBnB7KQ;EmB8KR,WnBpLK;AHu/Qf;;AU5/QI;;EYwLM,mBXpJW;EWqJX,WnBpLK;AH6/Qf;;AUlgRI;;EYwLM,mBXlJW;EWmJX,WnBpLK;AHmgRf;;AUxgRI;;EYwLM,mBXrJW;EWsJX,cnBlGa;AHu7QvB;;AU9gRI;;EYwLM,mBXvJW;EWwJX,WnBpLK;AH+gRf;;AUphRI;;EYwLM,mBnBlLQ;EmBmLR,cnBlGa;AHm8QvB;;AU1hRI;;EYwLM,mBnB3KQ;EmB4KR,WnBpLK;AH2hRf;;AUhiRI;;EY+LM,mBXtJ2B;EWuJ3B,cnBzGa;AH+8QvB;;AUtiRI;;EY+LM,mBXrJqB;EWsJrB,WnB3LK;AHuiRf;;AU5iRI;;EY+LM,mBXpJuB;EWqJvB,cnBzGa;AH29QvB;;AUljRI;;EY+LM,mBXnJsB;EWoJtB,cnBzGa;AHi+QvB;;AUxjRI;;EY+LM,mBXlJyB;EWmJzB,cnBzGa;AHu+QvB;;AU9jRI;;EY+LM,mBXjJwB;EWkJxB,cnBzGa;AH6+QvB;;AUpkRI;;EY+LM,mBXlKW;EWmKX,WnB3LK;AHqkRf;;AU1kRI;;EY+LM,mBXjKW;EWkKX,WnB3LK;AH2kRf;;AUhlRI;;EY+LM,mBXhKW;EWiKX,WnB3LK;AHilRf;;AUtlRI;;EY+LM,mBX/JW;EWgKX,WnB3LK;AHulRf;;AU5lRI;;EY+LM,mBX9JW;EW+JX,WnB3LK;AH6lRf;;AUlmRI;;EY+LM,mBX7JW;EW8JX,cnBzGa;AHihRvB;;AUxmRI;;EY+LM,mBX5JW;EW6JX,cnBzGa;AHuhRvB;;AU9mRI;;EY+LM,mBX3JW;EW4JX,WnB3LK;AH+mRf;;AUpnRI;;EY+LM,mBX1JW;EW2JX,WnB3LK;AHqnRf;;AU1nRI;;EY+LM,mBXzJW;EW0JX,WnB3LK;AH2nRf;;AUhoRI;;EY+LM,gBnB1LK;EmB2LL,cnBzGa;AH+iRvB;;AUtoRI;;EY+LM,mBnBpLQ;EmBqLR,WnB3LK;AHuoRf;;AU5oRI;;EY+LM,mBnBlLQ;EmBmLR,WnB3LK;AH6oRf;;AUlpRI;EaJA,yBAAoC;EACpC,eAAe;AvB0pRnB;;AUvpRI;EaCE,4BAAuC;AvB0pR7C;;AU3pRI;;EaME,yBAAoC;AvB0pR1C;;AUhqRI;EaSE,yBAAqC;EACrC,WpBLS;AHgqRf;;AUrqRI;;;;EaiBE,yBAAmC;EACnC,WpBbS;AHwqRf;;AU7qRI;EasBE,yBpBTY;AHoqRlB;;AUjrRI;Ea0BE,qBAAiC;AvB2pRvC;;AUrrRI;Ea8BI,qBAAiC;EACjC,cAAc;AvB2pRtB;;AU1rRI;EaoCE,qBAAiC;AvB0pRvC;;AU9rRI;EawCE,yBpB3BY;EoB4BZ,qBAAiC;AvB0pRvC;;AUnsRI;;Ea6CI,WpBxCO;AHmsRf;;AUxsRI;;EakDI,kBpB7CO;AHwsRf;;AU7sRI;;;;Ea0DE,yBpB7CY;EoB8CZ,qBAAiC;AvB0pRvC;;AwBrtRA;EACE,uBAAuB;EACvB,uBAAuB;EACvB,sBAAsB;AxBwtRxB;;AwBptRA;EACE,iBAAiB;AxButRnB;;AwBptRA;EACE,SAAS;EACT,mBAAmB;EACnB,WAAW;EACX,YAAY;EACZ,gBAAgB;EAChB,UAAU;EACV,kBAAkB;EAClB,UAAU;AxButRZ;;AwBptRA;EACE,mBrBjBgB;EqBkBhB,0BrBhBgB;EqBiBhB,mBAAmB;AxButRrB;;AwBntRA;EACE,gBAAgB;EAChB,kBAAkB;AxBstRpB;;AUrvRI;EcqCA,yBAAqC;AxBotRzC;;AUzvRI;;;;;;;Ec8CA,qBrBnCc;AHwvRlB;;AUnwRI;;EckDA,yBrBrCc;AH2vRlB;;AUxwRI;EcqDA,yBAAsC;AxButR1C;;AU5wRI;EcwDA,yBAAoC;AxBwtRxC", "file": "adminlte.plugins.css", "sourcesContent": ["/*!\n *   AdminLTE v3.2.0\n *     Only Plugins\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n\n// Bootstrap\n// ---------------------------------------------------\n@import \"~bootstrap/scss/functions\";\n@import \"../bootstrap-variables\";\n@import \"~bootstrap/scss/mixins\";\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import \"../variables\";\n@import \"../variables-alt\";\n@import \"../mixins\";\n\n@import \"plugins\";\n", "/*!\n *   AdminLTE v3.2.0\n *     Only Plugins\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes shake {\n  0% {\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes wobble {\n  0% {\n    transform: none;\n  }\n  15% {\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n  30% {\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n  45% {\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n  60% {\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n  75% {\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n  100% {\n    transform: none;\n  }\n}\n\n.fc-button {\n  background: #f8f9fa;\n  background-image: none;\n  border-bottom-color: #ddd;\n  border-color: #ddd;\n  color: #495057;\n}\n\n.fc-button:hover, .fc-button:active, .fc-button.hover {\n  background-color: #e9e9e9;\n}\n\n.fc-header-title h2 {\n  color: #666;\n  font-size: 15px;\n  line-height: 1.6em;\n  margin-left: 10px;\n}\n\n.fc-header-right {\n  padding-right: 10px;\n}\n\n.fc-header-left {\n  padding-left: 10px;\n}\n\n.fc-widget-header {\n  background: #fafafa;\n}\n\n.fc-grid {\n  border: 0;\n  width: 100%;\n}\n\n.fc-widget-header:first-of-type,\n.fc-widget-content:first-of-type {\n  border-left: 0;\n  border-right: 0;\n}\n\n.fc-widget-header:last-of-type,\n.fc-widget-content:last-of-type {\n  border-right: 0;\n}\n\n.fc-toolbar,\n.fc-toolbar.fc-header-toolbar {\n  margin: 0;\n  padding: 1rem;\n}\n\n@media (max-width: 575.98px) {\n  .fc-toolbar {\n    flex-direction: column;\n  }\n  .fc-toolbar .fc-left {\n    order: 1;\n    margin-bottom: .5rem;\n  }\n  .fc-toolbar .fc-center {\n    order: 0;\n    margin-bottom: .375rem;\n  }\n  .fc-toolbar .fc-right {\n    order: 2;\n  }\n}\n\n.fc-day-number {\n  font-size: 20px;\n  font-weight: 300;\n  padding-right: 10px;\n}\n\n.fc-color-picker {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.fc-color-picker > li {\n  float: left;\n  font-size: 30px;\n  line-height: 30px;\n  margin-right: 5px;\n}\n\n.fc-color-picker > li .fa,\n.fc-color-picker > li .fas,\n.fc-color-picker > li .far,\n.fc-color-picker > li .fab,\n.fc-color-picker > li .fal,\n.fc-color-picker > li .fad,\n.fc-color-picker > li .svg-inline--fa,\n.fc-color-picker > li .ion {\n  transition: transform linear .3s;\n}\n\n.fc-color-picker > li .fa:hover,\n.fc-color-picker > li .fas:hover,\n.fc-color-picker > li .far:hover,\n.fc-color-picker > li .fab:hover,\n.fc-color-picker > li .fal:hover,\n.fc-color-picker > li .fad:hover,\n.fc-color-picker > li .svg-inline--fa:hover,\n.fc-color-picker > li .ion:hover {\n  transform: rotate(30deg);\n}\n\n#add-new-event {\n  transition: all linear .3s;\n}\n\n.external-event {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  border-radius: 0.25rem;\n  cursor: move;\n  font-weight: 700;\n  margin-bottom: 4px;\n  padding: 5px 10px;\n}\n\n.external-event:hover {\n  box-shadow: inset 0 0 90px rgba(0, 0, 0, 0.2);\n}\n\n.select2-container--default .select2-selection--single {\n  border: 1px solid #ced4da;\n  padding: 0.46875rem 0.75rem;\n  height: calc(2.25rem + 2px);\n}\n\n.select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-dropdown {\n  border: 1px solid #ced4da;\n}\n\n.select2-container--default .select2-results__option {\n  padding: 6px 12px;\n  user-select: none;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__rendered {\n  padding-left: 0;\n  height: auto;\n  margin-top: -3px;\n}\n\n.select2-container--default[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\n  padding-right: 6px;\n  padding-left: 20px;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__arrow {\n  height: 31px;\n  right: 6px;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__arrow b {\n  margin-top: 0;\n}\n\n.select2-container--default .select2-dropdown .select2-search__field,\n.select2-container--default .select2-search--inline .select2-search__field {\n  border: 1px solid #ced4da;\n}\n\n.select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-search--inline .select2-search__field:focus {\n  outline: none;\n  border: 1px solid #80bdff;\n}\n\n.select2-container--default .select2-dropdown.select2-dropdown--below {\n  border-top: 0;\n}\n\n.select2-container--default .select2-dropdown.select2-dropdown--above {\n  border-bottom: 0;\n}\n\n.select2-container--default .select2-results__option[aria-disabled='true'] {\n  color: #6c757d;\n}\n\n.select2-container--default .select2-results__option[aria-selected='true'] {\n  background-color: #dee2e6;\n}\n\n.select2-container--default .select2-results__option[aria-selected='true'], .select2-container--default .select2-results__option[aria-selected='true']:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-results__option--highlighted {\n  background-color: #007bff;\n  color: #fff;\n}\n\n.select2-container--default .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #0074f0;\n  color: #fff;\n}\n\n.select2-container--default .select2-selection--multiple {\n  border: 1px solid #ced4da;\n  min-height: calc(2.25rem + 2px);\n}\n\n.select2-container--default .select2-selection--multiple:focus {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding: 0 0.375rem 0.375rem;\n  margin-bottom: -0.375rem;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline {\n  width: 100%;\n  margin-left: 0.375rem;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline .select2-search__field {\n  width: 100% !important;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field {\n  border: 0;\n  margin-top: 6px;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #007bff;\n  border-color: #006fe6;\n  color: #fff;\n  padding: 0 10px;\n  margin-top: .31rem;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n  float: right;\n  margin-left: 5px;\n  margin-right: -2px;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-search.select2-search--inline .select2-search__field, .select2-container--default .select2-selection--multiple.text-sm .select2-search.select2-search--inline .select2-search__field {\n  margin-top: 8px;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__choice, .select2-container--default .select2-selection--multiple.text-sm .select2-selection__choice {\n  margin-top: .4rem;\n}\n\n.select2-container--default.select2-container--focus .select2-selection--single,\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #80bdff;\n}\n\n.select2-container--default.select2-container--focus .select2-search__field {\n  border: 0;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__rendered li {\n  padding-right: 10px;\n}\n\n.input-group-prepend ~ .select2-container--default .select2-selection {\n  border-bottom-left-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.input-group > .select2-container--default:not(:last-child) .select2-selection {\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.select2-container--bootstrap4.select2-container--focus .select2-selection {\n  box-shadow: none;\n}\n\nselect.form-control-sm ~ .select2-container--default {\n  font-size: 75%;\n}\n\n.text-sm .select2-container--default .select2-selection--single,\nselect.form-control-sm ~ .select2-container--default .select2-selection--single {\n  height: calc(1.8125rem + 2px);\n}\n\n.text-sm .select2-container--default .select2-selection--single .select2-selection__rendered,\nselect.form-control-sm ~ .select2-container--default .select2-selection--single .select2-selection__rendered {\n  margin-top: -.4rem;\n}\n\n.text-sm .select2-container--default .select2-selection--single .select2-selection__arrow,\nselect.form-control-sm ~ .select2-container--default .select2-selection--single .select2-selection__arrow {\n  top: -.12rem;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple {\n  min-height: calc(1.8125rem + 2px);\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding: 0 0.25rem 0.25rem;\n  margin-top: -0.1rem;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline {\n  margin-left: 0.25rem;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field {\n  margin-top: 6px;\n}\n\n.maximized-card .select2-dropdown {\n  z-index: 9999;\n}\n\n.select2-primary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-primary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-primary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-primary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-primary .select2-search--inline .select2-search__field:focus,\n.select2-primary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-primary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-primary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #80bdff;\n}\n\n.select2-container--default .select2-primary .select2-results__option--highlighted,\n.select2-primary .select2-container--default .select2-results__option--highlighted {\n  background-color: #007bff;\n  color: #fff;\n}\n\n.select2-container--default .select2-primary .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-primary .select2-results__option--highlighted[aria-selected]:hover,\n.select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #0074f0;\n  color: #fff;\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple:focus,\n.select2-primary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice,\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #007bff;\n  border-color: #006fe6;\n  color: #fff;\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice__remove,\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-primary.select2-container--focus .select2-selection--multiple,\n.select2-primary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #80bdff;\n}\n\n.select2-secondary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-secondary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-secondary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-secondary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-secondary .select2-search--inline .select2-search__field:focus,\n.select2-secondary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-secondary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-secondary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .select2-secondary .select2-results__option--highlighted,\n.select2-secondary .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-secondary .select2-results__option--highlighted[aria-selected]:hover,\n.select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple:focus,\n.select2-secondary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice,\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice__remove,\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary.select2-container--focus .select2-selection--multiple,\n.select2-secondary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.select2-success + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-success + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-success.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-success .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-success .select2-search--inline .select2-search__field:focus,\n.select2-success .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-success .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-success .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #71dd8a;\n}\n\n.select2-container--default .select2-success .select2-results__option--highlighted,\n.select2-success .select2-container--default .select2-results__option--highlighted {\n  background-color: #28a745;\n  color: #fff;\n}\n\n.select2-container--default .select2-success .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-success .select2-results__option--highlighted[aria-selected]:hover,\n.select2-success .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-success .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #259b40;\n  color: #fff;\n}\n\n.select2-container--default .select2-success .select2-selection--multiple:focus,\n.select2-success .select2-container--default .select2-selection--multiple:focus {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice,\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #28a745;\n  border-color: #23923d;\n  color: #fff;\n}\n\n.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice__remove,\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-success.select2-container--focus .select2-selection--multiple,\n.select2-success .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #71dd8a;\n}\n\n.select2-info + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-info + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-info.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-info .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-info .select2-search--inline .select2-search__field:focus,\n.select2-info .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-info .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-info .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #63d9ec;\n}\n\n.select2-container--default .select2-info .select2-results__option--highlighted,\n.select2-info .select2-container--default .select2-results__option--highlighted {\n  background-color: #17a2b8;\n  color: #fff;\n}\n\n.select2-container--default .select2-info .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-info .select2-results__option--highlighted[aria-selected]:hover,\n.select2-info .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-info .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1596aa;\n  color: #fff;\n}\n\n.select2-container--default .select2-info .select2-selection--multiple:focus,\n.select2-info .select2-container--default .select2-selection--multiple:focus {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice,\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #17a2b8;\n  border-color: #148ea1;\n  color: #fff;\n}\n\n.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice__remove,\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-info.select2-container--focus .select2-selection--multiple,\n.select2-info .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #63d9ec;\n}\n\n.select2-warning + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-warning + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-warning.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-warning .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-warning .select2-search--inline .select2-search__field:focus,\n.select2-warning .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-warning .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-warning .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #ffe187;\n}\n\n.select2-container--default .select2-warning .select2-results__option--highlighted,\n.select2-warning .select2-container--default .select2-results__option--highlighted {\n  background-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-warning .select2-results__option--highlighted[aria-selected]:hover,\n.select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7b900;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple:focus,\n.select2-warning .select2-container--default .select2-selection--multiple:focus {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice,\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #ffc107;\n  border-color: #edb100;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice__remove,\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning.select2-container--focus .select2-selection--multiple,\n.select2-warning .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #ffe187;\n}\n\n.select2-danger + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-danger + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-danger.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-danger .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-danger .select2-search--inline .select2-search__field:focus,\n.select2-danger .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-danger .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-danger .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #efa2a9;\n}\n\n.select2-container--default .select2-danger .select2-results__option--highlighted,\n.select2-danger .select2-container--default .select2-results__option--highlighted {\n  background-color: #dc3545;\n  color: #fff;\n}\n\n.select2-container--default .select2-danger .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-danger .select2-results__option--highlighted[aria-selected]:hover,\n.select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #da2839;\n  color: #fff;\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple:focus,\n.select2-danger .select2-container--default .select2-selection--multiple:focus {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice,\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #dc3545;\n  border-color: #d32535;\n  color: #fff;\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice__remove,\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-danger.select2-container--focus .select2-selection--multiple,\n.select2-danger .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #efa2a9;\n}\n\n.select2-light + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.select2-light + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .select2-light.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-light .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-light .select2-search--inline .select2-search__field:focus,\n.select2-light .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-light .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-light .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .select2-light .select2-results__option--highlighted,\n.select2-light .select2-container--default .select2-results__option--highlighted {\n  background-color: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-light .select2-results__option--highlighted[aria-selected]:hover,\n.select2-light .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-light .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #eff1f4;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light .select2-selection--multiple:focus,\n.select2-light .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice,\n.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f8f9fa;\n  border-color: #e9ecef;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice__remove,\n.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light.select2-container--focus .select2-selection--multiple,\n.select2-light .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.select2-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-dark .select2-search--inline .select2-search__field:focus,\n.select2-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .select2-dark .select2-results__option--highlighted,\n.select2-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .select2-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-dark .select2-results__option--highlighted[aria-selected]:hover,\n.select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple:focus,\n.select2-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice,\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice__remove,\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-dark.select2-container--focus .select2-selection--multiple,\n.select2-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.select2-lightblue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #99c5de;\n}\n\n.select2-lightblue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #99c5de;\n}\n\n.select2-container--default .select2-lightblue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lightblue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lightblue .select2-search--inline .select2-search__field:focus,\n.select2-lightblue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-lightblue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-lightblue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #99c5de;\n}\n\n.select2-container--default .select2-lightblue .select2-results__option--highlighted,\n.select2-lightblue .select2-container--default .select2-results__option--highlighted {\n  background-color: #3c8dbc;\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-lightblue .select2-results__option--highlighted[aria-selected]:hover,\n.select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #3884b0;\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple:focus,\n.select2-lightblue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #99c5de;\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice,\n.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3c8dbc;\n  border-color: #367fa9;\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove,\n.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue.select2-container--focus .select2-selection--multiple,\n.select2-lightblue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #99c5de;\n}\n\n.select2-navy + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #005ebf;\n}\n\n.select2-navy + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #005ebf;\n}\n\n.select2-container--default .select2-navy.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-navy .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-navy .select2-search--inline .select2-search__field:focus,\n.select2-navy .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-navy .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-navy .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #005ebf;\n}\n\n.select2-container--default .select2-navy .select2-results__option--highlighted,\n.select2-navy .select2-container--default .select2-results__option--highlighted {\n  background-color: #001f3f;\n  color: #fff;\n}\n\n.select2-container--default .select2-navy .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-navy .select2-results__option--highlighted[aria-selected]:hover,\n.select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #001730;\n  color: #fff;\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple:focus,\n.select2-navy .select2-container--default .select2-selection--multiple:focus {\n  border-color: #005ebf;\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice,\n.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #001f3f;\n  border-color: #001226;\n  color: #fff;\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice__remove,\n.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-navy.select2-container--focus .select2-selection--multiple,\n.select2-navy .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #005ebf;\n}\n\n.select2-olive + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #87cfaf;\n}\n\n.select2-olive + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #87cfaf;\n}\n\n.select2-container--default .select2-olive.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-olive .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-olive .select2-search--inline .select2-search__field:focus,\n.select2-olive .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-olive .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-olive .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #87cfaf;\n}\n\n.select2-container--default .select2-olive .select2-results__option--highlighted,\n.select2-olive .select2-container--default .select2-results__option--highlighted {\n  background-color: #3d9970;\n  color: #fff;\n}\n\n.select2-container--default .select2-olive .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-olive .select2-results__option--highlighted[aria-selected]:hover,\n.select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #398e68;\n  color: #fff;\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple:focus,\n.select2-olive .select2-container--default .select2-selection--multiple:focus {\n  border-color: #87cfaf;\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice,\n.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3d9970;\n  border-color: #368763;\n  color: #fff;\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice__remove,\n.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-olive.select2-container--focus .select2-selection--multiple,\n.select2-olive .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #87cfaf;\n}\n\n.select2-lime + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #81ffb8;\n}\n\n.select2-lime + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #81ffb8;\n}\n\n.select2-container--default .select2-lime.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lime .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lime .select2-search--inline .select2-search__field:focus,\n.select2-lime .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-lime .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-lime .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #81ffb8;\n}\n\n.select2-container--default .select2-lime .select2-results__option--highlighted,\n.select2-lime .select2-container--default .select2-results__option--highlighted {\n  background-color: #01ff70;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-lime .select2-results__option--highlighted[aria-selected]:hover,\n.select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #00f169;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple:focus,\n.select2-lime .select2-container--default .select2-selection--multiple:focus {\n  border-color: #81ffb8;\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice,\n.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #01ff70;\n  border-color: #00e765;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice__remove,\n.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime.select2-container--focus .select2-selection--multiple,\n.select2-lime .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #81ffb8;\n}\n\n.select2-fuchsia + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f88adf;\n}\n\n.select2-fuchsia + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f88adf;\n}\n\n.select2-container--default .select2-fuchsia.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-fuchsia .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-fuchsia .select2-search--inline .select2-search__field:focus,\n.select2-fuchsia .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-fuchsia .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-fuchsia .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f88adf;\n}\n\n.select2-container--default .select2-fuchsia .select2-results__option--highlighted,\n.select2-fuchsia .select2-container--default .select2-results__option--highlighted {\n  background-color: #f012be;\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-fuchsia .select2-results__option--highlighted[aria-selected]:hover,\n.select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e40eb4;\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple:focus,\n.select2-fuchsia .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f88adf;\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice,\n.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f012be;\n  border-color: #db0ead;\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove,\n.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia.select2-container--focus .select2-selection--multiple,\n.select2-fuchsia .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f88adf;\n}\n\n.select2-maroon + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f083ab;\n}\n\n.select2-maroon + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f083ab;\n}\n\n.select2-container--default .select2-maroon.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-maroon .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-maroon .select2-search--inline .select2-search__field:focus,\n.select2-maroon .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-maroon .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-maroon .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f083ab;\n}\n\n.select2-container--default .select2-maroon .select2-results__option--highlighted,\n.select2-maroon .select2-container--default .select2-results__option--highlighted {\n  background-color: #d81b60;\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-maroon .select2-results__option--highlighted[aria-selected]:hover,\n.select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #ca195a;\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple:focus,\n.select2-maroon .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f083ab;\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice,\n.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #d81b60;\n  border-color: #c11856;\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice__remove,\n.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon.select2-container--focus .select2-selection--multiple,\n.select2-maroon .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f083ab;\n}\n\n.select2-blue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-blue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-blue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-blue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-blue .select2-search--inline .select2-search__field:focus,\n.select2-blue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-blue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-blue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #80bdff;\n}\n\n.select2-container--default .select2-blue .select2-results__option--highlighted,\n.select2-blue .select2-container--default .select2-results__option--highlighted {\n  background-color: #007bff;\n  color: #fff;\n}\n\n.select2-container--default .select2-blue .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-blue .select2-results__option--highlighted[aria-selected]:hover,\n.select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #0074f0;\n  color: #fff;\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple:focus,\n.select2-blue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice,\n.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #007bff;\n  border-color: #006fe6;\n  color: #fff;\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice__remove,\n.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-blue.select2-container--focus .select2-selection--multiple,\n.select2-blue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #80bdff;\n}\n\n.select2-indigo + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.select2-indigo + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.select2-container--default .select2-indigo.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-indigo .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-indigo .select2-search--inline .select2-search__field:focus,\n.select2-indigo .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-indigo .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-indigo .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b389f9;\n}\n\n.select2-container--default .select2-indigo .select2-results__option--highlighted,\n.select2-indigo .select2-container--default .select2-results__option--highlighted {\n  background-color: #6610f2;\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-indigo .select2-results__option--highlighted[aria-selected]:hover,\n.select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #5f0de6;\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple:focus,\n.select2-indigo .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b389f9;\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice,\n.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6610f2;\n  border-color: #5b0cdd;\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice__remove,\n.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo.select2-container--focus .select2-selection--multiple,\n.select2-indigo .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b389f9;\n}\n\n.select2-purple + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.select2-purple + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .select2-purple.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-purple .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-purple .select2-search--inline .select2-search__field:focus,\n.select2-purple .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-purple .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-purple .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b8a2e0;\n}\n\n.select2-container--default .select2-purple .select2-results__option--highlighted,\n.select2-purple .select2-container--default .select2-results__option--highlighted {\n  background-color: #6f42c1;\n  color: #fff;\n}\n\n.select2-container--default .select2-purple .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-purple .select2-results__option--highlighted[aria-selected]:hover,\n.select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #683cb8;\n  color: #fff;\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple:focus,\n.select2-purple .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice,\n.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6f42c1;\n  border-color: #643ab0;\n  color: #fff;\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice__remove,\n.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-purple.select2-container--focus .select2-selection--multiple,\n.select2-purple .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b8a2e0;\n}\n\n.select2-pink + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.select2-pink + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .select2-pink.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-pink .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-pink .select2-search--inline .select2-search__field:focus,\n.select2-pink .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-pink .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-pink .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f6b0d0;\n}\n\n.select2-container--default .select2-pink .select2-results__option--highlighted,\n.select2-pink .select2-container--default .select2-results__option--highlighted {\n  background-color: #e83e8c;\n  color: #fff;\n}\n\n.select2-container--default .select2-pink .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-pink .select2-results__option--highlighted[aria-selected]:hover,\n.select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e63084;\n  color: #fff;\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple:focus,\n.select2-pink .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice,\n.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #e83e8c;\n  border-color: #e5277e;\n  color: #fff;\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice__remove,\n.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-pink.select2-container--focus .select2-selection--multiple,\n.select2-pink .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f6b0d0;\n}\n\n.select2-red + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-red + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-red.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-red .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-red .select2-search--inline .select2-search__field:focus,\n.select2-red .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-red .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-red .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #efa2a9;\n}\n\n.select2-container--default .select2-red .select2-results__option--highlighted,\n.select2-red .select2-container--default .select2-results__option--highlighted {\n  background-color: #dc3545;\n  color: #fff;\n}\n\n.select2-container--default .select2-red .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-red .select2-results__option--highlighted[aria-selected]:hover,\n.select2-red .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-red .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #da2839;\n  color: #fff;\n}\n\n.select2-container--default .select2-red .select2-selection--multiple:focus,\n.select2-red .select2-container--default .select2-selection--multiple:focus {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice,\n.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #dc3545;\n  border-color: #d32535;\n  color: #fff;\n}\n\n.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice__remove,\n.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-red.select2-container--focus .select2-selection--multiple,\n.select2-red .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #efa2a9;\n}\n\n.select2-orange + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #fec392;\n}\n\n.select2-orange + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #fec392;\n}\n\n.select2-container--default .select2-orange.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-orange .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-orange .select2-search--inline .select2-search__field:focus,\n.select2-orange .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-orange .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-orange .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #fec392;\n}\n\n.select2-container--default .select2-orange .select2-results__option--highlighted,\n.select2-orange .select2-container--default .select2-results__option--highlighted {\n  background-color: #fd7e14;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-orange .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-orange .select2-results__option--highlighted[aria-selected]:hover,\n.select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #fd7605;\n  color: #fff;\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple:focus,\n.select2-orange .select2-container--default .select2-selection--multiple:focus {\n  border-color: #fec392;\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice,\n.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fd7e14;\n  border-color: #f57102;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice__remove,\n.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-orange.select2-container--focus .select2-selection--multiple,\n.select2-orange .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #fec392;\n}\n\n.select2-yellow + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-yellow + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-yellow.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-yellow .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-yellow .select2-search--inline .select2-search__field:focus,\n.select2-yellow .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-yellow .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-yellow .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #ffe187;\n}\n\n.select2-container--default .select2-yellow .select2-results__option--highlighted,\n.select2-yellow .select2-container--default .select2-results__option--highlighted {\n  background-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-yellow .select2-results__option--highlighted[aria-selected]:hover,\n.select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7b900;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple:focus,\n.select2-yellow .select2-container--default .select2-selection--multiple:focus {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice,\n.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #ffc107;\n  border-color: #edb100;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice__remove,\n.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow.select2-container--focus .select2-selection--multiple,\n.select2-yellow .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #ffe187;\n}\n\n.select2-green + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-green + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-green.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-green .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-green .select2-search--inline .select2-search__field:focus,\n.select2-green .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-green .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-green .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #71dd8a;\n}\n\n.select2-container--default .select2-green .select2-results__option--highlighted,\n.select2-green .select2-container--default .select2-results__option--highlighted {\n  background-color: #28a745;\n  color: #fff;\n}\n\n.select2-container--default .select2-green .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-green .select2-results__option--highlighted[aria-selected]:hover,\n.select2-green .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-green .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #259b40;\n  color: #fff;\n}\n\n.select2-container--default .select2-green .select2-selection--multiple:focus,\n.select2-green .select2-container--default .select2-selection--multiple:focus {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice,\n.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #28a745;\n  border-color: #23923d;\n  color: #fff;\n}\n\n.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice__remove,\n.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-green.select2-container--focus .select2-selection--multiple,\n.select2-green .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #71dd8a;\n}\n\n.select2-teal + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.select2-teal + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .select2-teal.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-teal .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-teal .select2-search--inline .select2-search__field:focus,\n.select2-teal .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-teal .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-teal .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #7eeaca;\n}\n\n.select2-container--default .select2-teal .select2-results__option--highlighted,\n.select2-teal .select2-container--default .select2-results__option--highlighted {\n  background-color: #20c997;\n  color: #fff;\n}\n\n.select2-container--default .select2-teal .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-teal .select2-results__option--highlighted[aria-selected]:hover,\n.select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1ebc8d;\n  color: #fff;\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple:focus,\n.select2-teal .select2-container--default .select2-selection--multiple:focus {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice,\n.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #20c997;\n  border-color: #1cb386;\n  color: #fff;\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice__remove,\n.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-teal.select2-container--focus .select2-selection--multiple,\n.select2-teal .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #7eeaca;\n}\n\n.select2-cyan + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-cyan + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-cyan.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-cyan .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-cyan .select2-search--inline .select2-search__field:focus,\n.select2-cyan .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-cyan .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-cyan .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #63d9ec;\n}\n\n.select2-container--default .select2-cyan .select2-results__option--highlighted,\n.select2-cyan .select2-container--default .select2-results__option--highlighted {\n  background-color: #17a2b8;\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-cyan .select2-results__option--highlighted[aria-selected]:hover,\n.select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1596aa;\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple:focus,\n.select2-cyan .select2-container--default .select2-selection--multiple:focus {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice,\n.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #17a2b8;\n  border-color: #148ea1;\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice__remove,\n.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan.select2-container--focus .select2-selection--multiple,\n.select2-cyan .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #63d9ec;\n}\n\n.select2-white + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.select2-white + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .select2-white.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-white .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-white .select2-search--inline .select2-search__field:focus,\n.select2-white .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-white .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-white .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .select2-white .select2-results__option--highlighted,\n.select2-white .select2-container--default .select2-results__option--highlighted {\n  background-color: #fff;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-white .select2-results__option--highlighted[aria-selected]:hover,\n.select2-white .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-white .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7f7f7;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white .select2-selection--multiple:focus,\n.select2-white .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice,\n.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fff;\n  border-color: #f2f2f2;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice__remove,\n.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white.select2-container--focus .select2-selection--multiple,\n.select2-white .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.select2-gray + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-gray + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-gray.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray .select2-search--inline .select2-search__field:focus,\n.select2-gray .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-gray .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-gray .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .select2-gray .select2-results__option--highlighted,\n.select2-gray .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-gray .select2-results__option--highlighted[aria-selected]:hover,\n.select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple:focus,\n.select2-gray .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice,\n.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice__remove,\n.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-gray.select2-container--focus .select2-selection--multiple,\n.select2-gray .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.select2-gray-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-gray-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-gray-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray-dark .select2-search--inline .select2-search__field:focus,\n.select2-gray-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-gray-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-gray-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .select2-gray-dark .select2-results__option--highlighted,\n.select2-gray-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-gray-dark .select2-results__option--highlighted[aria-selected]:hover,\n.select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple:focus,\n.select2-gray-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice,\n.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove,\n.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark.select2-container--focus .select2-selection--multiple,\n.select2-gray-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.dark-mode .select2-selection {\n  background-color: #343a40;\n  border-color: #6c757d;\n}\n\n.dark-mode .select2-container--disabled .select2-selection--single {\n  background-color: #454d55;\n}\n\n.dark-mode .select2-selection--single {\n  background-color: #343a40;\n  border-color: #6c757d;\n}\n\n.dark-mode .select2-selection--single .select2-selection__rendered {\n  color: #fff;\n}\n\n.dark-mode .select2-dropdown .select2-search__field,\n.dark-mode .select2-search--inline .select2-search__field {\n  background-color: #343a40;\n  border-color: #6c757d;\n  color: white;\n}\n\n.dark-mode .select2-dropdown {\n  background-color: #343a40;\n  border-color: #6c757d;\n  color: white;\n}\n\n.dark-mode .select2-results__option[aria-selected=\"true\"] {\n  background-color: #3f474e !important;\n  color: #dee2e6;\n}\n\n.dark-mode .select2-container .select2-search--inline .select2-search__field {\n  background-color: transparent;\n  color: #fff;\n}\n\n.dark-mode .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {\n  color: #fff;\n}\n\n.dark-mode .select2-primary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #85a7ca;\n}\n\n.dark-mode .select2-primary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-primary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-primary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-primary .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-primary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-primary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-primary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-results__option--highlighted,\n.dark-mode .select2-primary .select2-container--default .select2-results__option--highlighted {\n  background-color: #3f6791;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-primary .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #3a5f86;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-selection--multiple:focus,\n.dark-mode .select2-primary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3f6791;\n  border-color: #375a7f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-primary.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-primary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #85a7ca;\n}\n\n.dark-mode .select2-secondary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.dark-mode .select2-secondary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-secondary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-secondary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-secondary .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-secondary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-secondary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-secondary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-results__option--highlighted,\n.dark-mode .select2-secondary .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-secondary .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple:focus,\n.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-secondary.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-secondary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.dark-mode .select2-success + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #3dffcd;\n}\n\n.dark-mode .select2-success + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-success.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-success .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-success .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-success .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-success .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-success .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-results__option--highlighted,\n.dark-mode .select2-success .select2-container--default .select2-results__option--highlighted {\n  background-color: #00bc8c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-success .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-success .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-success .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #00ad81;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-selection--multiple:focus,\n.dark-mode .select2-success .select2-container--default .select2-selection--multiple:focus {\n  border-color: #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #00bc8c;\n  border-color: #00a379;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-success .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-success.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-success .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #3dffcd;\n}\n\n.dark-mode .select2-info + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #a0cfee;\n}\n\n.dark-mode .select2-info + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-info.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-info .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-info .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-info .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-info .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-info .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-results__option--highlighted,\n.dark-mode .select2-info .select2-container--default .select2-results__option--highlighted {\n  background-color: #3498db;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-info .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-info .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-info .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2791d9;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-selection--multiple:focus,\n.dark-mode .select2-info .select2-container--default .select2-selection--multiple:focus {\n  border-color: #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3498db;\n  border-color: #258cd1;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-info .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-info.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-info .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #a0cfee;\n}\n\n.dark-mode .select2-warning + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f9cf8b;\n}\n\n.dark-mode .select2-warning + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-warning.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-warning .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-warning .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-warning .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-warning .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-warning .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-results__option--highlighted,\n.dark-mode .select2-warning .select2-container--default .select2-results__option--highlighted {\n  background-color: #f39c12;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-warning .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #ea940c;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-selection--multiple:focus,\n.dark-mode .select2-warning .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f39c12;\n  border-color: #e08e0b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-warning.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-warning .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f9cf8b;\n}\n\n.dark-mode .select2-danger + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f5b4ae;\n}\n\n.dark-mode .select2-danger + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-danger.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-danger .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-danger .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-danger .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-danger .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-danger .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-results__option--highlighted,\n.dark-mode .select2-danger .select2-container--default .select2-results__option--highlighted {\n  background-color: #e74c3c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-danger .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e53f2e;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-selection--multiple:focus,\n.dark-mode .select2-danger .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #e74c3c;\n  border-color: #e43725;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-danger.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-danger .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f5b4ae;\n}\n\n.dark-mode .select2-light + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.dark-mode .select2-light + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .dark-mode .select2-light.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-light .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-light .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-light .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-light .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-light .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-results__option--highlighted,\n.dark-mode .select2-light .select2-container--default .select2-results__option--highlighted {\n  background-color: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-light .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-light .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-light .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #eff1f4;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-selection--multiple:focus,\n.dark-mode .select2-light .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f8f9fa;\n  border-color: #e9ecef;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-light .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-light.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-light .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.dark-mode .select2-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.dark-mode .select2-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-dark .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-results__option--highlighted,\n.dark-mode .select2-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-dark .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-selection--multiple:focus,\n.dark-mode .select2-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-dark.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.dark-mode .select2-lightblue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #e6f1f7;\n}\n\n.dark-mode .select2-lightblue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #e6f1f7;\n}\n\n.select2-container--default .dark-mode .select2-lightblue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-lightblue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-lightblue .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-lightblue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-lightblue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-lightblue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #e6f1f7;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-results__option--highlighted,\n.dark-mode .select2-lightblue .select2-container--default .select2-results__option--highlighted {\n  background-color: #86bad8;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-lightblue .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #7ab3d5;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple:focus,\n.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #e6f1f7;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #86bad8;\n  border-color: #72afd2;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lightblue.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-lightblue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #e6f1f7;\n}\n\n.dark-mode .select2-navy + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #006ad8;\n}\n\n.dark-mode .select2-navy + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #006ad8;\n}\n\n.select2-container--default .dark-mode .select2-navy.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-navy .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-navy .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-navy .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-navy .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-navy .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #006ad8;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-results__option--highlighted,\n.dark-mode .select2-navy .select2-container--default .select2-results__option--highlighted {\n  background-color: #002c59;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-navy .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #002449;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-selection--multiple:focus,\n.dark-mode .select2-navy .select2-container--default .select2-selection--multiple:focus {\n  border-color: #006ad8;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #002c59;\n  border-color: #001f3f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-navy.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-navy .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #006ad8;\n}\n\n.dark-mode .select2-olive + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #cfecdf;\n}\n\n.dark-mode .select2-olive + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #cfecdf;\n}\n\n.select2-container--default .dark-mode .select2-olive.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-olive .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-olive .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-olive .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-olive .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-olive .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #cfecdf;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-results__option--highlighted,\n.dark-mode .select2-olive .select2-container--default .select2-results__option--highlighted {\n  background-color: #74c8a3;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-olive .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #69c39b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-selection--multiple:focus,\n.dark-mode .select2-olive .select2-container--default .select2-selection--multiple:focus {\n  border-color: #cfecdf;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #74c8a3;\n  border-color: #62c096;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-olive.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-olive .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #cfecdf;\n}\n\n.dark-mode .select2-lime + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #e7fff1;\n}\n\n.dark-mode .select2-lime + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #e7fff1;\n}\n\n.select2-container--default .dark-mode .select2-lime.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-lime .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-lime .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-lime .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-lime .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-lime .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #e7fff1;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-results__option--highlighted,\n.dark-mode .select2-lime .select2-container--default .select2-results__option--highlighted {\n  background-color: #67ffa9;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-lime .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #58ffa1;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-selection--multiple:focus,\n.dark-mode .select2-lime .select2-container--default .select2-selection--multiple:focus {\n  border-color: #e7fff1;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #67ffa9;\n  border-color: #4eff9b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lime.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-lime .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #e7fff1;\n}\n\n.dark-mode .select2-fuchsia + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #feeaf9;\n}\n\n.dark-mode .select2-fuchsia + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #feeaf9;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-fuchsia .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-fuchsia .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-fuchsia .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-fuchsia .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-fuchsia .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #feeaf9;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-results__option--highlighted,\n.dark-mode .select2-fuchsia .select2-container--default .select2-results__option--highlighted {\n  background-color: #f672d8;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-fuchsia .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f564d4;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple:focus,\n.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple:focus {\n  border-color: #feeaf9;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f672d8;\n  border-color: #f55ad2;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-fuchsia .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #feeaf9;\n}\n\n.dark-mode .select2-maroon + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #fbdee8;\n}\n\n.dark-mode .select2-maroon + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #fbdee8;\n}\n\n.select2-container--default .dark-mode .select2-maroon.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-maroon .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-maroon .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-maroon .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-maroon .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-maroon .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #fbdee8;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-results__option--highlighted,\n.dark-mode .select2-maroon .select2-container--default .select2-results__option--highlighted {\n  background-color: #ed6c9b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-maroon .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #eb5f92;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple:focus,\n.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple:focus {\n  border-color: #fbdee8;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #ed6c9b;\n  border-color: #ea568c;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-maroon.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-maroon .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #fbdee8;\n}\n\n.dark-mode .select2-blue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #85a7ca;\n}\n\n.dark-mode .select2-blue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-blue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-blue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-blue .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-blue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-blue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-blue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-results__option--highlighted,\n.dark-mode .select2-blue .select2-container--default .select2-results__option--highlighted {\n  background-color: #3f6791;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-blue .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #3a5f86;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-selection--multiple:focus,\n.dark-mode .select2-blue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3f6791;\n  border-color: #375a7f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-blue.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-blue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #85a7ca;\n}\n\n.dark-mode .select2-indigo + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.dark-mode .select2-indigo + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.select2-container--default .dark-mode .select2-indigo.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-indigo .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-indigo .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-indigo .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-indigo .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-indigo .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b389f9;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-results__option--highlighted,\n.dark-mode .select2-indigo .select2-container--default .select2-results__option--highlighted {\n  background-color: #6610f2;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-indigo .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #5f0de6;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple:focus,\n.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b389f9;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6610f2;\n  border-color: #5b0cdd;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-indigo.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-indigo .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b389f9;\n}\n\n.dark-mode .select2-purple + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.dark-mode .select2-purple + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .dark-mode .select2-purple.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-purple .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-purple .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-purple .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-purple .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-purple .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b8a2e0;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-results__option--highlighted,\n.dark-mode .select2-purple .select2-container--default .select2-results__option--highlighted {\n  background-color: #6f42c1;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-purple .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #683cb8;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-selection--multiple:focus,\n.dark-mode .select2-purple .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6f42c1;\n  border-color: #643ab0;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-purple.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-purple .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b8a2e0;\n}\n\n.dark-mode .select2-pink + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.dark-mode .select2-pink + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .dark-mode .select2-pink.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-pink .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-pink .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-pink .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-pink .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-pink .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f6b0d0;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-results__option--highlighted,\n.dark-mode .select2-pink .select2-container--default .select2-results__option--highlighted {\n  background-color: #e83e8c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-pink .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e63084;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-selection--multiple:focus,\n.dark-mode .select2-pink .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #e83e8c;\n  border-color: #e5277e;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-pink.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-pink .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f6b0d0;\n}\n\n.dark-mode .select2-red + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f5b4ae;\n}\n\n.dark-mode .select2-red + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-red.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-red .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-red .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-red .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-red .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-red .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-results__option--highlighted,\n.dark-mode .select2-red .select2-container--default .select2-results__option--highlighted {\n  background-color: #e74c3c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-red .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-red .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-red .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e53f2e;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-selection--multiple:focus,\n.dark-mode .select2-red .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #e74c3c;\n  border-color: #e43725;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-red .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-red.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-red .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f5b4ae;\n}\n\n.dark-mode .select2-orange + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #fec392;\n}\n\n.dark-mode .select2-orange + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #fec392;\n}\n\n.select2-container--default .dark-mode .select2-orange.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-orange .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-orange .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-orange .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-orange .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-orange .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #fec392;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-results__option--highlighted,\n.dark-mode .select2-orange .select2-container--default .select2-results__option--highlighted {\n  background-color: #fd7e14;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-orange .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #fd7605;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-selection--multiple:focus,\n.dark-mode .select2-orange .select2-container--default .select2-selection--multiple:focus {\n  border-color: #fec392;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fd7e14;\n  border-color: #f57102;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-orange.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-orange .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #fec392;\n}\n\n.dark-mode .select2-yellow + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f9cf8b;\n}\n\n.dark-mode .select2-yellow + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-yellow.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-yellow .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-yellow .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-yellow .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-yellow .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-yellow .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-results__option--highlighted,\n.dark-mode .select2-yellow .select2-container--default .select2-results__option--highlighted {\n  background-color: #f39c12;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-yellow .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #ea940c;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple:focus,\n.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f39c12;\n  border-color: #e08e0b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-yellow.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-yellow .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f9cf8b;\n}\n\n.dark-mode .select2-green + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #3dffcd;\n}\n\n.dark-mode .select2-green + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-green.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-green .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-green .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-green .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-green .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-green .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-results__option--highlighted,\n.dark-mode .select2-green .select2-container--default .select2-results__option--highlighted {\n  background-color: #00bc8c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-green .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-green .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-green .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #00ad81;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-selection--multiple:focus,\n.dark-mode .select2-green .select2-container--default .select2-selection--multiple:focus {\n  border-color: #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #00bc8c;\n  border-color: #00a379;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-green .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-green.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-green .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #3dffcd;\n}\n\n.dark-mode .select2-teal + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.dark-mode .select2-teal + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .dark-mode .select2-teal.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-teal .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-teal .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-teal .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-teal .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-teal .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #7eeaca;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-results__option--highlighted,\n.dark-mode .select2-teal .select2-container--default .select2-results__option--highlighted {\n  background-color: #20c997;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-teal .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1ebc8d;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-selection--multiple:focus,\n.dark-mode .select2-teal .select2-container--default .select2-selection--multiple:focus {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #20c997;\n  border-color: #1cb386;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-teal.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-teal .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #7eeaca;\n}\n\n.dark-mode .select2-cyan + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #a0cfee;\n}\n\n.dark-mode .select2-cyan + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-cyan.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-cyan .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-cyan .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-cyan .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-cyan .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-cyan .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-results__option--highlighted,\n.dark-mode .select2-cyan .select2-container--default .select2-results__option--highlighted {\n  background-color: #3498db;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-cyan .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2791d9;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple:focus,\n.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple:focus {\n  border-color: #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3498db;\n  border-color: #258cd1;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-cyan.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-cyan .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #a0cfee;\n}\n\n.dark-mode .select2-white + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.dark-mode .select2-white + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .dark-mode .select2-white.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-white .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-white .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-white .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-white .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-white .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-results__option--highlighted,\n.dark-mode .select2-white .select2-container--default .select2-results__option--highlighted {\n  background-color: #fff;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-white .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-white .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-white .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7f7f7;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-selection--multiple:focus,\n.dark-mode .select2-white .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fff;\n  border-color: #f2f2f2;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-white .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-white.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-white .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.dark-mode .select2-gray + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.dark-mode .select2-gray + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-gray.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-gray .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-gray .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-gray .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-gray .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-gray .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-results__option--highlighted,\n.dark-mode .select2-gray .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-gray .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-selection--multiple:focus,\n.dark-mode .select2-gray .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-gray .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.dark-mode .select2-gray-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.dark-mode .select2-gray-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-gray-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-gray-dark .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-gray-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-gray-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-gray-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-results__option--highlighted,\n.dark-mode .select2-gray-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-gray-dark .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple:focus,\n.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-gray-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.slider .tooltip.in {\n  opacity: 0.9;\n}\n\n.slider.slider-vertical {\n  height: 100%;\n}\n\n.slider.slider-horizontal {\n  width: 100%;\n}\n\n.slider-primary .slider .slider-selection {\n  background: #007bff;\n}\n\n.slider-secondary .slider .slider-selection {\n  background: #6c757d;\n}\n\n.slider-success .slider .slider-selection {\n  background: #28a745;\n}\n\n.slider-info .slider .slider-selection {\n  background: #17a2b8;\n}\n\n.slider-warning .slider .slider-selection {\n  background: #ffc107;\n}\n\n.slider-danger .slider .slider-selection {\n  background: #dc3545;\n}\n\n.slider-light .slider .slider-selection {\n  background: #f8f9fa;\n}\n\n.slider-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.slider-lightblue .slider .slider-selection {\n  background: #3c8dbc;\n}\n\n.slider-navy .slider .slider-selection {\n  background: #001f3f;\n}\n\n.slider-olive .slider .slider-selection {\n  background: #3d9970;\n}\n\n.slider-lime .slider .slider-selection {\n  background: #01ff70;\n}\n\n.slider-fuchsia .slider .slider-selection {\n  background: #f012be;\n}\n\n.slider-maroon .slider .slider-selection {\n  background: #d81b60;\n}\n\n.slider-blue .slider .slider-selection {\n  background: #007bff;\n}\n\n.slider-indigo .slider .slider-selection {\n  background: #6610f2;\n}\n\n.slider-purple .slider .slider-selection {\n  background: #6f42c1;\n}\n\n.slider-pink .slider .slider-selection {\n  background: #e83e8c;\n}\n\n.slider-red .slider .slider-selection {\n  background: #dc3545;\n}\n\n.slider-orange .slider .slider-selection {\n  background: #fd7e14;\n}\n\n.slider-yellow .slider .slider-selection {\n  background: #ffc107;\n}\n\n.slider-green .slider .slider-selection {\n  background: #28a745;\n}\n\n.slider-teal .slider .slider-selection {\n  background: #20c997;\n}\n\n.slider-cyan .slider .slider-selection {\n  background: #17a2b8;\n}\n\n.slider-white .slider .slider-selection {\n  background: #fff;\n}\n\n.slider-gray .slider .slider-selection {\n  background: #6c757d;\n}\n\n.slider-gray-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.dark-mode .slider-track {\n  background-color: #4b545c;\n  background-image: none;\n}\n\n.dark-mode .slider-primary .slider .slider-selection {\n  background: #3f6791;\n}\n\n.dark-mode .slider-secondary .slider .slider-selection {\n  background: #6c757d;\n}\n\n.dark-mode .slider-success .slider .slider-selection {\n  background: #00bc8c;\n}\n\n.dark-mode .slider-info .slider .slider-selection {\n  background: #3498db;\n}\n\n.dark-mode .slider-warning .slider .slider-selection {\n  background: #f39c12;\n}\n\n.dark-mode .slider-danger .slider .slider-selection {\n  background: #e74c3c;\n}\n\n.dark-mode .slider-light .slider .slider-selection {\n  background: #f8f9fa;\n}\n\n.dark-mode .slider-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.dark-mode .slider-lightblue .slider .slider-selection {\n  background: #86bad8;\n}\n\n.dark-mode .slider-navy .slider .slider-selection {\n  background: #002c59;\n}\n\n.dark-mode .slider-olive .slider .slider-selection {\n  background: #74c8a3;\n}\n\n.dark-mode .slider-lime .slider .slider-selection {\n  background: #67ffa9;\n}\n\n.dark-mode .slider-fuchsia .slider .slider-selection {\n  background: #f672d8;\n}\n\n.dark-mode .slider-maroon .slider .slider-selection {\n  background: #ed6c9b;\n}\n\n.dark-mode .slider-blue .slider .slider-selection {\n  background: #3f6791;\n}\n\n.dark-mode .slider-indigo .slider .slider-selection {\n  background: #6610f2;\n}\n\n.dark-mode .slider-purple .slider .slider-selection {\n  background: #6f42c1;\n}\n\n.dark-mode .slider-pink .slider .slider-selection {\n  background: #e83e8c;\n}\n\n.dark-mode .slider-red .slider .slider-selection {\n  background: #e74c3c;\n}\n\n.dark-mode .slider-orange .slider .slider-selection {\n  background: #fd7e14;\n}\n\n.dark-mode .slider-yellow .slider .slider-selection {\n  background: #f39c12;\n}\n\n.dark-mode .slider-green .slider .slider-selection {\n  background: #00bc8c;\n}\n\n.dark-mode .slider-teal .slider .slider-selection {\n  background: #20c997;\n}\n\n.dark-mode .slider-cyan .slider .slider-selection {\n  background: #3498db;\n}\n\n.dark-mode .slider-white .slider .slider-selection {\n  background: #fff;\n}\n\n.dark-mode .slider-gray .slider .slider-selection {\n  background: #6c757d;\n}\n\n.dark-mode .slider-gray-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.icheck-primary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-primary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #007bff;\n}\n\n.icheck-primary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-primary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #007bff;\n}\n\n.icheck-primary > input:first-child:checked + label::before,\n.icheck-primary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-secondary > input:first-child:checked + label::before,\n.icheck-secondary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.icheck-success > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-success > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-success > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-success > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-success > input:first-child:checked + label::before,\n.icheck-success > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.icheck-info > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-info > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-info > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-info > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-info > input:first-child:checked + label::before,\n.icheck-info > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.icheck-warning > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-warning > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-warning > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-warning > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-warning > input:first-child:checked + label::before,\n.icheck-warning > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.icheck-danger > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-danger > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-danger > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-danger > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-danger > input:first-child:checked + label::before,\n.icheck-danger > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.icheck-light > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-light > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.icheck-light > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-light > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.icheck-light > input:first-child:checked + label::before,\n.icheck-light > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n\n.icheck-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-dark > input:first-child:checked + label::before,\n.icheck-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3c8dbc;\n}\n\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3c8dbc;\n}\n\n.icheck-lightblue > input:first-child:checked + label::before,\n.icheck-lightblue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3c8dbc;\n  border-color: #3c8dbc;\n}\n\n.icheck-navy > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-navy > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #001f3f;\n}\n\n.icheck-navy > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-navy > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #001f3f;\n}\n\n.icheck-navy > input:first-child:checked + label::before,\n.icheck-navy > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #001f3f;\n  border-color: #001f3f;\n}\n\n.icheck-olive > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-olive > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3d9970;\n}\n\n.icheck-olive > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-olive > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3d9970;\n}\n\n.icheck-olive > input:first-child:checked + label::before,\n.icheck-olive > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3d9970;\n  border-color: #3d9970;\n}\n\n.icheck-lime > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-lime > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #01ff70;\n}\n\n.icheck-lime > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-lime > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #01ff70;\n}\n\n.icheck-lime > input:first-child:checked + label::before,\n.icheck-lime > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #01ff70;\n  border-color: #01ff70;\n}\n\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f012be;\n}\n\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f012be;\n}\n\n.icheck-fuchsia > input:first-child:checked + label::before,\n.icheck-fuchsia > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f012be;\n  border-color: #f012be;\n}\n\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #d81b60;\n}\n\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #d81b60;\n}\n\n.icheck-maroon > input:first-child:checked + label::before,\n.icheck-maroon > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #d81b60;\n  border-color: #d81b60;\n}\n\n.icheck-blue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-blue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #007bff;\n}\n\n.icheck-blue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-blue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #007bff;\n}\n\n.icheck-blue > input:first-child:checked + label::before,\n.icheck-blue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.icheck-indigo > input:first-child:checked + label::before,\n.icheck-indigo > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6610f2;\n  border-color: #6610f2;\n}\n\n.icheck-purple > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-purple > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.icheck-purple > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-purple > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.icheck-purple > input:first-child:checked + label::before,\n.icheck-purple > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6f42c1;\n  border-color: #6f42c1;\n}\n\n.icheck-pink > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-pink > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.icheck-pink > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-pink > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.icheck-pink > input:first-child:checked + label::before,\n.icheck-pink > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n}\n\n.icheck-red > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-red > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-red > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-red > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-red > input:first-child:checked + label::before,\n.icheck-red > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.icheck-orange > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-orange > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.icheck-orange > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-orange > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.icheck-orange > input:first-child:checked + label::before,\n.icheck-orange > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fd7e14;\n  border-color: #fd7e14;\n}\n\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-yellow > input:first-child:checked + label::before,\n.icheck-yellow > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.icheck-green > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-green > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-green > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-green > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-green > input:first-child:checked + label::before,\n.icheck-green > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.icheck-teal > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-teal > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.icheck-teal > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-teal > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.icheck-teal > input:first-child:checked + label::before,\n.icheck-teal > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #20c997;\n  border-color: #20c997;\n}\n\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-cyan > input:first-child:checked + label::before,\n.icheck-cyan > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.icheck-white > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-white > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.icheck-white > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-white > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.icheck-white > input:first-child:checked + label::before,\n.icheck-white > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fff;\n  border-color: #fff;\n}\n\n.icheck-gray > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-gray > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-gray > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-gray > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-gray > input:first-child:checked + label::before,\n.icheck-gray > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-gray-dark > input:first-child:checked + label::before,\n.icheck-gray-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.dark-mode [class*=\"icheck-\"] > input:first-child:not(:checked) + input[type=\"hidden\"] + label::before,\n.dark-mode [class*=\"icheck-\"] > input:first-child:not(:checked) + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-primary > input:first-child:checked + label::before,\n.dark-mode .icheck-primary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3f6791;\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-secondary > input:first-child:checked + label::before,\n.dark-mode .icheck-secondary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-success > input:first-child:checked + label::before,\n.dark-mode .icheck-success > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #00bc8c;\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-info > input:first-child:checked + label::before,\n.dark-mode .icheck-info > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3498db;\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-warning > input:first-child:checked + label::before,\n.dark-mode .icheck-warning > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f39c12;\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-danger > input:first-child:checked + label::before,\n.dark-mode .icheck-danger > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #e74c3c;\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.dark-mode .icheck-light > input:first-child:checked + label::before,\n.dark-mode .icheck-light > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n\n.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-dark > input:first-child:checked + label::before,\n.dark-mode .icheck-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #86bad8;\n}\n\n.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #86bad8;\n}\n\n.dark-mode .icheck-lightblue > input:first-child:checked + label::before,\n.dark-mode .icheck-lightblue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #86bad8;\n  border-color: #86bad8;\n}\n\n.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #002c59;\n}\n\n.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #002c59;\n}\n\n.dark-mode .icheck-navy > input:first-child:checked + label::before,\n.dark-mode .icheck-navy > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #002c59;\n  border-color: #002c59;\n}\n\n.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #74c8a3;\n}\n\n.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #74c8a3;\n}\n\n.dark-mode .icheck-olive > input:first-child:checked + label::before,\n.dark-mode .icheck-olive > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #74c8a3;\n  border-color: #74c8a3;\n}\n\n.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #67ffa9;\n}\n\n.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #67ffa9;\n}\n\n.dark-mode .icheck-lime > input:first-child:checked + label::before,\n.dark-mode .icheck-lime > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #67ffa9;\n  border-color: #67ffa9;\n}\n\n.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f672d8;\n}\n\n.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f672d8;\n}\n\n.dark-mode .icheck-fuchsia > input:first-child:checked + label::before,\n.dark-mode .icheck-fuchsia > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f672d8;\n  border-color: #f672d8;\n}\n\n.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #ed6c9b;\n}\n\n.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #ed6c9b;\n}\n\n.dark-mode .icheck-maroon > input:first-child:checked + label::before,\n.dark-mode .icheck-maroon > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #ed6c9b;\n  border-color: #ed6c9b;\n}\n\n.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-blue > input:first-child:checked + label::before,\n.dark-mode .icheck-blue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3f6791;\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.dark-mode .icheck-indigo > input:first-child:checked + label::before,\n.dark-mode .icheck-indigo > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6610f2;\n  border-color: #6610f2;\n}\n\n.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.dark-mode .icheck-purple > input:first-child:checked + label::before,\n.dark-mode .icheck-purple > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6f42c1;\n  border-color: #6f42c1;\n}\n\n.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.dark-mode .icheck-pink > input:first-child:checked + label::before,\n.dark-mode .icheck-pink > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n}\n\n.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-red > input:first-child:checked + label::before,\n.dark-mode .icheck-red > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #e74c3c;\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.dark-mode .icheck-orange > input:first-child:checked + label::before,\n.dark-mode .icheck-orange > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fd7e14;\n  border-color: #fd7e14;\n}\n\n.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-yellow > input:first-child:checked + label::before,\n.dark-mode .icheck-yellow > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f39c12;\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-green > input:first-child:checked + label::before,\n.dark-mode .icheck-green > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #00bc8c;\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.dark-mode .icheck-teal > input:first-child:checked + label::before,\n.dark-mode .icheck-teal > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #20c997;\n  border-color: #20c997;\n}\n\n.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-cyan > input:first-child:checked + label::before,\n.dark-mode .icheck-cyan > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3498db;\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.dark-mode .icheck-white > input:first-child:checked + label::before,\n.dark-mode .icheck-white > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fff;\n  border-color: #fff;\n}\n\n.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-gray > input:first-child:checked + label::before,\n.dark-mode .icheck-gray > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-gray-dark > input:first-child:checked + label::before,\n.dark-mode .icheck-gray-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.mapael .map {\n  position: relative;\n}\n\n.mapael .mapTooltip {\n  font-family: \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\";\n  font-style: normal;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  border-radius: 0.25rem;\n  font-size: 0.875rem;\n  background-color: #000;\n  color: #fff;\n  display: block;\n  max-width: 200px;\n  padding: 0.25rem 0.5rem;\n  position: absolute;\n  text-align: center;\n  word-wrap: break-word;\n  z-index: 1070;\n}\n\n.mapael .myLegend {\n  background-color: #f8f9fa;\n  border: 1px solid #adb5bd;\n  padding: 10px;\n  width: 600px;\n}\n\n.mapael .zoomButton {\n  background-color: #f8f9fa;\n  border: 1px solid #ddd;\n  border-radius: 0.25rem;\n  color: #444;\n  cursor: pointer;\n  font-weight: 700;\n  height: 16px;\n  left: 10px;\n  line-height: 14px;\n  padding-left: 1px;\n  position: absolute;\n  text-align: center;\n  top: 0;\n  user-select: none;\n  width: 16px;\n}\n\n.mapael .zoomButton:hover, .mapael .zoomButton:active, .mapael .zoomButton.hover {\n  background-color: #e9ecef;\n  color: #2b2b2b;\n}\n\n.mapael .zoomReset {\n  line-height: 12px;\n  top: 10px;\n}\n\n.mapael .zoomIn {\n  top: 30px;\n}\n\n.mapael .zoomOut {\n  top: 50px;\n}\n\n.jqvmap-zoomin,\n.jqvmap-zoomout {\n  background-color: #f8f9fa;\n  border: 1px solid #ddd;\n  border-radius: 0.25rem;\n  color: #444;\n  height: 15px;\n  width: 15px;\n  padding: 1px 2px;\n}\n\n.jqvmap-zoomin:hover, .jqvmap-zoomin:active, .jqvmap-zoomin.hover,\n.jqvmap-zoomout:hover,\n.jqvmap-zoomout:active,\n.jqvmap-zoomout.hover {\n  background-color: #e9ecef;\n  color: #2b2b2b;\n}\n\n.swal2-icon.swal2-info {\n  border-color: ligthen(#17a2b8, 20%);\n  color: #17a2b8;\n}\n\n.swal2-icon.swal2-warning {\n  border-color: ligthen(#ffc107, 20%);\n  color: #ffc107;\n}\n\n.swal2-icon.swal2-error {\n  border-color: ligthen(#dc3545, 20%);\n  color: #dc3545;\n}\n\n.swal2-icon.swal2-question {\n  border-color: ligthen(#6c757d, 20%);\n  color: #6c757d;\n}\n\n.swal2-icon.swal2-success {\n  border-color: ligthen(#28a745, 20%);\n  color: #28a745;\n}\n\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: ligthen(#28a745, 20%);\n}\n\n.swal2-icon.swal2-success [class^='swal2-success-line'] {\n  background-color: #28a745;\n}\n\n.dark-mode .swal2-popup {\n  background-color: #343a40;\n  color: #e9ecef;\n}\n\n.dark-mode .swal2-popup .swal2-content,\n.dark-mode .swal2-popup .swal2-title {\n  color: #e9ecef;\n}\n\n#toast-container .toast {\n  background-color: #007bff;\n}\n\n#toast-container .toast-success {\n  background-color: #28a745;\n}\n\n#toast-container .toast-error {\n  background-color: #dc3545;\n}\n\n#toast-container .toast-info {\n  background-color: #17a2b8;\n}\n\n#toast-container .toast-warning {\n  background-color: #ffc107;\n}\n\n.toast-bottom-full-width .toast,\n.toast-top-full-width .toast {\n  max-width: inherit;\n}\n\n.pace {\n  z-index: 1048;\n}\n\n.pace .pace-progress {\n  z-index: 1049;\n}\n\n.pace .pace-activity {\n  z-index: 1050;\n}\n\n.pace-primary .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-barber-shop-primary .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-primary .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-barber-shop-primary .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-primary .pace .pace-progress::after {\n  color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-bounce-primary .pace .pace-activity {\n  background: #007bff;\n}\n\n.pace-center-atom-primary .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-primary .pace-progress::before {\n  background: #007bff;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-primary .pace-activity {\n  border-color: #007bff;\n}\n\n.pace-center-atom-primary .pace-activity::after, .pace-center-atom-primary .pace-activity::before {\n  border-color: #007bff;\n}\n\n.pace-center-circle-primary .pace .pace-progress {\n  background: rgba(0, 123, 255, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-primary .pace .pace-activity {\n  border-color: #007bff transparent transparent;\n}\n\n.pace-center-radar-primary .pace .pace-activity::before {\n  border-color: #007bff transparent transparent;\n}\n\n.pace-center-simple-primary .pace {\n  background: #fff;\n  border-color: #007bff;\n}\n\n.pace-center-simple-primary .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-material-primary .pace {\n  color: #007bff;\n}\n\n.pace-corner-indicator-primary .pace .pace-activity {\n  background: #007bff;\n}\n\n.pace-corner-indicator-primary .pace .pace-activity::after,\n.pace-corner-indicator-primary .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-primary .pace .pace-activity::before {\n  border-right-color: rgba(0, 123, 255, 0.2);\n  border-left-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-corner-indicator-primary .pace .pace-activity::after {\n  border-top-color: rgba(0, 123, 255, 0.2);\n  border-bottom-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-fill-left-primary .pace .pace-progress {\n  background-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-flash-primary .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-flash-primary .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #007bff, 0 0 5px #007bff;\n}\n\n.pace-flash-primary .pace .pace-activity {\n  border-top-color: #007bff;\n  border-left-color: #007bff;\n}\n\n.pace-loading-bar-primary .pace .pace-progress {\n  background: #007bff;\n  color: #007bff;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-primary .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #007bff, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-primary .pace .pace-progress {\n  background-color: #007bff;\n  box-shadow: inset -1px 0 #007bff, inset 0 -1px #007bff, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-primary .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-primary .pace-progress {\n  color: #007bff;\n}\n\n.pace-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-secondary .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-secondary .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-secondary .pace .pace-progress::after {\n  color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-bounce-secondary .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-center-atom-secondary .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-secondary .pace-progress::before {\n  background: #6c757d;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-secondary .pace-activity {\n  border-color: #6c757d;\n}\n\n.pace-center-atom-secondary .pace-activity::after, .pace-center-atom-secondary .pace-activity::before {\n  border-color: #6c757d;\n}\n\n.pace-center-circle-secondary .pace .pace-progress {\n  background: rgba(108, 117, 125, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-secondary .pace .pace-activity {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-radar-secondary .pace .pace-activity::before {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-simple-secondary .pace {\n  background: #fff;\n  border-color: #6c757d;\n}\n\n.pace-center-simple-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-material-secondary .pace {\n  color: #6c757d;\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity::after,\n.pace-corner-indicator-secondary .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity::before {\n  border-right-color: rgba(108, 117, 125, 0.2);\n  border-left-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity::after {\n  border-top-color: rgba(108, 117, 125, 0.2);\n  border-bottom-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-fill-left-secondary .pace .pace-progress {\n  background-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-flash-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-flash-secondary .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6c757d, 0 0 5px #6c757d;\n}\n\n.pace-flash-secondary .pace .pace-activity {\n  border-top-color: #6c757d;\n  border-left-color: #6c757d;\n}\n\n.pace-loading-bar-secondary .pace .pace-progress {\n  background: #6c757d;\n  color: #6c757d;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-secondary .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6c757d, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-secondary .pace .pace-progress {\n  background-color: #6c757d;\n  box-shadow: inset -1px 0 #6c757d, inset 0 -1px #6c757d, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-secondary .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-secondary .pace-progress {\n  color: #6c757d;\n}\n\n.pace-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-success .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-success .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-success .pace .pace-progress::after {\n  color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-bounce-success .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-center-atom-success .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-success .pace-progress::before {\n  background: #28a745;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-success .pace-activity {\n  border-color: #28a745;\n}\n\n.pace-center-atom-success .pace-activity::after, .pace-center-atom-success .pace-activity::before {\n  border-color: #28a745;\n}\n\n.pace-center-circle-success .pace .pace-progress {\n  background: rgba(40, 167, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-success .pace .pace-activity {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-radar-success .pace .pace-activity::before {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-simple-success .pace {\n  background: #fff;\n  border-color: #28a745;\n}\n\n.pace-center-simple-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-material-success .pace {\n  color: #28a745;\n}\n\n.pace-corner-indicator-success .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-corner-indicator-success .pace .pace-activity::after,\n.pace-corner-indicator-success .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-success .pace .pace-activity::before {\n  border-right-color: rgba(40, 167, 69, 0.2);\n  border-left-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-corner-indicator-success .pace .pace-activity::after {\n  border-top-color: rgba(40, 167, 69, 0.2);\n  border-bottom-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-fill-left-success .pace .pace-progress {\n  background-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-flash-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-flash-success .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #28a745, 0 0 5px #28a745;\n}\n\n.pace-flash-success .pace .pace-activity {\n  border-top-color: #28a745;\n  border-left-color: #28a745;\n}\n\n.pace-loading-bar-success .pace .pace-progress {\n  background: #28a745;\n  color: #28a745;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-success .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #28a745, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-success .pace .pace-progress {\n  background-color: #28a745;\n  box-shadow: inset -1px 0 #28a745, inset 0 -1px #28a745, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-success .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-success .pace-progress {\n  color: #28a745;\n}\n\n.pace-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-info .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-info .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-info .pace .pace-progress::after {\n  color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-bounce-info .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-center-atom-info .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-info .pace-progress::before {\n  background: #17a2b8;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-info .pace-activity {\n  border-color: #17a2b8;\n}\n\n.pace-center-atom-info .pace-activity::after, .pace-center-atom-info .pace-activity::before {\n  border-color: #17a2b8;\n}\n\n.pace-center-circle-info .pace .pace-progress {\n  background: rgba(23, 162, 184, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-info .pace .pace-activity {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-radar-info .pace .pace-activity::before {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-simple-info .pace {\n  background: #fff;\n  border-color: #17a2b8;\n}\n\n.pace-center-simple-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-material-info .pace {\n  color: #17a2b8;\n}\n\n.pace-corner-indicator-info .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-corner-indicator-info .pace .pace-activity::after,\n.pace-corner-indicator-info .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-info .pace .pace-activity::before {\n  border-right-color: rgba(23, 162, 184, 0.2);\n  border-left-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-corner-indicator-info .pace .pace-activity::after {\n  border-top-color: rgba(23, 162, 184, 0.2);\n  border-bottom-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-fill-left-info .pace .pace-progress {\n  background-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-flash-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-flash-info .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #17a2b8, 0 0 5px #17a2b8;\n}\n\n.pace-flash-info .pace .pace-activity {\n  border-top-color: #17a2b8;\n  border-left-color: #17a2b8;\n}\n\n.pace-loading-bar-info .pace .pace-progress {\n  background: #17a2b8;\n  color: #17a2b8;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-info .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #17a2b8, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-info .pace .pace-progress {\n  background-color: #17a2b8;\n  box-shadow: inset -1px 0 #17a2b8, inset 0 -1px #17a2b8, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-info .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-info .pace-progress {\n  color: #17a2b8;\n}\n\n.pace-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-warning .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-warning .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-warning .pace .pace-progress::after {\n  color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-bounce-warning .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-center-atom-warning .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-warning .pace-progress::before {\n  background: #ffc107;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-warning .pace-activity {\n  border-color: #ffc107;\n}\n\n.pace-center-atom-warning .pace-activity::after, .pace-center-atom-warning .pace-activity::before {\n  border-color: #ffc107;\n}\n\n.pace-center-circle-warning .pace .pace-progress {\n  background: rgba(255, 193, 7, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-warning .pace .pace-activity {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-radar-warning .pace .pace-activity::before {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-simple-warning .pace {\n  background: #1f2d3d;\n  border-color: #ffc107;\n}\n\n.pace-center-simple-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-material-warning .pace {\n  color: #ffc107;\n}\n\n.pace-corner-indicator-warning .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-corner-indicator-warning .pace .pace-activity::after,\n.pace-corner-indicator-warning .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-warning .pace .pace-activity::before {\n  border-right-color: rgba(255, 193, 7, 0.2);\n  border-left-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-corner-indicator-warning .pace .pace-activity::after {\n  border-top-color: rgba(255, 193, 7, 0.2);\n  border-bottom-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-fill-left-warning .pace .pace-progress {\n  background-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-flash-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-flash-warning .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #ffc107, 0 0 5px #ffc107;\n}\n\n.pace-flash-warning .pace .pace-activity {\n  border-top-color: #ffc107;\n  border-left-color: #ffc107;\n}\n\n.pace-loading-bar-warning .pace .pace-progress {\n  background: #ffc107;\n  color: #ffc107;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-warning .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #ffc107, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-warning .pace .pace-progress {\n  background-color: #ffc107;\n  box-shadow: inset -1px 0 #ffc107, inset 0 -1px #ffc107, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-warning .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-warning .pace-progress {\n  color: #ffc107;\n}\n\n.pace-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-danger .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-danger .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-danger .pace .pace-progress::after {\n  color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-bounce-danger .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-center-atom-danger .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-danger .pace-progress::before {\n  background: #dc3545;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-danger .pace-activity {\n  border-color: #dc3545;\n}\n\n.pace-center-atom-danger .pace-activity::after, .pace-center-atom-danger .pace-activity::before {\n  border-color: #dc3545;\n}\n\n.pace-center-circle-danger .pace .pace-progress {\n  background: rgba(220, 53, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-danger .pace .pace-activity {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-radar-danger .pace .pace-activity::before {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-simple-danger .pace {\n  background: #fff;\n  border-color: #dc3545;\n}\n\n.pace-center-simple-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-material-danger .pace {\n  color: #dc3545;\n}\n\n.pace-corner-indicator-danger .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-corner-indicator-danger .pace .pace-activity::after,\n.pace-corner-indicator-danger .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-danger .pace .pace-activity::before {\n  border-right-color: rgba(220, 53, 69, 0.2);\n  border-left-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-corner-indicator-danger .pace .pace-activity::after {\n  border-top-color: rgba(220, 53, 69, 0.2);\n  border-bottom-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-fill-left-danger .pace .pace-progress {\n  background-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-flash-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-flash-danger .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #dc3545, 0 0 5px #dc3545;\n}\n\n.pace-flash-danger .pace .pace-activity {\n  border-top-color: #dc3545;\n  border-left-color: #dc3545;\n}\n\n.pace-loading-bar-danger .pace .pace-progress {\n  background: #dc3545;\n  color: #dc3545;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-danger .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #dc3545, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-danger .pace .pace-progress {\n  background-color: #dc3545;\n  box-shadow: inset -1px 0 #dc3545, inset 0 -1px #dc3545, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-danger .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-danger .pace-progress {\n  color: #dc3545;\n}\n\n.pace-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-barber-shop-light .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-barber-shop-light .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-light .pace .pace-progress::after {\n  color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-bounce-light .pace .pace-activity {\n  background: #f8f9fa;\n}\n\n.pace-center-atom-light .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-light .pace-progress::before {\n  background: #f8f9fa;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-light .pace-activity {\n  border-color: #f8f9fa;\n}\n\n.pace-center-atom-light .pace-activity::after, .pace-center-atom-light .pace-activity::before {\n  border-color: #f8f9fa;\n}\n\n.pace-center-circle-light .pace .pace-progress {\n  background: rgba(248, 249, 250, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-light .pace .pace-activity {\n  border-color: #f8f9fa transparent transparent;\n}\n\n.pace-center-radar-light .pace .pace-activity::before {\n  border-color: #f8f9fa transparent transparent;\n}\n\n.pace-center-simple-light .pace {\n  background: #1f2d3d;\n  border-color: #f8f9fa;\n}\n\n.pace-center-simple-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-material-light .pace {\n  color: #f8f9fa;\n}\n\n.pace-corner-indicator-light .pace .pace-activity {\n  background: #f8f9fa;\n}\n\n.pace-corner-indicator-light .pace .pace-activity::after,\n.pace-corner-indicator-light .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-light .pace .pace-activity::before {\n  border-right-color: rgba(248, 249, 250, 0.2);\n  border-left-color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-corner-indicator-light .pace .pace-activity::after {\n  border-top-color: rgba(248, 249, 250, 0.2);\n  border-bottom-color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-fill-left-light .pace .pace-progress {\n  background-color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-flash-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-flash-light .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #f8f9fa, 0 0 5px #f8f9fa;\n}\n\n.pace-flash-light .pace .pace-activity {\n  border-top-color: #f8f9fa;\n  border-left-color: #f8f9fa;\n}\n\n.pace-loading-bar-light .pace .pace-progress {\n  background: #f8f9fa;\n  color: #f8f9fa;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-light .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #f8f9fa, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-light .pace .pace-progress {\n  background-color: #f8f9fa;\n  box-shadow: inset -1px 0 #f8f9fa, inset 0 -1px #f8f9fa, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-light .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-light .pace-progress {\n  color: #f8f9fa;\n}\n\n.pace-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-dark .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-dark .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-dark .pace .pace-progress::after {\n  color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-bounce-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-center-atom-dark .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-dark .pace-progress::before {\n  background: #343a40;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-dark .pace-activity {\n  border-color: #343a40;\n}\n\n.pace-center-atom-dark .pace-activity::after, .pace-center-atom-dark .pace-activity::before {\n  border-color: #343a40;\n}\n\n.pace-center-circle-dark .pace .pace-progress {\n  background: rgba(52, 58, 64, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-dark .pace .pace-activity {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-radar-dark .pace .pace-activity::before {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-simple-dark .pace {\n  background: #fff;\n  border-color: #343a40;\n}\n\n.pace-center-simple-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-material-dark .pace {\n  color: #343a40;\n}\n\n.pace-corner-indicator-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-corner-indicator-dark .pace .pace-activity::after,\n.pace-corner-indicator-dark .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-dark .pace .pace-activity::before {\n  border-right-color: rgba(52, 58, 64, 0.2);\n  border-left-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-corner-indicator-dark .pace .pace-activity::after {\n  border-top-color: rgba(52, 58, 64, 0.2);\n  border-bottom-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-fill-left-dark .pace .pace-progress {\n  background-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-flash-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-flash-dark .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #343a40, 0 0 5px #343a40;\n}\n\n.pace-flash-dark .pace .pace-activity {\n  border-top-color: #343a40;\n  border-left-color: #343a40;\n}\n\n.pace-loading-bar-dark .pace .pace-progress {\n  background: #343a40;\n  color: #343a40;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-dark .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #343a40, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-dark .pace .pace-progress {\n  background-color: #343a40;\n  box-shadow: inset -1px 0 #343a40, inset 0 -1px #343a40, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-dark .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-dark .pace-progress {\n  color: #343a40;\n}\n\n.pace-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-barber-shop-lightblue .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-barber-shop-lightblue .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-lightblue .pace .pace-progress::after {\n  color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-bounce-lightblue .pace .pace-activity {\n  background: #3c8dbc;\n}\n\n.pace-center-atom-lightblue .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-lightblue .pace-progress::before {\n  background: #3c8dbc;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-lightblue .pace-activity {\n  border-color: #3c8dbc;\n}\n\n.pace-center-atom-lightblue .pace-activity::after, .pace-center-atom-lightblue .pace-activity::before {\n  border-color: #3c8dbc;\n}\n\n.pace-center-circle-lightblue .pace .pace-progress {\n  background: rgba(60, 141, 188, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-lightblue .pace .pace-activity {\n  border-color: #3c8dbc transparent transparent;\n}\n\n.pace-center-radar-lightblue .pace .pace-activity::before {\n  border-color: #3c8dbc transparent transparent;\n}\n\n.pace-center-simple-lightblue .pace {\n  background: #fff;\n  border-color: #3c8dbc;\n}\n\n.pace-center-simple-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-material-lightblue .pace {\n  color: #3c8dbc;\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity {\n  background: #3c8dbc;\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity::after,\n.pace-corner-indicator-lightblue .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity::before {\n  border-right-color: rgba(60, 141, 188, 0.2);\n  border-left-color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity::after {\n  border-top-color: rgba(60, 141, 188, 0.2);\n  border-bottom-color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-fill-left-lightblue .pace .pace-progress {\n  background-color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-flash-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-flash-lightblue .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #3c8dbc, 0 0 5px #3c8dbc;\n}\n\n.pace-flash-lightblue .pace .pace-activity {\n  border-top-color: #3c8dbc;\n  border-left-color: #3c8dbc;\n}\n\n.pace-loading-bar-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n  color: #3c8dbc;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-lightblue .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #3c8dbc, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-lightblue .pace .pace-progress {\n  background-color: #3c8dbc;\n  box-shadow: inset -1px 0 #3c8dbc, inset 0 -1px #3c8dbc, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-lightblue .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-lightblue .pace-progress {\n  color: #3c8dbc;\n}\n\n.pace-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-barber-shop-navy .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-barber-shop-navy .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-navy .pace .pace-progress::after {\n  color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-bounce-navy .pace .pace-activity {\n  background: #001f3f;\n}\n\n.pace-center-atom-navy .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-navy .pace-progress::before {\n  background: #001f3f;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-navy .pace-activity {\n  border-color: #001f3f;\n}\n\n.pace-center-atom-navy .pace-activity::after, .pace-center-atom-navy .pace-activity::before {\n  border-color: #001f3f;\n}\n\n.pace-center-circle-navy .pace .pace-progress {\n  background: rgba(0, 31, 63, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-navy .pace .pace-activity {\n  border-color: #001f3f transparent transparent;\n}\n\n.pace-center-radar-navy .pace .pace-activity::before {\n  border-color: #001f3f transparent transparent;\n}\n\n.pace-center-simple-navy .pace {\n  background: #fff;\n  border-color: #001f3f;\n}\n\n.pace-center-simple-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-material-navy .pace {\n  color: #001f3f;\n}\n\n.pace-corner-indicator-navy .pace .pace-activity {\n  background: #001f3f;\n}\n\n.pace-corner-indicator-navy .pace .pace-activity::after,\n.pace-corner-indicator-navy .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-navy .pace .pace-activity::before {\n  border-right-color: rgba(0, 31, 63, 0.2);\n  border-left-color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-corner-indicator-navy .pace .pace-activity::after {\n  border-top-color: rgba(0, 31, 63, 0.2);\n  border-bottom-color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-fill-left-navy .pace .pace-progress {\n  background-color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-flash-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-flash-navy .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #001f3f, 0 0 5px #001f3f;\n}\n\n.pace-flash-navy .pace .pace-activity {\n  border-top-color: #001f3f;\n  border-left-color: #001f3f;\n}\n\n.pace-loading-bar-navy .pace .pace-progress {\n  background: #001f3f;\n  color: #001f3f;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-navy .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #001f3f, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-navy .pace .pace-progress {\n  background-color: #001f3f;\n  box-shadow: inset -1px 0 #001f3f, inset 0 -1px #001f3f, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-navy .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-navy .pace-progress {\n  color: #001f3f;\n}\n\n.pace-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-barber-shop-olive .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-barber-shop-olive .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-olive .pace .pace-progress::after {\n  color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-bounce-olive .pace .pace-activity {\n  background: #3d9970;\n}\n\n.pace-center-atom-olive .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-olive .pace-progress::before {\n  background: #3d9970;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-olive .pace-activity {\n  border-color: #3d9970;\n}\n\n.pace-center-atom-olive .pace-activity::after, .pace-center-atom-olive .pace-activity::before {\n  border-color: #3d9970;\n}\n\n.pace-center-circle-olive .pace .pace-progress {\n  background: rgba(61, 153, 112, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-olive .pace .pace-activity {\n  border-color: #3d9970 transparent transparent;\n}\n\n.pace-center-radar-olive .pace .pace-activity::before {\n  border-color: #3d9970 transparent transparent;\n}\n\n.pace-center-simple-olive .pace {\n  background: #fff;\n  border-color: #3d9970;\n}\n\n.pace-center-simple-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-material-olive .pace {\n  color: #3d9970;\n}\n\n.pace-corner-indicator-olive .pace .pace-activity {\n  background: #3d9970;\n}\n\n.pace-corner-indicator-olive .pace .pace-activity::after,\n.pace-corner-indicator-olive .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-olive .pace .pace-activity::before {\n  border-right-color: rgba(61, 153, 112, 0.2);\n  border-left-color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-corner-indicator-olive .pace .pace-activity::after {\n  border-top-color: rgba(61, 153, 112, 0.2);\n  border-bottom-color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-fill-left-olive .pace .pace-progress {\n  background-color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-flash-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-flash-olive .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #3d9970, 0 0 5px #3d9970;\n}\n\n.pace-flash-olive .pace .pace-activity {\n  border-top-color: #3d9970;\n  border-left-color: #3d9970;\n}\n\n.pace-loading-bar-olive .pace .pace-progress {\n  background: #3d9970;\n  color: #3d9970;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-olive .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #3d9970, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-olive .pace .pace-progress {\n  background-color: #3d9970;\n  box-shadow: inset -1px 0 #3d9970, inset 0 -1px #3d9970, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-olive .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-olive .pace-progress {\n  color: #3d9970;\n}\n\n.pace-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-barber-shop-lime .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-barber-shop-lime .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-lime .pace .pace-progress::after {\n  color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-bounce-lime .pace .pace-activity {\n  background: #01ff70;\n}\n\n.pace-center-atom-lime .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-lime .pace-progress::before {\n  background: #01ff70;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-lime .pace-activity {\n  border-color: #01ff70;\n}\n\n.pace-center-atom-lime .pace-activity::after, .pace-center-atom-lime .pace-activity::before {\n  border-color: #01ff70;\n}\n\n.pace-center-circle-lime .pace .pace-progress {\n  background: rgba(1, 255, 112, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-lime .pace .pace-activity {\n  border-color: #01ff70 transparent transparent;\n}\n\n.pace-center-radar-lime .pace .pace-activity::before {\n  border-color: #01ff70 transparent transparent;\n}\n\n.pace-center-simple-lime .pace {\n  background: #1f2d3d;\n  border-color: #01ff70;\n}\n\n.pace-center-simple-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-material-lime .pace {\n  color: #01ff70;\n}\n\n.pace-corner-indicator-lime .pace .pace-activity {\n  background: #01ff70;\n}\n\n.pace-corner-indicator-lime .pace .pace-activity::after,\n.pace-corner-indicator-lime .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-lime .pace .pace-activity::before {\n  border-right-color: rgba(1, 255, 112, 0.2);\n  border-left-color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-corner-indicator-lime .pace .pace-activity::after {\n  border-top-color: rgba(1, 255, 112, 0.2);\n  border-bottom-color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-fill-left-lime .pace .pace-progress {\n  background-color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-flash-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-flash-lime .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #01ff70, 0 0 5px #01ff70;\n}\n\n.pace-flash-lime .pace .pace-activity {\n  border-top-color: #01ff70;\n  border-left-color: #01ff70;\n}\n\n.pace-loading-bar-lime .pace .pace-progress {\n  background: #01ff70;\n  color: #01ff70;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-lime .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #01ff70, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-lime .pace .pace-progress {\n  background-color: #01ff70;\n  box-shadow: inset -1px 0 #01ff70, inset 0 -1px #01ff70, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-lime .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-lime .pace-progress {\n  color: #01ff70;\n}\n\n.pace-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-barber-shop-fuchsia .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-barber-shop-fuchsia .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-fuchsia .pace .pace-progress::after {\n  color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-bounce-fuchsia .pace .pace-activity {\n  background: #f012be;\n}\n\n.pace-center-atom-fuchsia .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-fuchsia .pace-progress::before {\n  background: #f012be;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-fuchsia .pace-activity {\n  border-color: #f012be;\n}\n\n.pace-center-atom-fuchsia .pace-activity::after, .pace-center-atom-fuchsia .pace-activity::before {\n  border-color: #f012be;\n}\n\n.pace-center-circle-fuchsia .pace .pace-progress {\n  background: rgba(240, 18, 190, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-fuchsia .pace .pace-activity {\n  border-color: #f012be transparent transparent;\n}\n\n.pace-center-radar-fuchsia .pace .pace-activity::before {\n  border-color: #f012be transparent transparent;\n}\n\n.pace-center-simple-fuchsia .pace {\n  background: #fff;\n  border-color: #f012be;\n}\n\n.pace-center-simple-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-material-fuchsia .pace {\n  color: #f012be;\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity {\n  background: #f012be;\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity::after,\n.pace-corner-indicator-fuchsia .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity::before {\n  border-right-color: rgba(240, 18, 190, 0.2);\n  border-left-color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity::after {\n  border-top-color: rgba(240, 18, 190, 0.2);\n  border-bottom-color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-fill-left-fuchsia .pace .pace-progress {\n  background-color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-flash-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-flash-fuchsia .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #f012be, 0 0 5px #f012be;\n}\n\n.pace-flash-fuchsia .pace .pace-activity {\n  border-top-color: #f012be;\n  border-left-color: #f012be;\n}\n\n.pace-loading-bar-fuchsia .pace .pace-progress {\n  background: #f012be;\n  color: #f012be;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-fuchsia .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #f012be, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-fuchsia .pace .pace-progress {\n  background-color: #f012be;\n  box-shadow: inset -1px 0 #f012be, inset 0 -1px #f012be, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-fuchsia .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-fuchsia .pace-progress {\n  color: #f012be;\n}\n\n.pace-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-barber-shop-maroon .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-barber-shop-maroon .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-maroon .pace .pace-progress::after {\n  color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-bounce-maroon .pace .pace-activity {\n  background: #d81b60;\n}\n\n.pace-center-atom-maroon .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-maroon .pace-progress::before {\n  background: #d81b60;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-maroon .pace-activity {\n  border-color: #d81b60;\n}\n\n.pace-center-atom-maroon .pace-activity::after, .pace-center-atom-maroon .pace-activity::before {\n  border-color: #d81b60;\n}\n\n.pace-center-circle-maroon .pace .pace-progress {\n  background: rgba(216, 27, 96, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-maroon .pace .pace-activity {\n  border-color: #d81b60 transparent transparent;\n}\n\n.pace-center-radar-maroon .pace .pace-activity::before {\n  border-color: #d81b60 transparent transparent;\n}\n\n.pace-center-simple-maroon .pace {\n  background: #fff;\n  border-color: #d81b60;\n}\n\n.pace-center-simple-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-material-maroon .pace {\n  color: #d81b60;\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity {\n  background: #d81b60;\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity::after,\n.pace-corner-indicator-maroon .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity::before {\n  border-right-color: rgba(216, 27, 96, 0.2);\n  border-left-color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity::after {\n  border-top-color: rgba(216, 27, 96, 0.2);\n  border-bottom-color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-fill-left-maroon .pace .pace-progress {\n  background-color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-flash-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-flash-maroon .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #d81b60, 0 0 5px #d81b60;\n}\n\n.pace-flash-maroon .pace .pace-activity {\n  border-top-color: #d81b60;\n  border-left-color: #d81b60;\n}\n\n.pace-loading-bar-maroon .pace .pace-progress {\n  background: #d81b60;\n  color: #d81b60;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-maroon .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #d81b60, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-maroon .pace .pace-progress {\n  background-color: #d81b60;\n  box-shadow: inset -1px 0 #d81b60, inset 0 -1px #d81b60, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-maroon .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-maroon .pace-progress {\n  color: #d81b60;\n}\n\n.pace-blue .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-barber-shop-blue .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-blue .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-barber-shop-blue .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-blue .pace .pace-progress::after {\n  color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-bounce-blue .pace .pace-activity {\n  background: #007bff;\n}\n\n.pace-center-atom-blue .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-blue .pace-progress::before {\n  background: #007bff;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-blue .pace-activity {\n  border-color: #007bff;\n}\n\n.pace-center-atom-blue .pace-activity::after, .pace-center-atom-blue .pace-activity::before {\n  border-color: #007bff;\n}\n\n.pace-center-circle-blue .pace .pace-progress {\n  background: rgba(0, 123, 255, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-blue .pace .pace-activity {\n  border-color: #007bff transparent transparent;\n}\n\n.pace-center-radar-blue .pace .pace-activity::before {\n  border-color: #007bff transparent transparent;\n}\n\n.pace-center-simple-blue .pace {\n  background: #fff;\n  border-color: #007bff;\n}\n\n.pace-center-simple-blue .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-material-blue .pace {\n  color: #007bff;\n}\n\n.pace-corner-indicator-blue .pace .pace-activity {\n  background: #007bff;\n}\n\n.pace-corner-indicator-blue .pace .pace-activity::after,\n.pace-corner-indicator-blue .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-blue .pace .pace-activity::before {\n  border-right-color: rgba(0, 123, 255, 0.2);\n  border-left-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-corner-indicator-blue .pace .pace-activity::after {\n  border-top-color: rgba(0, 123, 255, 0.2);\n  border-bottom-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-fill-left-blue .pace .pace-progress {\n  background-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-flash-blue .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-flash-blue .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #007bff, 0 0 5px #007bff;\n}\n\n.pace-flash-blue .pace .pace-activity {\n  border-top-color: #007bff;\n  border-left-color: #007bff;\n}\n\n.pace-loading-bar-blue .pace .pace-progress {\n  background: #007bff;\n  color: #007bff;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-blue .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #007bff, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-blue .pace .pace-progress {\n  background-color: #007bff;\n  box-shadow: inset -1px 0 #007bff, inset 0 -1px #007bff, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-blue .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-blue .pace-progress {\n  color: #007bff;\n}\n\n.pace-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-barber-shop-indigo .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-barber-shop-indigo .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-indigo .pace .pace-progress::after {\n  color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-bounce-indigo .pace .pace-activity {\n  background: #6610f2;\n}\n\n.pace-center-atom-indigo .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-indigo .pace-progress::before {\n  background: #6610f2;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-indigo .pace-activity {\n  border-color: #6610f2;\n}\n\n.pace-center-atom-indigo .pace-activity::after, .pace-center-atom-indigo .pace-activity::before {\n  border-color: #6610f2;\n}\n\n.pace-center-circle-indigo .pace .pace-progress {\n  background: rgba(102, 16, 242, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-indigo .pace .pace-activity {\n  border-color: #6610f2 transparent transparent;\n}\n\n.pace-center-radar-indigo .pace .pace-activity::before {\n  border-color: #6610f2 transparent transparent;\n}\n\n.pace-center-simple-indigo .pace {\n  background: #fff;\n  border-color: #6610f2;\n}\n\n.pace-center-simple-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-material-indigo .pace {\n  color: #6610f2;\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity {\n  background: #6610f2;\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity::after,\n.pace-corner-indicator-indigo .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity::before {\n  border-right-color: rgba(102, 16, 242, 0.2);\n  border-left-color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity::after {\n  border-top-color: rgba(102, 16, 242, 0.2);\n  border-bottom-color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-fill-left-indigo .pace .pace-progress {\n  background-color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-flash-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-flash-indigo .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6610f2, 0 0 5px #6610f2;\n}\n\n.pace-flash-indigo .pace .pace-activity {\n  border-top-color: #6610f2;\n  border-left-color: #6610f2;\n}\n\n.pace-loading-bar-indigo .pace .pace-progress {\n  background: #6610f2;\n  color: #6610f2;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-indigo .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6610f2, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-indigo .pace .pace-progress {\n  background-color: #6610f2;\n  box-shadow: inset -1px 0 #6610f2, inset 0 -1px #6610f2, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-indigo .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-indigo .pace-progress {\n  color: #6610f2;\n}\n\n.pace-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-barber-shop-purple .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-barber-shop-purple .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-purple .pace .pace-progress::after {\n  color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-bounce-purple .pace .pace-activity {\n  background: #6f42c1;\n}\n\n.pace-center-atom-purple .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-purple .pace-progress::before {\n  background: #6f42c1;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-purple .pace-activity {\n  border-color: #6f42c1;\n}\n\n.pace-center-atom-purple .pace-activity::after, .pace-center-atom-purple .pace-activity::before {\n  border-color: #6f42c1;\n}\n\n.pace-center-circle-purple .pace .pace-progress {\n  background: rgba(111, 66, 193, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-purple .pace .pace-activity {\n  border-color: #6f42c1 transparent transparent;\n}\n\n.pace-center-radar-purple .pace .pace-activity::before {\n  border-color: #6f42c1 transparent transparent;\n}\n\n.pace-center-simple-purple .pace {\n  background: #fff;\n  border-color: #6f42c1;\n}\n\n.pace-center-simple-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-material-purple .pace {\n  color: #6f42c1;\n}\n\n.pace-corner-indicator-purple .pace .pace-activity {\n  background: #6f42c1;\n}\n\n.pace-corner-indicator-purple .pace .pace-activity::after,\n.pace-corner-indicator-purple .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-purple .pace .pace-activity::before {\n  border-right-color: rgba(111, 66, 193, 0.2);\n  border-left-color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-corner-indicator-purple .pace .pace-activity::after {\n  border-top-color: rgba(111, 66, 193, 0.2);\n  border-bottom-color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-fill-left-purple .pace .pace-progress {\n  background-color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-flash-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-flash-purple .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6f42c1, 0 0 5px #6f42c1;\n}\n\n.pace-flash-purple .pace .pace-activity {\n  border-top-color: #6f42c1;\n  border-left-color: #6f42c1;\n}\n\n.pace-loading-bar-purple .pace .pace-progress {\n  background: #6f42c1;\n  color: #6f42c1;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-purple .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6f42c1, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-purple .pace .pace-progress {\n  background-color: #6f42c1;\n  box-shadow: inset -1px 0 #6f42c1, inset 0 -1px #6f42c1, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-purple .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-purple .pace-progress {\n  color: #6f42c1;\n}\n\n.pace-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-barber-shop-pink .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-barber-shop-pink .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-pink .pace .pace-progress::after {\n  color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-bounce-pink .pace .pace-activity {\n  background: #e83e8c;\n}\n\n.pace-center-atom-pink .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-pink .pace-progress::before {\n  background: #e83e8c;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-pink .pace-activity {\n  border-color: #e83e8c;\n}\n\n.pace-center-atom-pink .pace-activity::after, .pace-center-atom-pink .pace-activity::before {\n  border-color: #e83e8c;\n}\n\n.pace-center-circle-pink .pace .pace-progress {\n  background: rgba(232, 62, 140, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-pink .pace .pace-activity {\n  border-color: #e83e8c transparent transparent;\n}\n\n.pace-center-radar-pink .pace .pace-activity::before {\n  border-color: #e83e8c transparent transparent;\n}\n\n.pace-center-simple-pink .pace {\n  background: #fff;\n  border-color: #e83e8c;\n}\n\n.pace-center-simple-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-material-pink .pace {\n  color: #e83e8c;\n}\n\n.pace-corner-indicator-pink .pace .pace-activity {\n  background: #e83e8c;\n}\n\n.pace-corner-indicator-pink .pace .pace-activity::after,\n.pace-corner-indicator-pink .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-pink .pace .pace-activity::before {\n  border-right-color: rgba(232, 62, 140, 0.2);\n  border-left-color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-corner-indicator-pink .pace .pace-activity::after {\n  border-top-color: rgba(232, 62, 140, 0.2);\n  border-bottom-color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-fill-left-pink .pace .pace-progress {\n  background-color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-flash-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-flash-pink .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #e83e8c, 0 0 5px #e83e8c;\n}\n\n.pace-flash-pink .pace .pace-activity {\n  border-top-color: #e83e8c;\n  border-left-color: #e83e8c;\n}\n\n.pace-loading-bar-pink .pace .pace-progress {\n  background: #e83e8c;\n  color: #e83e8c;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-pink .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #e83e8c, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-pink .pace .pace-progress {\n  background-color: #e83e8c;\n  box-shadow: inset -1px 0 #e83e8c, inset 0 -1px #e83e8c, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-pink .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-pink .pace-progress {\n  color: #e83e8c;\n}\n\n.pace-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-red .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-red .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-red .pace .pace-progress::after {\n  color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-bounce-red .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-center-atom-red .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-red .pace-progress::before {\n  background: #dc3545;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-red .pace-activity {\n  border-color: #dc3545;\n}\n\n.pace-center-atom-red .pace-activity::after, .pace-center-atom-red .pace-activity::before {\n  border-color: #dc3545;\n}\n\n.pace-center-circle-red .pace .pace-progress {\n  background: rgba(220, 53, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-red .pace .pace-activity {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-radar-red .pace .pace-activity::before {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-simple-red .pace {\n  background: #fff;\n  border-color: #dc3545;\n}\n\n.pace-center-simple-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-material-red .pace {\n  color: #dc3545;\n}\n\n.pace-corner-indicator-red .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-corner-indicator-red .pace .pace-activity::after,\n.pace-corner-indicator-red .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-red .pace .pace-activity::before {\n  border-right-color: rgba(220, 53, 69, 0.2);\n  border-left-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-corner-indicator-red .pace .pace-activity::after {\n  border-top-color: rgba(220, 53, 69, 0.2);\n  border-bottom-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-fill-left-red .pace .pace-progress {\n  background-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-flash-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-flash-red .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #dc3545, 0 0 5px #dc3545;\n}\n\n.pace-flash-red .pace .pace-activity {\n  border-top-color: #dc3545;\n  border-left-color: #dc3545;\n}\n\n.pace-loading-bar-red .pace .pace-progress {\n  background: #dc3545;\n  color: #dc3545;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-red .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #dc3545, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-red .pace .pace-progress {\n  background-color: #dc3545;\n  box-shadow: inset -1px 0 #dc3545, inset 0 -1px #dc3545, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-red .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-red .pace-progress {\n  color: #dc3545;\n}\n\n.pace-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-barber-shop-orange .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-barber-shop-orange .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-orange .pace .pace-progress::after {\n  color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-bounce-orange .pace .pace-activity {\n  background: #fd7e14;\n}\n\n.pace-center-atom-orange .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-orange .pace-progress::before {\n  background: #fd7e14;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-orange .pace-activity {\n  border-color: #fd7e14;\n}\n\n.pace-center-atom-orange .pace-activity::after, .pace-center-atom-orange .pace-activity::before {\n  border-color: #fd7e14;\n}\n\n.pace-center-circle-orange .pace .pace-progress {\n  background: rgba(253, 126, 20, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-orange .pace .pace-activity {\n  border-color: #fd7e14 transparent transparent;\n}\n\n.pace-center-radar-orange .pace .pace-activity::before {\n  border-color: #fd7e14 transparent transparent;\n}\n\n.pace-center-simple-orange .pace {\n  background: #1f2d3d;\n  border-color: #fd7e14;\n}\n\n.pace-center-simple-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-material-orange .pace {\n  color: #fd7e14;\n}\n\n.pace-corner-indicator-orange .pace .pace-activity {\n  background: #fd7e14;\n}\n\n.pace-corner-indicator-orange .pace .pace-activity::after,\n.pace-corner-indicator-orange .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-orange .pace .pace-activity::before {\n  border-right-color: rgba(253, 126, 20, 0.2);\n  border-left-color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-corner-indicator-orange .pace .pace-activity::after {\n  border-top-color: rgba(253, 126, 20, 0.2);\n  border-bottom-color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-fill-left-orange .pace .pace-progress {\n  background-color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-flash-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-flash-orange .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #fd7e14, 0 0 5px #fd7e14;\n}\n\n.pace-flash-orange .pace .pace-activity {\n  border-top-color: #fd7e14;\n  border-left-color: #fd7e14;\n}\n\n.pace-loading-bar-orange .pace .pace-progress {\n  background: #fd7e14;\n  color: #fd7e14;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-orange .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #fd7e14, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-orange .pace .pace-progress {\n  background-color: #fd7e14;\n  box-shadow: inset -1px 0 #fd7e14, inset 0 -1px #fd7e14, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-orange .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-orange .pace-progress {\n  color: #fd7e14;\n}\n\n.pace-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-yellow .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-yellow .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-yellow .pace .pace-progress::after {\n  color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-bounce-yellow .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-center-atom-yellow .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-yellow .pace-progress::before {\n  background: #ffc107;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-yellow .pace-activity {\n  border-color: #ffc107;\n}\n\n.pace-center-atom-yellow .pace-activity::after, .pace-center-atom-yellow .pace-activity::before {\n  border-color: #ffc107;\n}\n\n.pace-center-circle-yellow .pace .pace-progress {\n  background: rgba(255, 193, 7, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-yellow .pace .pace-activity {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-radar-yellow .pace .pace-activity::before {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-simple-yellow .pace {\n  background: #1f2d3d;\n  border-color: #ffc107;\n}\n\n.pace-center-simple-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-material-yellow .pace {\n  color: #ffc107;\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity::after,\n.pace-corner-indicator-yellow .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity::before {\n  border-right-color: rgba(255, 193, 7, 0.2);\n  border-left-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity::after {\n  border-top-color: rgba(255, 193, 7, 0.2);\n  border-bottom-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-fill-left-yellow .pace .pace-progress {\n  background-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-flash-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-flash-yellow .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #ffc107, 0 0 5px #ffc107;\n}\n\n.pace-flash-yellow .pace .pace-activity {\n  border-top-color: #ffc107;\n  border-left-color: #ffc107;\n}\n\n.pace-loading-bar-yellow .pace .pace-progress {\n  background: #ffc107;\n  color: #ffc107;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-yellow .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #ffc107, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-yellow .pace .pace-progress {\n  background-color: #ffc107;\n  box-shadow: inset -1px 0 #ffc107, inset 0 -1px #ffc107, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-yellow .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-yellow .pace-progress {\n  color: #ffc107;\n}\n\n.pace-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-green .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-green .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-green .pace .pace-progress::after {\n  color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-bounce-green .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-center-atom-green .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-green .pace-progress::before {\n  background: #28a745;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-green .pace-activity {\n  border-color: #28a745;\n}\n\n.pace-center-atom-green .pace-activity::after, .pace-center-atom-green .pace-activity::before {\n  border-color: #28a745;\n}\n\n.pace-center-circle-green .pace .pace-progress {\n  background: rgba(40, 167, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-green .pace .pace-activity {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-radar-green .pace .pace-activity::before {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-simple-green .pace {\n  background: #fff;\n  border-color: #28a745;\n}\n\n.pace-center-simple-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-material-green .pace {\n  color: #28a745;\n}\n\n.pace-corner-indicator-green .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-corner-indicator-green .pace .pace-activity::after,\n.pace-corner-indicator-green .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-green .pace .pace-activity::before {\n  border-right-color: rgba(40, 167, 69, 0.2);\n  border-left-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-corner-indicator-green .pace .pace-activity::after {\n  border-top-color: rgba(40, 167, 69, 0.2);\n  border-bottom-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-fill-left-green .pace .pace-progress {\n  background-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-flash-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-flash-green .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #28a745, 0 0 5px #28a745;\n}\n\n.pace-flash-green .pace .pace-activity {\n  border-top-color: #28a745;\n  border-left-color: #28a745;\n}\n\n.pace-loading-bar-green .pace .pace-progress {\n  background: #28a745;\n  color: #28a745;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-green .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #28a745, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-green .pace .pace-progress {\n  background-color: #28a745;\n  box-shadow: inset -1px 0 #28a745, inset 0 -1px #28a745, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-green .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-green .pace-progress {\n  color: #28a745;\n}\n\n.pace-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-barber-shop-teal .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-barber-shop-teal .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-teal .pace .pace-progress::after {\n  color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-bounce-teal .pace .pace-activity {\n  background: #20c997;\n}\n\n.pace-center-atom-teal .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-teal .pace-progress::before {\n  background: #20c997;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-teal .pace-activity {\n  border-color: #20c997;\n}\n\n.pace-center-atom-teal .pace-activity::after, .pace-center-atom-teal .pace-activity::before {\n  border-color: #20c997;\n}\n\n.pace-center-circle-teal .pace .pace-progress {\n  background: rgba(32, 201, 151, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-teal .pace .pace-activity {\n  border-color: #20c997 transparent transparent;\n}\n\n.pace-center-radar-teal .pace .pace-activity::before {\n  border-color: #20c997 transparent transparent;\n}\n\n.pace-center-simple-teal .pace {\n  background: #fff;\n  border-color: #20c997;\n}\n\n.pace-center-simple-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-material-teal .pace {\n  color: #20c997;\n}\n\n.pace-corner-indicator-teal .pace .pace-activity {\n  background: #20c997;\n}\n\n.pace-corner-indicator-teal .pace .pace-activity::after,\n.pace-corner-indicator-teal .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-teal .pace .pace-activity::before {\n  border-right-color: rgba(32, 201, 151, 0.2);\n  border-left-color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-corner-indicator-teal .pace .pace-activity::after {\n  border-top-color: rgba(32, 201, 151, 0.2);\n  border-bottom-color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-fill-left-teal .pace .pace-progress {\n  background-color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-flash-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-flash-teal .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #20c997, 0 0 5px #20c997;\n}\n\n.pace-flash-teal .pace .pace-activity {\n  border-top-color: #20c997;\n  border-left-color: #20c997;\n}\n\n.pace-loading-bar-teal .pace .pace-progress {\n  background: #20c997;\n  color: #20c997;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-teal .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #20c997, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-teal .pace .pace-progress {\n  background-color: #20c997;\n  box-shadow: inset -1px 0 #20c997, inset 0 -1px #20c997, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-teal .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-teal .pace-progress {\n  color: #20c997;\n}\n\n.pace-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-cyan .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-cyan .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-cyan .pace .pace-progress::after {\n  color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-bounce-cyan .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-center-atom-cyan .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-cyan .pace-progress::before {\n  background: #17a2b8;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-cyan .pace-activity {\n  border-color: #17a2b8;\n}\n\n.pace-center-atom-cyan .pace-activity::after, .pace-center-atom-cyan .pace-activity::before {\n  border-color: #17a2b8;\n}\n\n.pace-center-circle-cyan .pace .pace-progress {\n  background: rgba(23, 162, 184, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-cyan .pace .pace-activity {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-radar-cyan .pace .pace-activity::before {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-simple-cyan .pace {\n  background: #fff;\n  border-color: #17a2b8;\n}\n\n.pace-center-simple-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-material-cyan .pace {\n  color: #17a2b8;\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity::after,\n.pace-corner-indicator-cyan .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity::before {\n  border-right-color: rgba(23, 162, 184, 0.2);\n  border-left-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity::after {\n  border-top-color: rgba(23, 162, 184, 0.2);\n  border-bottom-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-fill-left-cyan .pace .pace-progress {\n  background-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-flash-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-flash-cyan .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #17a2b8, 0 0 5px #17a2b8;\n}\n\n.pace-flash-cyan .pace .pace-activity {\n  border-top-color: #17a2b8;\n  border-left-color: #17a2b8;\n}\n\n.pace-loading-bar-cyan .pace .pace-progress {\n  background: #17a2b8;\n  color: #17a2b8;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-cyan .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #17a2b8, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-cyan .pace .pace-progress {\n  background-color: #17a2b8;\n  box-shadow: inset -1px 0 #17a2b8, inset 0 -1px #17a2b8, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-cyan .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-cyan .pace-progress {\n  color: #17a2b8;\n}\n\n.pace-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-barber-shop-white .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-barber-shop-white .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-white .pace .pace-progress::after {\n  color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-bounce-white .pace .pace-activity {\n  background: #fff;\n}\n\n.pace-center-atom-white .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-white .pace-progress::before {\n  background: #fff;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-white .pace-activity {\n  border-color: #fff;\n}\n\n.pace-center-atom-white .pace-activity::after, .pace-center-atom-white .pace-activity::before {\n  border-color: #fff;\n}\n\n.pace-center-circle-white .pace .pace-progress {\n  background: rgba(255, 255, 255, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-white .pace .pace-activity {\n  border-color: #fff transparent transparent;\n}\n\n.pace-center-radar-white .pace .pace-activity::before {\n  border-color: #fff transparent transparent;\n}\n\n.pace-center-simple-white .pace {\n  background: #1f2d3d;\n  border-color: #fff;\n}\n\n.pace-center-simple-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-material-white .pace {\n  color: #fff;\n}\n\n.pace-corner-indicator-white .pace .pace-activity {\n  background: #fff;\n}\n\n.pace-corner-indicator-white .pace .pace-activity::after,\n.pace-corner-indicator-white .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-white .pace .pace-activity::before {\n  border-right-color: rgba(255, 255, 255, 0.2);\n  border-left-color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-corner-indicator-white .pace .pace-activity::after {\n  border-top-color: rgba(255, 255, 255, 0.2);\n  border-bottom-color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-fill-left-white .pace .pace-progress {\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-flash-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-flash-white .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #fff, 0 0 5px #fff;\n}\n\n.pace-flash-white .pace .pace-activity {\n  border-top-color: #fff;\n  border-left-color: #fff;\n}\n\n.pace-loading-bar-white .pace .pace-progress {\n  background: #fff;\n  color: #fff;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-white .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #fff, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-white .pace .pace-progress {\n  background-color: #fff;\n  box-shadow: inset -1px 0 #fff, inset 0 -1px #fff, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-white .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-white .pace-progress {\n  color: #fff;\n}\n\n.pace-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-gray .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-gray .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-gray .pace .pace-progress::after {\n  color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-bounce-gray .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-center-atom-gray .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-gray .pace-progress::before {\n  background: #6c757d;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-gray .pace-activity {\n  border-color: #6c757d;\n}\n\n.pace-center-atom-gray .pace-activity::after, .pace-center-atom-gray .pace-activity::before {\n  border-color: #6c757d;\n}\n\n.pace-center-circle-gray .pace .pace-progress {\n  background: rgba(108, 117, 125, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-gray .pace .pace-activity {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-radar-gray .pace .pace-activity::before {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-simple-gray .pace {\n  background: #fff;\n  border-color: #6c757d;\n}\n\n.pace-center-simple-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-material-gray .pace {\n  color: #6c757d;\n}\n\n.pace-corner-indicator-gray .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-corner-indicator-gray .pace .pace-activity::after,\n.pace-corner-indicator-gray .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-gray .pace .pace-activity::before {\n  border-right-color: rgba(108, 117, 125, 0.2);\n  border-left-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-corner-indicator-gray .pace .pace-activity::after {\n  border-top-color: rgba(108, 117, 125, 0.2);\n  border-bottom-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-fill-left-gray .pace .pace-progress {\n  background-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-flash-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-flash-gray .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6c757d, 0 0 5px #6c757d;\n}\n\n.pace-flash-gray .pace .pace-activity {\n  border-top-color: #6c757d;\n  border-left-color: #6c757d;\n}\n\n.pace-loading-bar-gray .pace .pace-progress {\n  background: #6c757d;\n  color: #6c757d;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-gray .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6c757d, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-gray .pace .pace-progress {\n  background-color: #6c757d;\n  box-shadow: inset -1px 0 #6c757d, inset 0 -1px #6c757d, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-gray .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-gray .pace-progress {\n  color: #6c757d;\n}\n\n.pace-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-gray-dark .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-gray-dark .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-gray-dark .pace .pace-progress::after {\n  color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-bounce-gray-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-center-atom-gray-dark .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-gray-dark .pace-progress::before {\n  background: #343a40;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-gray-dark .pace-activity {\n  border-color: #343a40;\n}\n\n.pace-center-atom-gray-dark .pace-activity::after, .pace-center-atom-gray-dark .pace-activity::before {\n  border-color: #343a40;\n}\n\n.pace-center-circle-gray-dark .pace .pace-progress {\n  background: rgba(52, 58, 64, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-gray-dark .pace .pace-activity {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-radar-gray-dark .pace .pace-activity::before {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-simple-gray-dark .pace {\n  background: #fff;\n  border-color: #343a40;\n}\n\n.pace-center-simple-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-material-gray-dark .pace {\n  color: #343a40;\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity::after,\n.pace-corner-indicator-gray-dark .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity::before {\n  border-right-color: rgba(52, 58, 64, 0.2);\n  border-left-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity::after {\n  border-top-color: rgba(52, 58, 64, 0.2);\n  border-bottom-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-fill-left-gray-dark .pace .pace-progress {\n  background-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-flash-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-flash-gray-dark .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #343a40, 0 0 5px #343a40;\n}\n\n.pace-flash-gray-dark .pace .pace-activity {\n  border-top-color: #343a40;\n  border-left-color: #343a40;\n}\n\n.pace-loading-bar-gray-dark .pace .pace-progress {\n  background: #343a40;\n  color: #343a40;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-gray-dark .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #343a40, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-gray-dark .pace .pace-progress {\n  background-color: #343a40;\n  box-shadow: inset -1px 0 #343a40, inset 0 -1px #343a40, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-gray-dark .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-gray-dark .pace-progress {\n  color: #343a40;\n}\n\n/**\n  * bootstrap-switch - Turn checkboxes and radio buttons into toggle switches.\n  *\n  * @version v3.4 (MODDED)\n  * @homepage https://bttstrp.github.io/bootstrap-switch\n  * <AUTHOR> Larentis <<EMAIL>> (http://larentis.eu)\n  * @license MIT\n  */\n.bootstrap-switch {\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n  cursor: pointer;\n  direction: ltr;\n  display: inline-block;\n  line-height: .5rem;\n  overflow: hidden;\n  position: relative;\n  text-align: left;\n  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;\n  user-select: none;\n  vertical-align: middle;\n  z-index: 0;\n}\n\n.bootstrap-switch .bootstrap-switch-container {\n  border-radius: 0.25rem;\n  display: inline-block;\n  top: 0;\n  transform: translate3d(0, 0, 0);\n}\n\n.bootstrap-switch:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on,\n.bootstrap-switch .bootstrap-switch-handle-off,\n.bootstrap-switch .bootstrap-switch-label {\n  box-sizing: border-box;\n  cursor: pointer;\n  display: table-cell;\n  font-size: 1rem;\n  font-weight: 500;\n  line-height: 1.2rem;\n  padding: .25rem .5rem;\n  vertical-align: middle;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on,\n.bootstrap-switch .bootstrap-switch-handle-off {\n  text-align: center;\n  z-index: 1;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default {\n  background: #e9ecef;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary {\n  background: #007bff;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-secondary,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-secondary {\n  background: #6c757d;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {\n  background: #28a745;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info {\n  background: #17a2b8;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning {\n  background: #ffc107;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger {\n  background: #dc3545;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-light,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-light {\n  background: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-dark,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lightblue,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lightblue {\n  background: #3c8dbc;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-navy,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-navy {\n  background: #001f3f;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-olive,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-olive {\n  background: #3d9970;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lime,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lime {\n  background: #01ff70;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-fuchsia,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-fuchsia {\n  background: #f012be;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-maroon,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-maroon {\n  background: #d81b60;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-blue,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-blue {\n  background: #007bff;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-indigo,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-indigo {\n  background: #6610f2;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-purple,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-purple {\n  background: #6f42c1;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-pink,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-pink {\n  background: #e83e8c;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-red,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-red {\n  background: #dc3545;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-orange,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-orange {\n  background: #fd7e14;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-yellow,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-yellow {\n  background: #ffc107;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-green,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-green {\n  background: #28a745;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-teal,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-teal {\n  background: #20c997;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-cyan,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-cyan {\n  background: #17a2b8;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-white,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-white {\n  background: #fff;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray {\n  background: #6c757d;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray-dark,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on {\n  border-bottom-left-radius: 0.1rem;\n  border-top-left-radius: 0.1rem;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-off {\n  border-bottom-right-radius: 0.1rem;\n  border-top-right-radius: 0.1rem;\n}\n\n.bootstrap-switch input[type='radio'],\n.bootstrap-switch input[type='checkbox'] {\n  filter: alpha(opacity=0);\n  left: 0;\n  margin: 0;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  visibility: hidden;\n  z-index: -1;\n}\n\n.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-label {\n  font-size: .875rem;\n  line-height: 1.5;\n  padding: .1rem .3rem;\n}\n\n.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-label {\n  font-size: .875rem;\n  line-height: 1.5;\n  padding: .2rem .4rem;\n}\n\n.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-label {\n  font-size: 1.25rem;\n  line-height: 1.3333333rem;\n  padding: .3rem .5rem;\n}\n\n.bootstrap-switch.bootstrap-switch-disabled, .bootstrap-switch.bootstrap-switch-readonly, .bootstrap-switch.bootstrap-switch-indeterminate {\n  cursor: default;\n}\n\n.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-label, .bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-label, .bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-label {\n  cursor: default;\n  filter: alpha(opacity=50);\n  opacity: .5;\n}\n\n.bootstrap-switch.bootstrap-switch-animate .bootstrap-switch-container {\n  transition: margin-left .5s;\n}\n\n.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-on {\n  border-radius: 0 0.1rem 0.1rem 0;\n}\n\n.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-off {\n  border-radius: 0.1rem 0 0 0.1rem;\n}\n\n.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-label,\n.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-off .bootstrap-switch-label {\n  border-bottom-right-radius: 0.1rem;\n  border-top-right-radius: 0.1rem;\n}\n\n.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-label,\n.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-on .bootstrap-switch-label {\n  border-bottom-left-radius: 0.1rem;\n  border-top-left-radius: 0.1rem;\n}\n\n.dark-mode .bootstrap-switch {\n  border-color: #6c757d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default {\n  background-color: #3a4047;\n  color: #fff;\n  border-color: #454d55;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary {\n  background: #3f6791;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-secondary,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-secondary {\n  background: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {\n  background: #00bc8c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info {\n  background: #3498db;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning {\n  background: #f39c12;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger {\n  background: #e74c3c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-light,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-light {\n  background: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-dark,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lightblue,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lightblue {\n  background: #86bad8;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-navy,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-navy {\n  background: #002c59;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-olive,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-olive {\n  background: #74c8a3;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lime,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lime {\n  background: #67ffa9;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-fuchsia,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-fuchsia {\n  background: #f672d8;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-maroon,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-maroon {\n  background: #ed6c9b;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-blue,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-blue {\n  background: #3f6791;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-indigo,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-indigo {\n  background: #6610f2;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-purple,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-purple {\n  background: #6f42c1;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-pink,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-pink {\n  background: #e83e8c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-red,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-red {\n  background: #e74c3c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-orange,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-orange {\n  background: #fd7e14;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-yellow,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-yellow {\n  background: #f39c12;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-green,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-green {\n  background: #00bc8c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-teal,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-teal {\n  background: #20c997;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-cyan,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-cyan {\n  background: #3498db;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-white,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-white {\n  background: #fff;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray {\n  background: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray-dark,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.dark-mode .daterangepicker {\n  background-color: #3f474e;\n  border: inherit;\n}\n\n.dark-mode .daterangepicker::before, .dark-mode .daterangepicker::after {\n  border-bottom-color: #3f474e;\n}\n\n.dark-mode .daterangepicker td.available:hover,\n.dark-mode .daterangepicker th.available:hover {\n  background-color: #3f474e;\n}\n\n.dark-mode .daterangepicker td.in-range {\n  background-color: #4b545c;\n  color: #fff;\n}\n\n.dark-mode .daterangepicker td.off,\n.dark-mode .daterangepicker td.off.in-range,\n.dark-mode .daterangepicker td.off.start-date,\n.dark-mode .daterangepicker td.off.end-date {\n  background-color: #292d32;\n  color: #fff;\n}\n\n.dark-mode .daterangepicker .ranges li:hover {\n  background-color: #343a40;\n}\n\n.dark-mode .daterangepicker.show-ranges.ltr .drp-calendar {\n  border-color: #4b545c;\n}\n\n.dark-mode .daterangepicker.show-ranges.ltr .drp-calendar.left, .dark-mode .daterangepicker.show-ranges.ltr .drp-calendar.right {\n  border-color: #4b545c;\n  padding-top: 0;\n}\n\n.dark-mode .daterangepicker .drp-buttons {\n  border-color: #4b545c;\n}\n\n.dark-mode .daterangepicker .calendar-table {\n  background-color: #343a40;\n  border-color: #4b545c;\n}\n\n.dark-mode .daterangepicker .calendar-table th,\n.dark-mode .daterangepicker .calendar-table td {\n  color: #fff;\n}\n\n.dark-mode .daterangepicker .calendar-table .next span,\n.dark-mode .daterangepicker .calendar-table .prev span {\n  border-color: #fff;\n}\n\n.dark-mode .daterangepicker select.hourselect,\n.dark-mode .daterangepicker select.minuteselect,\n.dark-mode .daterangepicker select.secondselect,\n.dark-mode .daterangepicker select.ampmselect {\n  background-color: #343a40;\n  border-color: #4b545c;\n}\n\n.jqstooltip {\n  height: auto !important;\n  padding: 5px !important;\n  width: auto !important;\n}\n\n.connectedSortable {\n  min-height: 100px;\n}\n\n.ui-helper-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sort-highlight {\n  background: #f8f9fa;\n  border: 1px dashed #dee2e6;\n  margin-bottom: 10px;\n}\n\n.chart {\n  overflow: hidden;\n  position: relative;\n}\n\n.dark-mode .irs--flat .irs-line {\n  background-color: #4b545c;\n}\n\n.dark-mode .jsgrid-edit-row > .jsgrid-cell,\n.dark-mode .jsgrid-filter-row > .jsgrid-cell,\n.dark-mode .jsgrid-grid-body, .dark-mode .jsgrid-grid-header,\n.dark-mode .jsgrid-header-row > .jsgrid-header-cell,\n.dark-mode .jsgrid-insert-row > .jsgrid-cell,\n.dark-mode .jsgrid-row > .jsgrid-cell,\n.dark-mode .jsgrid-alt-row > .jsgrid-cell {\n  border-color: #6c757d;\n}\n\n.dark-mode .jsgrid-header-row > .jsgrid-header-cell,\n.dark-mode .jsgrid-row > .jsgrid-cell {\n  background-color: #343a40;\n}\n\n.dark-mode .jsgrid-alt-row > .jsgrid-cell {\n  background-color: #3a4047;\n}\n\n.dark-mode .jsgrid-selected-row > .jsgrid-cell {\n  background-color: #3f474e;\n}\n\n/*# sourceMappingURL=adminlte.plugins.css.map */", "//\n// Mixins: Animation\n//\n\n\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes shake {\n  0% {\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes wobble {\n  0% {\n    transform: none;\n  }\n\n  15% {\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n\n  30% {\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n\n  45% {\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n\n  60% {\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n\n  75% {\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n\n  100% {\n    transform: none;\n  }\n}\n\n//\n", "//\n// Plugin: Full Calendar\n//\n\n// Buttons\n.fc-button {\n  background: $gray-100;\n  background-image: none;\n  border-bottom-color: #ddd;\n  border-color: #ddd;\n  color: $gray-700;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: #e9e9e9;\n  }\n}\n\n// Calendar title\n.fc-header-title h2 {\n  color: #666;\n  font-size: 15px;\n  line-height: 1.6em;\n  margin-left: 10px;\n}\n\n.fc-header-right {\n  padding-right: 10px;\n}\n\n.fc-header-left {\n  padding-left: 10px;\n}\n\n// Calendar table header cells\n.fc-widget-header {\n  background: #fafafa;\n}\n\n.fc-grid {\n  border: 0;\n  width: 100%;\n}\n\n.fc-widget-header:first-of-type,\n.fc-widget-content:first-of-type {\n  border-left: 0;\n  border-right: 0;\n}\n\n.fc-widget-header:last-of-type,\n.fc-widget-content:last-of-type {\n  border-right: 0;\n}\n\n.fc-toolbar,\n.fc-toolbar.fc-header-toolbar {\n  margin: 0;\n  padding: 1rem;\n}\n\n@include media-breakpoint-down(xs) {\n  .fc-toolbar {\n    flex-direction: column;\n\n    .fc-left {\n      order: 1;\n      margin-bottom: .5rem;\n    }\n\n    .fc-center {\n      order: 0;\n      margin-bottom: .375rem;\n    }\n\n    .fc-right {\n      order: 2;\n    }\n  }\n}\n\n.fc-day-number {\n  font-size: 20px;\n  font-weight: 300;\n  padding-right: 10px;\n}\n\n.fc-color-picker {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n\n  > li {\n    float: left;\n    font-size: 30px;\n    line-height: 30px;\n    margin-right: 5px;\n\n    .fa,\n    .fas,\n    .far,\n    .fab,\n    .fal,\n    .fad,\n    .svg-inline--fa,\n    .ion {\n      transition: transform linear .3s;\n\n      &:hover {\n        @include rotate(30deg);\n      }\n    }\n  }\n}\n\n#add-new-event {\n  transition: all linear .3s;\n}\n\n.external-event {\n  @include box-shadow($card-shadow);\n\n  border-radius: $border-radius;\n  cursor: move;\n  font-weight: 700;\n  margin-bottom: 4px;\n  padding: 5px 10px;\n\n  &:hover {\n    @include box-shadow(inset 0 0 90px rgba(0, 0, 0, 0.2));\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n//\n// Color system\n//\n\n// stylelint-disable\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge((\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n), $grays);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge((\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n), $colors);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge((\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n), $theme-colors);\n// stylelint-enable\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: #1f2d3d !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              true !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// stylelint-disable\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge((\n  0: 0,\n  1: ($spacer * .25),\n  2: ($spacer * .5),\n  3: $spacer,\n  4: ($spacer * 1.5),\n  5: ($spacer * 3)\n), $spacers);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge((\n  25: 25%,\n  50: 50%,\n  75: 75%,\n  100: 100%\n), $sizes);\n// stylelint-enable\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                theme-color(\"primary\") !default;\n$link-decoration:           none !default;\n$link-hover-color:          darken($link-color, 15%) !default;\n$link-hover-decoration:     none !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           15px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                ($font-size-base * 1.25) !default;\n$font-size-sm:                ($font-size-base * .875) !default;\n\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      ($spacer * .5) !default;\n$headings-font-family:        inherit !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              ($font-size-base * 1.25) !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-bg:                    transparent !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $gray-300 !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-bg:               $gray-900 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($gray-900, 10%) !default;\n$table-dark-color:            $body-bg !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              none !default;\n$btn-focus-width:             0 !default;\n$btn-focus-box-shadow:        none !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       none !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 0 0 rgba($black, 0) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     0 !default;\n$input-focus-box-shadow:                none !default;\n\n$input-placeholder-color:               lighten($gray-600, 15%) !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y * .5}) !default;\n\n$input-height:                          calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:                 ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:                       calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:                 ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:                       calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $gray-300 !default;\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-control-indicator-disabled-bg:          $gray-200 !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n\n$custom-control-indicator-focus-box-shadow:     0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:    $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:  str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:           .375rem !default;\n$custom-select-padding-x:          .75rem !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $white !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'><path fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>\") !default;\n$custom-select-background:          escape-svg($custom-select-indicator) right $custom-select-padding-x center / $custom-select-bg-size no-repeat !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n$custom-select-border-width:        $input-btn-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-box-shadow:    none !default;\n\n$custom-select-font-size-sm:        75% !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-font-size-lg:        125% !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $custom-select-focus-box-shadow !default;\n\n$custom-file-padding-y:             $input-btn-padding-y !default;\n$custom-file-padding-x:             $input-btn-padding-x !default;\n$custom-file-line-height:           $input-btn-line-height !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-btn-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $custom-select-focus-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n\n// Form validation\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer * .5) !default;\n$navbar-padding-x:                  ($spacer * .5) !default;\n\n$navbar-nav-link-padding-x:         1rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .75) !default;\n$navbar-dark-hover-color:           rgba($white, 1) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 0 !default; //$border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 ($grid-gutter-width * .5) !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:           $font-size-sm !default;\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-border-radius:        $border-radius !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           .25rem !default;\n$tooltip-padding-x:           .5rem !default;\n$tooltip-margin:              0 !default;\n\n$tooltip-arrow-width:         .8rem !default;\n$tooltip-arrow-height:        .4rem !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         1rem !default;\n\n$modal-dialog-margin:         .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black, .2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-border-radius:    $border-radius-lg !default;\n$modal-content-box-shadow-xs:    0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        1rem !default;\n\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                ($font-size-base * .75) !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// List group\n\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:            $white !default;\n$carousel-control-width:            15% !default;\n$carousel-control-opacity:          .5 !default;\n\n$carousel-indicator-width:          30px !default;\n$carousel-indicator-height:         3px !default;\n$carousel-indicator-spacer:         3px !default;\n$carousel-indicator-active-bg:      $white !default;\n\n$carousel-caption-width:            70% !default;\n$carousel-caption-color:            $white !default;\n\n$carousel-control-icon-width:       20px !default;\n\n$carousel-control-prev-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:               transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Printing\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\n// Mixins: Miscellaneous\n//\n\n// ETC\n@mixin translate($x, $y) {\n  transform: translate($x, $y);\n}\n\n// Different radius each side\n@mixin border-radius-sides($top-left, $top-right, $bottom-left, $bottom-right) {\n  border-radius: $top-left $top-right $bottom-left $bottom-right;\n}\n\n@mixin calc($property, $expression) {\n  #{$property}: calc(#{$expression});\n}\n\n@mixin rotate($value) {\n  transform: rotate($value);\n}\n\n@mixin animation($animation) {\n  animation: $animation;\n}\n\n// Gradient background\n@mixin gradient($color: #f5f5f5, $start: #eee, $stop: $white) {\n  background-color: $color;\n  background-image: gradient(linear, left bottom, left top, color-stop(0, $start), color-stop(1, $stop));\n}\n\n//\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Core: Variables\n//\n\n// COLORS\n// --------------------------------------------------------\n$blue: #0073b7 !default;\n$lightblue: #3c8dbc !default;\n$navy: #001f3f !default;\n$teal: #39cccc !default;\n$olive: #3d9970 !default;\n$lime: #01ff70 !default;\n$orange: #ff851b !default;\n$fuchsia: #f012be !default;\n$purple: #605ca8 !default;\n$maroon: #d81b60 !default;\n$black: #111 !default;\n$gray-x-light: #d2d6de !default;\n\n$colors: map-merge(\n  (\n    \"lightblue\": $lightblue,\n    \"navy\": $navy,\n    \"olive\": $olive,\n    \"lime\": $lime,\n    \"fuchsia\": $fuchsia,\n    \"maroon\": $maroon,\n  ),\n  $colors\n);\n\n// LAYOUT\n// --------------------------------------------------------\n\n$font-size-root: 1rem !default;\n\n// Sidebar\n$sidebar-width: 250px !default;\n$sidebar-padding-x: .5rem !default;\n$sidebar-padding-y: 0 !default;\n$sidebar-custom-height: 4rem !default;\n$sidebar-custom-height-lg: 6rem !default;\n$sidebar-custom-height-xl: 8rem !default;\n$sidebar-custom-padding-x: .85rem !default;\n$sidebar-custom-padding-y: .5rem !default;\n\n// Boxed layout maximum width\n$boxed-layout-max-width: 1250px !default;\n\n// Body background (Affects main content background only)\n$main-bg: #f4f6f9 !default;\n\n$dark-main-bg: lighten($dark, 7.5%) !important;\n\n// Content padding\n$content-padding-y: 0 !default;\n$content-padding-x: $navbar-padding-x !default;\n\n// IMAGE SIZES\n// --------------------------------------------------------\n$img-size-sm: 1.875rem !default;\n$img-size-md: 3.75rem !default;\n$img-size-lg: 6.25rem !default;\n$img-size-push: .625rem !default;\n\n// MAIN HEADER\n// --------------------------------------------------------\n$main-header-bottom-border-width: $border-width !default;\n$main-header-bottom-border-color: $gray-300 !default;\n$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;\n$main-header-link-padding-y: $navbar-padding-y !default;\n$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;\n$nav-link-sm-padding-y: .35rem !default;\n$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;\n$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;\n\n\n// Main header skins\n$main-header-dark-form-control-bg: $gray-800 !default;\n$main-header-dark-form-control-focused-bg: $gray-700 !default;\n$main-header-dark-form-control-focused-color: $gray-400 !default;\n$main-header-dark-form-control-border-color: $gray-600 !default;\n$main-header-dark-form-control-focused-border-color: $gray-600 !default;\n$main-header-dark-placeholder-color: rgba($white, .6) !default;\n\n$main-header-light-form-control-bg: darken($gray-200, 5%) !default;\n$main-header-light-form-control-focused-bg: darken($gray-200, 7.5%) !default;\n$main-header-light-form-control-focused-color: $gray-400 !default;\n$main-header-light-form-control-border-color: $gray-400 !default;\n$main-header-light-form-control-focused-border-color: darken($gray-400, 2.5%) !default;\n$main-header-light-placeholder-color: rgba(0, 0, 0, .6) !default;\n\n// MAIN FOOTER\n// --------------------------------------------------------\n$main-footer-padding: 1rem !default;\n$main-footer-padding-sm: $main-footer-padding * .812 !default;\n$main-footer-border-top-width: 1px !default;\n$main-footer-border-top-color: $gray-300 !default;\n$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;\n$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;\n$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;\n$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-bg: $white !default;\n\n// SIDEBAR SKINS\n// --------------------------------------------------------\n\n// Dark sidebar\n$sidebar-dark-bg: $dark !default;\n$sidebar-dark-hover-bg: rgba(255, 255, 255, .1) !default;\n$sidebar-dark-color: #c2c7d0 !default;\n$sidebar-dark-hover-color: $white !default;\n$sidebar-dark-active-color: $white !default;\n$sidebar-dark-submenu-bg: transparent !default;\n$sidebar-dark-submenu-color: #c2c7d0 !default;\n$sidebar-dark-submenu-hover-color: $white !default;\n$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;\n$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;\n$sidebar-dark-submenu-active-bg: rgba(255, 255, 255, .9) !default;\n\n// Light sidebar\n$sidebar-light-bg: $white !default;\n$sidebar-light-hover-bg: rgba($black, .1) !default;\n$sidebar-light-color: $gray-800 !default;\n$sidebar-light-hover-color: $gray-900 !default;\n$sidebar-light-active-color: $black !default;\n$sidebar-light-submenu-bg: transparent !default;\n$sidebar-light-submenu-color: #777 !default;\n$sidebar-light-submenu-hover-color: $black !default;\n$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;\n$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;\n$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;\n\n// SIDEBAR MINI\n// --------------------------------------------------------\n$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;\n$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;\n$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x * .5) !default;\n\n// CONTROL SIDEBAR\n// --------------------------------------------------------\n$control-sidebar-width: $sidebar-width !default;\n\n// Cards\n// --------------------------------------------------------\n$card-border-color: $gray-100 !default;\n$card-dark-border-color: lighten($gray-900, 10%) !default;\n$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;\n$card-title-font-size: 1.1rem !default;\n$card-title-font-size-sm: 1rem !default;\n$card-title-font-weight: $font-weight-normal !default;\n$card-nav-link-padding-sm-y: .4rem !default;\n$card-nav-link-padding-sm-x: .8rem !default;\n$card-img-size: $img-size-sm !default;\n\n// PROGRESS BARS\n// --------------------------------------------------------\n$progress-bar-border-radius: 1px !default;\n\n// DIRECT CHAT\n// --------------------------------------------------------\n$direct-chat-default-msg-bg: $gray-x-light !default;\n$direct-chat-default-font-color: #444 !default;\n$direct-chat-default-msg-border-color: $gray-x-light !default;\n\n// Z-INDEX\n// --------------------------------------------------------\n$zindex-main-header: $zindex-fixed + 4 !default;\n$zindex-main-sidebar: $zindex-fixed + 8 !default;\n$zindex-main-footer: $zindex-fixed + 2 !default;\n$zindex-control-sidebar: $zindex-fixed + 1 !default;\n$zindex-toasts: $zindex-main-sidebar + 2 !default;\n$zindex-preloader: 9999 !default;\n\n// TRANSITIONS SETTINGS\n// --------------------------------------------------------\n\n// Transition global options\n$transition-speed: .3s !default;\n$transition-fn: ease-in-out !default;\n\n// TEXT\n// --------------------------------------------------------\n$font-size-xs: ($font-size-base * .75) !default;\n$font-size-xl: ($font-size-base * 2) !default;\n\n\n// BUTTON\n// --------------------------------------------------------\n$button-default-background-color: $gray-100 !default;\n$button-default-color: #444 !default;\n$button-default-border-color: #ddd !default;\n\n$button-padding-y-xs: .125rem !default;\n$button-padding-x-xs: .25rem !default;\n$button-line-height-xs: $line-height-sm !default;\n$button-font-size-xs: ($font-size-base * .75) !default;\n$button-border-radius-xs: .15rem !default;\n\n\n// ELEVATION\n// --------------------------------------------------------\n$elevations: ();\n$elevations: map-merge(\n  (\n    1: unquote(\"0 1px 3px \" + rgba($black, .12) + \", 0 1px 2px \" + rgba($black, .24)),\n    2: unquote(\"0 3px 6px \" + rgba($black, .16) + \", 0 3px 6px \" + rgba($black, .23)),\n    3: unquote(\"0 10px 20px \" + rgba($black, .19) + \", 0 6px 6px \" + rgba($black, .23)),\n    4: unquote(\"0 14px 28px \" + rgba($black, .25) + \", 0 10px 10px \" + rgba($black, .22)),\n    5: unquote(\"0 19px 38px \" + rgba($black, .3) + \", 0 15px 12px \" + rgba($black, .22)),\n  ),\n  $elevations\n);\n\n// RIBBON\n// --------------------------------------------------------\n$ribbon-border-size: 3px !default;\n$ribbon-line-height: 100% !default;\n$ribbon-padding: .375rem 0 !default;\n$ribbon-font-size: .8rem !default;\n$ribbon-width: 90px !default;\n$ribbon-wrapper-size: 70px !default;\n$ribbon-top: 10px !default;\n$ribbon-right: -2px !default;\n$ribbon-lg-wrapper-size: 120px !default;\n$ribbon-lg-width: 160px !default;\n$ribbon-lg-top: 26px !default;\n$ribbon-lg-right: 0 !default;\n$ribbon-xl-wrapper-size: 180px !default;\n$ribbon-xl-width: 240px !default;\n$ribbon-xl-top: 47px !default;\n$ribbon-xl-right: 4px !default;\n\n// CUSTOM FORM SELECT\n// --------------------------------------------------------\n\n$custom-select-dark-indicator-color:     $white !default;\n$custom-select-dark-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'><path fill='#{$custom-select-dark-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>\") !default;\n$custom-select-dark-background:          escape-svg($custom-select-dark-indicator) right $custom-select-padding-x center / $custom-select-bg-size no-repeat !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n// ENABLE DARK MODE\n// --------------------------------------------------------\n$enable-dark-mode: true !default;  // requires `@import \"variables-alt\";`\n\n//\n", "//\n// Plugin: Select2\n//\n\n//Signle select\n// .select2-container--default,\n// .select2-selection {\n//   &.select2-container--focus,\n//   &:focus,\n//   &:active {\n//     outline: none;\n//   }\n// }\n\n.select2-container--default {\n  .select2-selection--single {\n    border: $input-border-width solid $input-border-color;\n    //border-radius: $input-radius;\n    padding: ($input-padding-y * 1.25) $input-padding-x;\n    height: $input-height;\n  }\n\n  &.select2-container--open {\n    .select2-selection--single {\n      border-color: lighten($primary, 25%);\n    }\n  }\n\n  & .select2-dropdown {\n    border: $input-border-width solid $input-border-color;\n    //border-radius: $input-radius;\n  }\n\n  & .select2-results__option {\n    padding: 6px 12px;\n    user-select: none;\n  }\n\n  & .select2-selection--single .select2-selection__rendered {\n    padding-left: 0;\n    //padding-right: 0;\n    height: auto;\n    margin-top: -3px;\n  }\n\n  &[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\n    padding-right: 6px;\n    padding-left: 20px;\n  }\n\n  & .select2-selection--single .select2-selection__arrow {\n    height: 31px;\n    right: 6px;\n  }\n\n  & .select2-selection--single .select2-selection__arrow b {\n    margin-top: 0;\n  }\n\n  .select2-dropdown,\n  .select2-search--inline {\n    .select2-search__field {\n      border: $input-border-width solid $input-border-color;\n\n      &:focus {\n        outline: none;\n        border: $input-border-width solid $input-focus-border-color;\n      }\n    }\n  }\n\n  .select2-dropdown {\n    &.select2-dropdown--below {\n      border-top: 0;\n    }\n\n    &.select2-dropdown--above {\n      border-bottom: 0;\n    }\n  }\n\n  .select2-results__option {\n    &[aria-disabled='true'] {\n      color: $gray-600;\n    }\n\n    &[aria-selected='true'] {\n      $color: $gray-300;\n\n      background-color: $color;\n\n      &,\n      &:hover {\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .select2-results__option--highlighted {\n    $color: $primary;\n    background-color: $color;\n    color: color-yiq($color);\n\n    &[aria-selected] {\n      $color: darken($color, 3%);\n\n      &,\n      &:hover {\n        background-color: $color;\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  //Multiple select\n  & {\n    .select2-selection--multiple {\n      border: $input-border-width solid $input-border-color;\n      min-height: $input-height;\n\n      &:focus {\n        border-color: $input-focus-border-color;\n      }\n\n      .select2-selection__rendered {\n        padding: 0 $input-padding-x * 0.5 $input-padding-y;\n        margin-bottom: -$input-padding-x * 0.5;\n\n        li:first-child.select2-search.select2-search--inline {\n          width: 100%;\n          margin-left: $input-padding-x * 0.5;\n\n          .select2-search__field {\n            width: 100% !important;\n          }\n        }\n\n\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            border: 0;\n            margin-top: 6px;\n          }\n        }\n      }\n\n      .select2-selection__choice {\n        background-color: $primary;\n        border-color: darken($primary, 5%);\n        color: color-yiq($primary);\n        padding: 0 10px;\n        margin-top: .31rem;\n      }\n\n      .select2-selection__choice__remove {\n        color: rgba(255, 255, 255, 0.7);\n        float: right;\n        margin-left: 5px;\n        margin-right: -2px;\n\n        &:hover {\n          color: $white;\n        }\n      }\n\n      .text-sm &,\n      &.text-sm {\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            margin-top: 8px;\n          }\n        }\n\n        .select2-selection__choice {\n          margin-top: .4rem;\n        }\n      }\n    }\n\n    &.select2-container--focus {\n      .select2-selection--single,\n      .select2-selection--multiple {\n        border-color: $input-focus-border-color;\n      }\n\n      .select2-search__field {\n        border: 0;\n      }\n    }\n  }\n\n  & .select2-selection--single .select2-selection__rendered li {\n    padding-right: 10px;\n  }\n\n  .input-group-prepend ~ & {\n    .select2-selection {\n      border-bottom-left-radius: 0;\n      border-top-left-radius: 0;\n    }\n  }\n\n  .input-group > &:not(:last-child) {\n    .select2-selection {\n      border-bottom-right-radius: 0;\n      border-top-right-radius: 0;\n    }\n  }\n}\n\n// Select2 Bootstrap4 Theme overrides\n.select2-container--bootstrap4 {\n  &.select2-container--focus .select2-selection {\n    box-shadow: none;\n  }\n}\n\n// text-sm / form-control-sm override\nselect.form-control-sm ~ {\n  .select2-container--default {\n    font-size: 75%;\n  }\n}\n\n.text-sm,\nselect.form-control-sm ~ {\n  .select2-container--default {\n    .select2-selection--single {\n      height: $input-height-sm;\n\n      .select2-selection__rendered {\n        margin-top: -.4rem;\n      }\n\n      .select2-selection__arrow {\n        top: -.12rem;\n      }\n    }\n\n    .select2-selection--multiple {\n      min-height: $input-height-sm;\n\n      .select2-selection__rendered {\n        padding: 0 $input-padding-x-sm * 0.5 $input-padding-y-sm;\n        margin-top: -($input-padding-x-sm * .2);\n\n        li:first-child.select2-search.select2-search--inline {\n          margin-left: $input-padding-x-sm * 0.5;\n        }\n\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            margin-top: 6px;\n          }\n        }\n      }\n    }\n  }\n}\n\n// Dropdown Fix inside maximized card\n.maximized-card .select2-dropdown {\n  z-index: 9999;\n}\n\n// Background colors (theme colors)\n@each $name, $color in $theme-colors {\n  @include select2-variant($name, $color);\n}\n\n// Background colors (colors)\n@each $name, $color in $colors {\n  @include select2-variant($name, $color);\n}\n\n@include dark-mode () {\n  .select2-selection {\n    background-color: $dark;\n    border-color: $gray-600;\n  }\n\n  .select2-container--disabled .select2-selection--single {\n    background-color: lighten($dark, 7.5%);\n  }\n\n  .select2-selection--single {\n    background-color: $dark;\n    border-color: $gray-600;\n\n    .select2-selection__rendered {\n      color: $white;\n    }\n  }\n  .select2-dropdown .select2-search__field,\n  .select2-search--inline .select2-search__field {\n    background-color: $dark;\n    border-color: $gray-600;\n    color: white;\n  }\n  .select2-dropdown {\n    background-color: $dark;\n    border-color: $gray-600;\n    color: white;\n  }\n  .select2-results__option[aria-selected=\"true\"] {\n    background-color: lighten($dark, 5%) !important;\n    color: $gray-300;\n  }\n  .select2-container .select2-search--inline .select2-search__field {\n    background-color: transparent;\n    color: $white;\n  }\n\n  .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {\n    color: $white;\n  }\n\n  // Background colors (theme colors)\n  @each $name, $color in $theme-colors-alt {\n    @include select2-variant($name, $color);\n  }\n\n  // Background colors (colors)\n  @each $name, $color in $colors-alt {\n    @include select2-variant($name, $color);\n  }\n}\n", "//\n// General: Mixins\n//\n\n// Select2 Variant\n@mixin select2-variant($name, $color) {\n  .select2-#{$name} {\n\n    + .select2-container--default {\n      &.select2-container--open {\n        .select2-selection--single {\n          border-color: lighten($color, 25%);\n        }\n      }\n\n      &.select2-container--focus .select2-selection--single {\n        border-color: lighten($color, 25%);\n      }\n    }\n\n    .select2-container--default &,\n    .select2-container--default {\n      &.select2-dropdown,\n      .select2-dropdown,\n      .select2-search--inline {\n        .select2-search__field {\n          &:focus {\n            border: $input-border-width solid lighten($color, 25%);\n          }\n        }\n      }\n\n      .select2-results__option--highlighted {\n        background-color: $color;\n        color: color-yiq($color);\n\n        &[aria-selected] {\n          &,\n          &:hover {\n            background-color: darken($color, 3%);\n            color: color-yiq(darken($color, 3%));\n          }\n        }\n      }\n\n      //Multiple select\n      & {\n        .select2-selection--multiple {\n          &:focus {\n            border-color: lighten($color, 25%);\n          }\n\n          .select2-selection__choice {\n            background-color: $color;\n            border-color: darken($color, 5%);\n            color: color-yiq($color);\n          }\n\n          .select2-selection__choice__remove {\n            color: rgba(color-yiq($color), 0.7);\n\n            &:hover {\n              color: color-yiq($color);\n            }\n          }\n        }\n\n        &.select2-container--focus .select2-selection--multiple {\n          border-color: lighten($color, 25%);\n        }\n      }\n    }\n  }\n}\n", "//\n// Mixins: Dark Mode Controll\n//\n\n@mixin dark-mode {\n  @if $enable-dark-mode {\n    .dark-mode {\n      @content;\n    }\n  }\n}\n", "//\n// Core: Variables for Dark Mode\n//\n\n// COLORS\n// --------------------------------------------------------\n\n// stylelint-disable\n// Gray color will be default in dark mode\n$white-alt:    $white !default;\n$gray-100-alt: $gray-100 !default;\n$gray-200-alt: $gray-200 !default;\n$gray-300-alt: $gray-300 !default;\n$gray-400-alt: $gray-400 !default;\n$gray-500-alt: $gray-500 !default;\n$gray-600-alt: $gray-600 !default;\n$gray-700-alt: $gray-700 !default;\n$gray-800-alt: $gray-800 !default;\n$gray-900-alt: $gray-900 !default;\n$black-alt:    $black !default;\n\n$grays-alt: () !default;\n$grays-alt: map-merge((\n  \"100\": $gray-100-alt,\n  \"200\": $gray-200-alt,\n  \"300\": $gray-300-alt,\n  \"400\": $gray-400-alt,\n  \"500\": $gray-500-alt,\n  \"600\": $gray-600-alt,\n  \"700\": $gray-700-alt,\n  \"800\": $gray-800-alt,\n  \"900\": $gray-900-alt\n), $grays-alt);\n\n// Below colors from bootwatch darkly\n$blue-alt:    #3f6791 !default;\n$indigo-alt:  #6610f2 !default;\n$purple-alt:  #6f42c1 !default;\n$pink-alt:    #e83e8c !default;\n$red-alt:     #e74c3c !default;\n$orange-alt:  #fd7e14 !default;\n$yellow-alt:  #f39c12 !default;\n$green-alt:   #00bc8c !default;\n$teal-alt:    #20c997 !default;\n$cyan-alt:    #3498db !default;\n\n// by darken function\n$lightblue-alt: lighten(#3c8dbc, 20%) !default;\n$navy-alt: lighten(#001f3f, 5%) !default;\n$olive-alt: lighten(#3d9970, 20%) !default;\n$lime-alt: lighten(#01ff70, 20%) !default;\n$fuchsia-alt: lighten(#f012be, 20%) !default;\n$maroon-alt: lighten(#d81b60, 20%) !default;\n$gray-x-light-alt: lighten(#d2d6de, 20%) !default;\n\n$colors-alt: () !default;\n$colors-alt: map-merge((\n  \"blue\":       $blue-alt,\n  \"indigo\":     $indigo-alt,\n  \"purple\":     $purple-alt,\n  \"pink\":       $pink-alt,\n  \"red\":        $red-alt,\n  \"orange\":     $orange-alt,\n  \"yellow\":     $yellow-alt,\n  \"green\":      $green-alt,\n  \"teal\":       $teal-alt,\n  \"cyan\":       $cyan-alt,\n  \"white\":      $white-alt,\n  \"gray\":       $gray-600-alt,\n  \"gray-dark\":  $gray-800-alt\n), $colors-alt);\n\n$primary-alt:       $blue-alt !default;\n$secondary-alt:     $gray-600-alt !default;\n$success-alt:       $green-alt !default;\n$info-alt:          $cyan-alt !default;\n$warning-alt:       $yellow-alt !default;\n$danger-alt:        $red-alt !default;\n$light-alt:         $gray-100-alt !default;\n$dark-alt:          $gray-800-alt !default;\n\n$theme-colors-alt: () !default;\n$theme-colors-alt: map-merge((\n  \"primary\":    $primary-alt,\n  \"secondary\":  $secondary-alt,\n  \"success\":    $success-alt,\n  \"info\":       $info-alt,\n  \"warning\":    $warning-alt,\n  \"danger\":     $danger-alt,\n  \"light\":      $light-alt,\n  \"dark\":       $dark-alt\n), $theme-colors-alt);\n\n$colors-alt: map-merge(\n  (\n    \"lightblue\": $lightblue-alt,\n    \"navy\": $navy-alt,\n    \"olive\": $olive-alt,\n    \"lime\": $lime-alt,\n    \"fuchsia\": $fuchsia-alt,\n    \"maroon\": $maroon-alt,\n  ),\n  $colors-alt\n);\n// stylelint-enable\n\n//\n", "//\n// Plugin: Bootstrap Slider\n//\n\n// Tooltip fix\n.slider .tooltip.in {\n  opacity: $tooltip-opacity;\n}\n\n// Style override\n.slider {\n  &.slider-vertical {\n    height: 100%;\n  }\n  &.slider-horizontal {\n    width: 100%;\n  }\n}\n\n// Colors\n@each $name, $color in $theme-colors {\n  .slider-#{$name} .slider {\n    .slider-selection {\n      background: $color;\n    }\n  }\n}\n\n@each $name, $color in $colors {\n  .slider-#{$name} .slider {\n    .slider-selection {\n      background: $color;\n    }\n  }\n}\n\n@include dark-mode () {\n  .slider-track {\n    background-color: lighten($dark, 10%);\n    background-image: none;\n  }\n\n  @each $name, $color in $theme-colors-alt {\n    .slider-#{$name} .slider {\n      .slider-selection {\n        background: $color;\n      }\n    }\n  }\n\n  @each $name, $color in $colors-alt {\n    .slider-#{$name} .slider {\n      .slider-selection {\n        background: $color;\n      }\n    }\n  }\n}\n", "//\n// Plugin: iCheck Bootstrap\n//\n\n// iCheck colors (theme colors)\n@each $name, $color in $theme-colors {\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:checked + label::before,\n  .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n    background-color: #{$color};\n    border-color: #{$color};\n  }\n}\n\n// iCheck colors (colors)\n@each $name, $color in $colors {\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:checked + label::before,\n  .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n    background-color: #{$color};\n    border-color: #{$color};\n  }\n}\n\n@include dark-mode () {\n  [class*=\"icheck-\"] > input:first-child:not(:checked) {\n    + input[type=\"hidden\"] + label::before,\n    + label::before {\n      border-color: $gray-600;\n    }\n  }\n  // iCheck colors (theme colors)\n  @each $name, $color in $theme-colors-alt {\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n      border-color: #{$color};\n    }\n\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n      border-color: #{$color};\n    }\n\n    .icheck-#{$name} > input:first-child:checked + label::before,\n    .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n      background-color: #{$color};\n      border-color: #{$color};\n    }\n  }\n\n  // iCheck colors (colors)\n  @each $name, $color in $colors-alt {\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n      border-color: #{$color};\n    }\n\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n      border-color: #{$color};\n    }\n\n    .icheck-#{$name} > input:first-child:checked + label::before,\n    .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n      background-color: #{$color};\n      border-color: #{$color};\n    }\n  }\n}\n", "//\n// Plugins: jQ<PERSON>y <PERSON>\n//\n\n.mapael {\n  .map {\n    position: relative;\n  }\n\n  .mapTooltip {\n    @include reset-text();\n    @include border-radius($tooltip-border-radius);\n    @include font-size($tooltip-font-size);\n    background-color: $tooltip-bg;\n    color: $tooltip-color;\n    display: block;\n    max-width: $tooltip-max-width;\n    padding: $tooltip-padding-y $tooltip-padding-x;\n    position: absolute;\n    text-align: center;\n    word-wrap: break-word;\n    z-index: $zindex-tooltip;\n  }\n\n  .myLegend {\n    background-color: $gray-100;\n    border: 1px solid $gray-500;\n    padding: 10px;\n    width: 600px;\n  }\n\n  .zoomButton {\n    background-color: $button-default-background-color;\n    border: 1px solid $button-default-border-color;\n    border-radius: $btn-border-radius;\n    color: $button-default-color;\n    cursor: pointer;\n    font-weight: 700;\n    height: 16px;\n    left: 10px;\n    line-height: 14px;\n    padding-left: 1px;\n    position: absolute;\n    text-align: center;\n    top: 0;\n\n    user-select: none;\n    width: 16px;\n\n    &:hover,\n    &:active,\n    &.hover {\n      background-color: darken($button-default-background-color, 5%);\n      color: darken($button-default-color, 10%);\n    }\n  }\n\n  .zoomReset {\n    line-height: 12px;\n    top: 10px;\n  }\n\n  .zoomIn {\n    top: 30px;\n  }\n\n  .zoomOut {\n    top: 50px;\n  }\n}\n", "@mixin reset-text() {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated responsive font sizes\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/v8.x/LICENSE)\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n@if $rfs-font-size-unit != rem and $rfs-font-size-unit != px {\n  @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize font size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: divide($rfs-base-font-size, $rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: divide($rfs-base-font-size, divide($rfs-base-font-size * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-disable-class {\n  @if $rfs-class == \"disable\" {\n    // Adding an extra class increases specificity, which prevents the media query to override the font size\n    &,\n    .disable-responsive-font-size &,\n    &.disable-responsive-font-size {\n      @content;\n    }\n  }\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-enable-class {\n  @if $rfs-class == \"enable\" {\n    .enable-responsive-font-size &,\n    &.enable-responsive-font-size {\n      @content;\n    }\n  }\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query($mq-value) {\n  @if $rfs-two-dimensional {\n    @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n      @content;\n    }\n  }\n  @else {\n    @media (max-width: #{$mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Responsive font size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Remove unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: divide($fs, $fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: divide($fs, divide($fs * 0 + 1, $rfs-rem-value));\n    }\n\n    // Set default font size\n    $rfs-static: if($rfs-font-size-unit == rem, #{divide($fs, $rfs-rem-value)}rem, #{$fs}px);\n\n    // Only add the media query if the font size is bigger than the minimum font size\n    @if $fs <= $rfs-base-font-size or not $enable-responsive-font-sizes {\n      font-size: #{$rfs-static}#{$rfs-suffix};\n    }\n    @else {\n      // Calculate the minimum font size for $fs\n      $fs-min: $rfs-base-font-size + divide($fs - $rfs-base-font-size, $rfs-factor);\n\n      // Calculate difference between $fs and the minimum font size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      $min-width: if($rfs-font-size-unit == rem, #{divide($fs-min, $rfs-rem-value)}rem, #{$fs-min}px);\n\n      // Use `vmin` if two-dimensional is enabled\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{divide($fs-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n      // Set the calculated font-size\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n\n      // Breakpoint formatting\n      $mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n\n      @include _rfs-disable-class {\n        font-size: #{$rfs-static}#{$rfs-suffix};\n      }\n\n      @include _rfs-media-query($mq-value) {\n        @include _rfs-enable-class {\n          font-size: $rfs-fluid;\n        }\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixins use RFS to rescale the font size\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "//\n// Plugins: JQVMap\n//\n\n// Zoom Button size fixes\n.jqvmap-zoomin,\n.jqvmap-zoomout {\n  background-color: $button-default-background-color;\n  border: 1px solid $button-default-border-color;\n  border-radius: $btn-border-radius;\n  color: $button-default-color;\n  height: 15px;\n  width: 15px;\n  padding: 1px 2px;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: darken($button-default-background-color, 5%);\n    color: darken($button-default-color, 10%);\n  }\n}\n", "//\n// Plugin: SweetAlert2\n//\n\n// Icon Colors\n.swal2-icon {\n  &.swal2-info {\n    border-color: ligthen($info, 20%);\n    color: $info;\n  }\n\n  &.swal2-warning {\n    border-color: ligthen($warning, 20%);\n    color: $warning;\n  }\n\n  &.swal2-error {\n    border-color: ligthen($danger, 20%);\n    color: $danger;\n  }\n\n  &.swal2-question {\n    border-color: ligthen($secondary, 20%);\n    color: $secondary;\n  }\n\n  &.swal2-success {\n    border-color: ligthen($success, 20%);\n    color: $success;\n\n    .swal2-success-ring {\n      border-color: ligthen($success, 20%);\n    }\n\n    [class^='swal2-success-line'] {\n      background-color: $success;\n    }\n  }\n}\n\n@include dark-mode () {\n  .swal2-popup {\n    background-color: $dark;\n    color: $gray-200;\n\n    .swal2-content,\n    .swal2-title {\n      color: $gray-200;\n    }\n  }\n}\n", "//\n// Plugin: Toastr\n//\n\n// Background to FontAwesome Icons\n// #toast-container > .toast {\n//     background-image: none !important;\n// }\n// #toast-container > .toast .toast-message:before {\n//     font-family: 'Font Awesome 5 Free';\n//     font-size: 24px;\n//     font-weight: 900;\n//     line-height: 18px;\n//     float: left;\n//     color: $white;\n//     padding-right: 0.5em;\n//     margin: auto 0.5em auto -1.5em;\n// }\n// #toast-container > .toast-warning .toast-message:before {\n//     content: \"\\f06a\";\n// }\n// #toast-container > .toast-error .toast-message:before {\n//     content: \"\\f071\";\n// }\n// #toast-container > .toast-info .toast-message:before {\n//     content: \"\\f05a\";\n// }\n// #toast-container > .toast-success .toast-message:before {\n//     content: \"\\f058\";\n// }\n\n\n#toast-container {\n  // Background color\n  .toast {\n    background-color: $primary;\n  }\n\n  .toast-success {\n    background-color: $success;\n  }\n\n  .toast-error {\n    background-color: $danger;\n  }\n\n  .toast-info {\n    background-color: $info;\n  }\n\n  .toast-warning {\n    background-color: $warning;\n  }\n}\n\n// full width fix\n.toast-bottom-full-width .toast,\n.toast-top-full-width .toast {\n  max-width: inherit;\n}\n", "//\n// Plugin: Pace\n//\n\n.pace {\n  z-index: $zindex-main-sidebar + 10;\n\n  .pace-progress {\n    z-index: $zindex-main-sidebar + 11;\n  }\n\n  .pace-activity {\n    z-index: $zindex-main-sidebar + 12;\n  }\n}\n\n// Mixin\n@mixin pace-variant($name, $color) {\n  .pace-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-barber-shop-#{$name} {\n    .pace {\n      background: color-yiq($color);\n\n      .pace-progress {\n        background: $color;\n      }\n\n      .pace-activity {\n        background-image: linear-gradient(45deg, rgba(color-yiq($color), 0.2) 25%, transparent 25%, transparent 50%, rgba(color-yiq($color), 0.2) 50%, rgba(color-yiq($color), 0.2) 75%, transparent 75%, transparent);\n      }\n    }\n  }\n\n  .pace-big-counter-#{$name} {\n    .pace {\n      .pace-progress::after {\n        color: rgba($color, .19999999999999996);\n      }\n    }\n  }\n\n  .pace-bounce-#{$name} {\n    .pace {\n      .pace-activity {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-center-atom-#{$name} {\n    .pace-progress {\n      height: 100px;\n      width: 80px;\n\n      &::before {\n        background: $color;\n        color: color-yiq($color);\n        font-size: .8rem;\n        line-height: .7rem;\n        padding-top: 17%;\n      }\n    }\n\n    .pace-activity {\n      border-color: $color;\n\n      &::after,\n      &::before {\n        border-color: $color;\n      }\n    }\n  }\n\n  .pace-center-circle-#{$name} {\n    .pace {\n      .pace-progress {\n        background: rgba($color, .8);\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .pace-center-radar-#{$name} {\n    .pace {\n      .pace-activity {\n        border-color: $color transparent transparent;\n      }\n\n      .pace-activity::before {\n        border-color: $color transparent transparent;\n      }\n    }\n  }\n\n  .pace-center-simple-#{$name} {\n    .pace {\n      background: color-yiq($color);\n      border-color: $color;\n\n      .pace-progress {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-material-#{$name} {\n    .pace {\n      color: $color;\n    }\n  }\n\n  .pace-corner-indicator-#{$name} {\n    .pace {\n      .pace-activity {\n        background: $color;\n      }\n\n      .pace-activity::after,\n      .pace-activity::before {\n        border: 5px solid color-yiq($color);\n      }\n\n\n      .pace-activity::before {\n          border-right-color: rgba($color, .2);\n          border-left-color: rgba($color, .2);\n      }\n\n      .pace-activity::after {\n          border-top-color: rgba($color, .2);\n          border-bottom-color: rgba($color, .2);\n      }\n    }\n  }\n\n  .pace-fill-left-#{$name} {\n    .pace {\n      .pace-progress {\n        background-color: rgba($color, 0.19999999999999996);\n      }\n    }\n  }\n\n  .pace-flash-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n      }\n\n      .pace-progress-inner {\n        box-shadow: 0 0 10px $color, 0 0 5px $color;\n      }\n\n      .pace-activity {\n        border-top-color: $color;\n        border-left-color: $color;\n      }\n    }\n  }\n\n  .pace-loading-bar-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n        color: $color;\n        box-shadow: 120px 0 color-yiq($color), 240px 0 color-yiq($color);\n      }\n\n      .pace-activity {\n        box-shadow: inset 0 0 0 2px $color, inset 0 0 0 7px color-yiq($color);\n      }\n    }\n  }\n\n  .pace-mac-osx-#{$name} {\n    .pace {\n      .pace-progress {\n        background-color: $color;\n        box-shadow: inset -1px 0 $color, inset 0 -1px $color, inset 0 2px rgba(color-yiq($color), 0.5), inset 0 6px rgba(color-yiq($color), .3);\n      }\n\n      .pace-activity {\n        background-image: radial-gradient(rgba(color-yiq($color), .65) 0%, rgba(color-yiq($color), .15) 100%);\n        height: 12px;\n      }\n    }\n  }\n\n  .pace-progress-color-#{$name} {\n    .pace-progress {\n      color: $color;\n    }\n  }\n}\n\n\n@each $name, $color in $theme-colors {\n  @include pace-variant($name, $color);\n}\n\n@each $name, $color in $colors {\n  @include pace-variant($name, $color);\n}\n\n", "/**\n  * bootstrap-switch - Turn checkboxes and radio buttons into toggle switches.\n  *\n  * @version v3.4 (MODDED)\n  * @homepage https://bttstrp.github.io/bootstrap-switch\n  * <AUTHOR> <<EMAIL>> (http://larentis.eu)\n  * @license MIT\n  */\n\n$bootstrap-switch-border-radius: $btn-border-radius;\n$bootstrap-switch-handle-border-radius: .1rem;\n\n.bootstrap-switch {\n  border: $input-border-width solid $input-border-color;\n  border-radius: $bootstrap-switch-border-radius;\n  cursor: pointer;\n  direction: ltr;\n  display: inline-block;\n  line-height: .5rem;\n  overflow: hidden;\n  position: relative;\n  text-align: left;\n  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;\n  user-select: none;\n  vertical-align: middle;\n  z-index: 0;\n\n  .bootstrap-switch-container {\n    border-radius: $bootstrap-switch-border-radius;\n    display: inline-block;\n    top: 0;\n    transform: translate3d(0, 0, 0);\n\n  }\n\n  &:focus-within {\n    box-shadow: $input-btn-focus-box-shadow;\n  }\n\n  .bootstrap-switch-handle-on,\n  .bootstrap-switch-handle-off,\n  .bootstrap-switch-label {\n    box-sizing: border-box;\n    cursor: pointer;\n    display: table-cell;\n    font-size: 1rem;\n    font-weight: 500;\n    line-height: 1.2rem;\n    padding: .25rem .5rem;\n    vertical-align: middle;\n  }\n\n  .bootstrap-switch-handle-on,\n  .bootstrap-switch-handle-off {\n    text-align: center;\n    z-index: 1;\n\n    &.bootstrap-switch-default {\n      background: $gray-200;\n      color: color-yiq($gray-200);\n    }\n\n    @each $name, $color in $theme-colors {\n      &.bootstrap-switch-#{$name} {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n\n    @each $name, $color in $colors {\n      &.bootstrap-switch-#{$name} {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .bootstrap-switch-handle-on {\n    border-bottom-left-radius: $bootstrap-switch-handle-border-radius;\n    border-top-left-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  .bootstrap-switch-handle-off {\n    border-bottom-right-radius: $bootstrap-switch-handle-border-radius;\n    border-top-right-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  input[type='radio'],\n  input[type='checkbox'] {\n    filter: alpha(opacity=0);\n    left: 0;\n    margin: 0;\n    opacity: 0;\n    position: absolute;\n    top: 0;\n    visibility: hidden;\n    z-index: -1;\n  }\n\n  &.bootstrap-switch-mini {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: .875rem;\n      line-height: 1.5;\n      padding: .1rem .3rem;\n    }\n  }\n\n  &.bootstrap-switch-small {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: .875rem;\n      line-height: 1.5;\n      padding: .2rem .4rem;\n    }\n  }\n\n  &.bootstrap-switch-large {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: 1.25rem;\n      line-height: 1.3333333rem;\n      padding: .3rem .5rem;\n    }\n  }\n\n  &.bootstrap-switch-disabled,\n  &.bootstrap-switch-readonly,\n  &.bootstrap-switch-indeterminate {\n    cursor: default;\n\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      cursor: default;\n      filter: alpha(opacity=50);\n      opacity: .5;\n    }\n  }\n\n  &.bootstrap-switch-animate .bootstrap-switch-container {\n    transition: margin-left .5s;\n  }\n\n  &.bootstrap-switch-inverse {\n    .bootstrap-switch-handle-on {\n      border-radius: 0 $bootstrap-switch-handle-border-radius $bootstrap-switch-handle-border-radius 0;\n    }\n\n    .bootstrap-switch-handle-off {\n      border-radius: $bootstrap-switch-handle-border-radius 0 0 $bootstrap-switch-handle-border-radius;\n    }\n  }\n\n  // &.bootstrap-switch-focused {\n  //   border-color: $input-btn-focus-color;\n  //   box-shadow: $input-btn-focus-box-shadow;\n  //   outline: 0;\n  // }\n\n  &.bootstrap-switch-on .bootstrap-switch-label,\n  &.bootstrap-switch-inverse.bootstrap-switch-off .bootstrap-switch-label {\n    border-bottom-right-radius: $bootstrap-switch-handle-border-radius;\n    border-top-right-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  &.bootstrap-switch-off .bootstrap-switch-label,\n  &.bootstrap-switch-inverse.bootstrap-switch-on .bootstrap-switch-label {\n    border-bottom-left-radius: $bootstrap-switch-handle-border-radius;\n    border-top-left-radius: $bootstrap-switch-handle-border-radius;\n  }\n}\n\n@include dark-mode () {\n  .bootstrap-switch {\n    border-color: $gray-600;\n\n    .bootstrap-switch-handle-off.bootstrap-switch-default,\n    .bootstrap-switch-handle-on.bootstrap-switch-default {\n      background-color: lighten($dark, 2.5%);\n      color: $white;\n      border-color: lighten($dark, 7.5%);\n    }\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off {\n      @each $name, $color in $theme-colors-alt {\n        &.bootstrap-switch-#{$name} {\n          background: $color;\n          color: color-yiq($color);\n        }\n      }\n\n      @each $name, $color in $colors-alt {\n        &.bootstrap-switch-#{$name} {\n          background: $color;\n          color: color-yiq($color);\n        }\n      }\n    }\n  }\n}\n", "@include dark-mode() {\n  .daterangepicker {\n    background-color: lighten($dark, 5%);\n    border: inherit;\n\n    &::before,\n    &::after {\n      border-bottom-color: lighten($dark, 5%);\n    }\n\n    td.available:hover,\n    th.available:hover {\n      background-color: lighten($dark, 5%);\n    }\n    td.in-range {\n      background-color: lighten($dark, 10%);\n      color: $white;\n    }\n\n    td.off,\n    td.off.in-range,\n    td.off.start-date,\n    td.off.end-date {\n      background-color: darken($dark, 5%);\n      color: $white;\n    }\n\n    .ranges li:hover {\n      background-color: $dark;\n    }\n\n    &.show-ranges.ltr .drp-calendar {\n      border-color: lighten($dark, 10%);\n\n      &.left,\n      &.right {\n        border-color: lighten($dark, 10%);\n        padding-top: 0;\n      }\n    }\n\n    .drp-buttons {\n      border-color: lighten($dark, 10%);\n    }\n\n    .calendar-table {\n      background-color: $dark;\n      border-color: lighten($dark, 10%);\n\n      th,\n      td {\n        color: $white;\n      }\n\n      .next span,\n      .prev span {\n        border-color: $white;\n      }\n    }\n\n    select.hourselect,\n    select.minuteselect,\n    select.secondselect,\n    select.ampmselect {\n      background-color: $dark;\n      border-color: lighten($dark, 10%);\n    }\n  }\n}\n", "//\n// Plugins: Miscellaneous\n// Old plugin codes\n//\n\n// _fix for sparkline tooltip\n.jqstooltip {\n  height: auto !important;\n  padding: 5px !important;\n  width: auto !important;\n}\n\n// jQueryUI\n.connectedSortable {\n  min-height: 100px;\n}\n\n.ui-helper-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sort-highlight {\n  background: $gray-100;\n  border: 1px dashed $gray-300;\n  margin-bottom: 10px;\n}\n\n// Charts\n.chart {\n  overflow: hidden;\n  position: relative;\n}\n\n\n@include dark-mode () {\n  .irs--flat .irs-line {\n    background-color: lighten($dark, 10%);\n  }\n  .jsgrid-edit-row > .jsgrid-cell,\n  .jsgrid-filter-row > .jsgrid-cell,\n  .jsgrid-grid-body, .jsgrid-grid-header,\n  .jsgrid-header-row > .jsgrid-header-cell,\n  .jsgrid-insert-row > .jsgrid-cell,\n  .jsgrid-row > .jsgrid-cell,\n  .jsgrid-alt-row > .jsgrid-cell {\n    border-color: $gray-600;\n  }\n  .jsgrid-header-row > .jsgrid-header-cell,\n  .jsgrid-row > .jsgrid-cell {\n    background-color: $dark;\n  }\n  .jsgrid-alt-row > .jsgrid-cell {\n    background-color: lighten($dark, 2.5%);\n  }\n  .jsgrid-selected-row > .jsgrid-cell {\n    background-color: lighten($dark, 5%);\n  }\n}\n"]}