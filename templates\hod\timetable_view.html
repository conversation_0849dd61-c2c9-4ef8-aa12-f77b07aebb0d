{% extends "base.html" %}
{% block title %}View Timetable | College ERP{% endblock %}
{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}
{% block navbar %}{% include "partials/navbar.html" %}{% endblock %}
{% block sidebar %}{% include "partials/sidebar_hod.html" %}{% endblock %}

{% block content %}
<!-- Page Header -->
{% with page_title="View Timetable" icon_class="fas fa-calendar" show_actions=False %}
  {% include "partials/page_header.html" %}
{% endwith %}

<!-- Filters -->
<div class="row">
  <div class="col-md-12">
    <div class="card card-primary card-outline shadow-sm">
      <div class="card-header">
        <h3 class="card-title font-weight-bold">
          <i class="fas fa-filter mr-2"></i>Filter Timetable
        </h3>
      </div>
      <div class="card-body">
        <form method="GET" id="filterForm">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="branch">Branch</label>
                <select name="branch" id="branch" class="form-control">
                  <option value="">Select Branch</option>
                  {% for branch in hod_branches %}
                    <option value="{{ branch.id }}" {% if selected_branch and selected_branch.id == branch.id %}selected{% endif %}>
                      {{ branch.name }} ({{ branch.code }})
                    </option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="semester">Semester</label>
                <select name="semester" id="semester" class="form-control">
                  <option value="">Select Semester</option>
                  {% for semester in semesters %}
                    <option value="{{ semester.id }}" {% if selected_semester and selected_semester.id == semester.id %}selected{% endif %}>
                      Semester {{ semester.number }}
                    </option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="section">Section</label>
                <select name="section" id="section" class="form-control">
                  <option value="">Select Section</option>
                  {% for section in sections %}
                    <option value="{{ section.id }}" {% if selected_section and selected_section.id == section.id %}selected{% endif %}>
                      {{ section.name }}
                    </option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-search mr-1"></i> View Timetable
              </button>
              <a href="{% url 'hod_timetable_view' %}" class="btn btn-secondary ml-2">
                <i class="fas fa-times mr-1"></i> Clear Filters
              </a>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Timetable Display -->
{% if batch_timetables %}
<div class="row">
  <div class="col-md-12">
    <div class="card card-primary card-outline shadow-sm">
      <div class="card-body">
        <div class="container">
          {% for batch_name, timetable in batch_timetables.items %}
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h2 class="font-weight-bold text-primary mb-0">{{ batch_name }} Timetable</h2>
              <button id="downloadPDF" class="btn btn-danger">
                <i class="fas fa-file-pdf mr-1"></i> Download PDF
              </button>
            </div>
            <div class="table-responsive" id="timetableContainer">
              <table class="table table-bordered mb-0" style="border-collapse: collapse; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); border-radius: 10px; overflow: hidden;">
                <thead>
                  <tr>
                    <th style="background-color: #34495e; color: white; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px; padding: 12px; text-align: center;">Day / Time</th>
                    {% for slot in time_slots %}
                      <th style="background-color: #34495e; color: white; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px; padding: 12px; text-align: center;">{{ slot.slot }}</th>
                    {% endfor %}
                  </tr>
                </thead>
                <tbody>
                  {% for day, slots in timetable.items %}
                    <tr>
                      <td style="background-color: #34495e; color: white; font-weight: 600; padding: 12px; text-align: center;"><strong>{{ day }}</strong></td>
                      {% for slot, details in slots.items %}
                        <td style="background-color: #fff; padding: 12px; text-align: center; vertical-align: middle;">
                            <strong style="color: #2c3e50; font-weight: 600;">{{ details.subject }}</strong><br>
                            <small style="color: #7f8c8d; font-size: 0.9em;">{{ details.faculty|default:"-" }}</small>
                        </td>
                      {% endfor %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            <br><br>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
  .table th, .table td {
    vertical-align: middle;
  }

  /* Additional styling for the grid timetable */
  .timetable-grid {
    font-family: 'Arial', sans-serif;
    background-color: #f0f4f8;
    color: #333;
  }

  .timetable-grid table {
    width: 100%;
    border-collapse: collapse;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
  }

  .timetable-grid th, .timetable-grid td {
    border: 1px solid #ddd;
    padding: 12px;
    vertical-align: middle;
  }

  .timetable-grid th {
    background-color: #34495e;
    color: white;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .timetable-grid td {
    background-color: #fff;
  }

  /* Download button styling */
  #downloadPDF {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 5px;
    transition: all 0.3s ease;
  }

  #downloadPDF:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }
</style>
{% endblock %}

{% block extra_js %}
<!-- Include html2pdf library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

<script>
  $(document).ready(function() {
    // PDF Download functionality
    $('#downloadPDF').click(function() {
      // Show loading state
      const button = $(this);
      const originalText = button.html();
      button.html('<i class="fas fa-spinner fa-spin mr-1"></i> Generating PDF...');
      button.prop('disabled', true);

      // Get the timetable container
      const element = document.getElementById('timetableContainer');

      // PDF options
      const opt = {
        margin: [0.5, 0.5, 0.5, 0.5],
        filename: '{{ branch.code|lower }}-semester-{{ semester.number }}-section-{{ section.name|lower }}-timetable.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          letterRendering: true
        },
        jsPDF: {
          unit: 'in',
          format: 'a4',
          orientation: 'landscape'
        }
      };

      // Generate PDF
      html2pdf().set(opt).from(element).save().then(function() {
        // Reset button state
        button.html(originalText);
        button.prop('disabled', false);
      }).catch(function(error) {
        console.error('PDF generation failed:', error);
        button.html(originalText);
        button.prop('disabled', false);
        alert('Failed to generate PDF. Please try again.');
      });
    });

    // Cascading dropdowns functionality
    $('#branch').change(function() {
      const branchId = $(this).val();
      $('#semester').empty().append('<option value="">Select Semester</option>');
      $('#section').empty().append('<option value="">Select Section</option>');

      if (branchId) {
        $.ajax({
          url: '{% url "ajax_get_timetable_semesters" %}',
          type: 'GET',
          data: {
            'branch_id': branchId
          },
          success: function(data) {
            if (data.semesters) {
              $.each(data.semesters, function(index, semester) {
                $('#semester').append('<option value="' + semester.id + '">Semester ' + semester.number + '</option>');
              });
            }
          },
          error: function() {
            console.error('Error loading semesters');
          }
        });
      }
    });

    $('#semester').change(function() {
      const semesterId = $(this).val();
      const branchId = $('#branch').val();
      $('#section').empty().append('<option value="">Select Section</option>');

      if (semesterId && branchId) {
        // Get semester number from the selected option text
        const semesterText = $('#semester option:selected').text();
        const semesterNumber = semesterText.replace('Semester ', '');

        $.ajax({
          url: '{% url "ajax_get_timetable_sections" %}',
          type: 'GET',
          data: {
            'semester_number': semesterNumber,
            'branch_id': branchId
          },
          success: function(data) {
            if (data.sections) {
              $.each(data.sections, function(index, section) {
                $('#section').append('<option value="' + section.id + '">' + section.name + '</option>');
              });
            }
          },
          error: function() {
            console.error('Error loading sections');
          }
        });
      }
    });
  });
</script>
{% endblock %}
