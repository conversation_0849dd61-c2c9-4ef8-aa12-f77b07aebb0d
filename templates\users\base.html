{% load static i18n %}
<!DOCTYPE html>
{% get_current_language as LANGUAGE_CODE %}
<html lang="{{ LANGUAGE_CODE }}">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" href="{% static 'logo/logo.png' %}">
  <meta name="description" content="College ERP System">

  <title>{% block title %}College ERP{% endblock %}</title>

  <!-- Google Fonts & Font Awesome (CDN for faster load) -->
  <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

  <!-- Bootstrap 4 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.2/css/bootstrap.min.css">

  <!-- AdminLTE -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">

  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

  {% block extra_css %}{% endblock %}
  {% block head_scripts %}{% endblock %}
</head>

<body class="{% block body_class %}hold-transition{% endblock %}">

  <!-- Preloader -->
  <div class="preloader flex-column justify-content-center align-items-center">
    <img class="animation__shake" src="{% static 'logo/logo.png' %}" alt="College ERP Logo" height="60" width="60">
    <div class="spinner-border mt-3" role="status">
      <span class="sr-only">{% trans "Loading..." %}</span>
    </div>
  </div>

  {% block content %} {% endblock %}

  <!-- Scripts -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.2/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>

  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>

  <!-- Custom Initialization -->
  <script>
    // Preloader removal after content load
    $(window).on('load', function() {
      $('.preloader').fadeOut('slow');
    });

    // Convert Flash Messages to SweetAlert2 Toast
    {% if messages %}
      {% for message in messages %}
        Swal.fire({
          icon: '{{ message.tags }}' === 'danger' ? 'error' : '{{ message.tags }}',
          text: '{{ message }}',
          showConfirmButton: false,
          timer: 3000,
          toast: true,
          position: 'top-end',
          background: {% if message.tags == 'success' %}'#d4edda'{% elif message.tags == 'danger' %}'#f8d7da'{% else %}'#d1ecf1'{% endif %},
          iconColor: {% if message.tags == 'success' %}'#28a745'{% elif message.tags == 'danger' %}'#dc3545'{% else %}'#17a2b8'{% endif %}
        });
      {% endfor %}
    {% endif %}
 </script>

  {% block extra_js %}{% endblock %}
  {% block footer_scripts %}{% endblock %}
</body>
</html>
