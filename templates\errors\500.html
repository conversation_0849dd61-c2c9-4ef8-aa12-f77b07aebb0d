{% extends "users/base.html" %}
{% load i18n static %}

{% block title %}{{ error_code }} - {{ page_title }} | NITRA-CMS{% endblock %}
{% block body_class %} hold-transition login-page{% endblock %}

{% block content %}
<div class="login-box">
  <div class="text-center mb-4">
    <img src="{% static 'logo/logo.png' %}" alt="NITRA Technical Campus Logo" class="img-fluid" style="max-height: 120px;">
  </div>
  
  <div class="login-logo">
    <a href="{% url 'login_page' %}"><b>NITRA</b> Technical Campus</a>
  </div>

  <div class="card">
    <div class="card-body login-card-body text-center">
      <i class="fas fa-server text-danger fa-4x mb-3"></i>

      <h1 class="mb-3 text-danger">{{ error_code }}</h1>
      <h3 class="mb-3">{{ page_title }}</h3>

      <p class="mb-4 text-muted">
        {{ error_message }}
      </p>

      <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle"></i>
        {% trans "We're experiencing technical difficulties. Our team has been notified and is working to resolve this issue." %}
      </div>

      <div class="row">
        <div class="col-6">
          <a href="javascript:history.back()" class="btn btn-secondary btn-block">
            <i class="fas fa-arrow-left"></i> {% trans "Go Back" %}
          </a>
        </div>
        <div class="col-6">
          <a href="{% url 'login_page' %}" class="btn btn-primary btn-block">
            <i class="fas fa-home"></i> {% trans "Home" %}
          </a>
        </div>
      </div>

      <div class="mt-3">
        <a href="mailto:<EMAIL>" class="btn btn-outline-danger">
          <i class="fas fa-envelope"></i> {% trans "Contact Support" %}
        </a>
      </div>

      <div class="mt-3">
        <small class="text-muted">
          {% trans "Please try again in a few minutes. If the problem persists, contact our technical support team." %}
        </small>
      </div>
    </div>
  </div>

  <div class="text-center mt-3">
    <small class="text-muted">
      &copy; {% now "Y" %} NITRA Technical Campus, Ghaziabad. All rights reserved.
    </small>
  </div>
</div>
{% endblock %}
