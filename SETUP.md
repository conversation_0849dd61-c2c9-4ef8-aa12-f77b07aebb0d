# NITRA-CMS Setup Guide

This guide provides detailed step-by-step instructions for setting up the NITRA-CMS (College Management System) on your local machine.

## 📋 Prerequisites

Before starting, ensure you have the following installed on your system:

### Required Software
- **Python 3.8 or higher** - [Download Python](https://www.python.org/downloads/)
- **Git** - [Download Git](https://git-scm.com/downloads/)
- **pip** (usually comes with Python)

### Optional (for Production)
- **PostgreSQL** - [Download PostgreSQL](https://www.postgresql.org/download/)
- **Redis** - [Download Redis](https://redis.io/download)

### Verify Installation
```bash
python --version    # Should show Python 3.8+
pip --version       # Should show pip version
git --version       # Should show git version
```

## 🚀 Quick Setup (Development)

### Step 1: Clone the Repository
```bash
git clone <repository-url>
cd NITRA-CMS
```

### Step 2: Create Virtual Environment
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python -m venv venv
source venv/bin/activate
```

### Step 3: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 4: Environment Configuration
Create a `.env` file in the project root directory:

```env
# Core Django Settings
SECRET_KEY=django-insecure-your-secret-key-here-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (Optional - defaults to SQLite)
# DATABASE_URL=postgres://user:password@localhost:5432/nitra_cms

# Email Configuration (Required for user registration)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# External Services (Optional)
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key

# Production Settings (Optional)
# REDIS_URL=redis://127.0.0.1:6379/1
```

### Step 5: Database Setup
```bash
python manage.py makemigrations
python manage.py migrate
```

### Step 6: Load Test Data
```bash
python manage.py add_dummy_data
```

### Step 7: Run the Server
```bash
python manage.py runserver
```

Visit `http://127.0.0.1:8000` to access the application.

## 🔧 Detailed Setup Instructions

### Email Configuration Setup

#### For Gmail:
1. Enable 2-Factor Authentication on your Google account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use the generated password in `EMAIL_HOST_PASSWORD`

#### Example .env for Gmail:
```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-16-character-app-password
```

### Database Configuration

#### SQLite (Default - No setup required)
The project uses SQLite by default, which requires no additional setup.

#### PostgreSQL (Recommended for Production)
1. Install PostgreSQL
2. Create database and user:
```sql
CREATE DATABASE nitra_cms;
CREATE USER nitra_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE nitra_cms TO nitra_user;
```
3. Update .env file:
```env
DATABASE_URL=postgres://nitra_user:your_password@localhost:5432/nitra_cms
```

### Creating Superuser (Optional)
```bash
python manage.py createsuperuser
```
Follow the prompts to create an admin account.

## 🎯 Test Data Overview

The `add_dummy_data` command creates:

### Users & Profiles
- **1 Admin**: <EMAIL>
- **3 HODs**: <EMAIL>, <EMAIL>, <EMAIL>
- **5 Faculty**: <EMAIL>, <EMAIL>, etc.
- **200+ Students**: student1@nitra.<NAME_EMAIL>
- **1 Accountant**: <EMAIL>

**All passwords**: `Abhi@356#`

### Academic Structure
- **3 Branches**: CSE (Computer Science), ECE (Electronics), ME (Mechanical)
- **4 Years**: I, II, III, IV
- **8 Semesters**: I through VIII
- **Sections**: A and B for each valid combination
- **10 Subjects**: Mix of branch-specific and common subjects

### Sample Data
- Fee structures for all branch-semester combinations
- Sample fee payments with different verification statuses
- Attendance sessions and records
- Notifications from faculty and HODs
- Student feedback data
- Result types and sample results

## 🔍 Verification Steps

### 1. Check Server Status
- Visit `http://127.0.0.1:8000`
- You should see the login page with NITRA logo

### 2. Test Login
Try logging in with any of these accounts:
- **Student**: <EMAIL> / Abhi@356#
- **Faculty**: <EMAIL> / Abhi@356#
- **HOD**: <EMAIL> / Abhi@356#
- **Accountant**: <EMAIL> / Abhi@356#
- **Admin**: <EMAIL> / Abhi@356#

### 3. Check Admin Panel
- Visit `http://127.0.0.1:8000/admin`
- Login with admin credentials
- Verify all models are visible and populated

### 4. Test Key Features
- **Student Portal**: View attendance, fees, notifications
- **Faculty Portal**: Mark attendance, add results, upload materials
- **HOD Portal**: View reports, manage faculty, approve promotions
- **Accountant Portal**: Verify fee payments, generate receipts

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### 1. Virtual Environment Issues
```bash
# If activation fails on Windows
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Recreate virtual environment
rmdir /s venv  # Windows
rm -rf venv    # macOS/Linux
python -m venv venv
```

#### 2. Database Migration Errors
```bash
# Reset database completely
python manage.py flush
python manage.py migrate
python manage.py add_dummy_data

# Fix migration conflicts
python manage.py migrate --fake-initial
```

#### 3. Email Configuration Issues
- Ensure you're using Gmail App Password, not regular password
- Check that 2FA is enabled on your Google account
- Verify EMAIL_USE_TLS=True for Gmail
- Test email settings:
```bash
python manage.py shell
>>> from django.core.mail import send_mail
>>> send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])
```

#### 4. Static Files Issues
```bash
# Clear and recollect static files
python manage.py collectstatic --clear --noinput
```

#### 5. Permission Errors
- Ensure media directory is writable
- Check logs directory permissions
- For SQLite, ensure db.sqlite3 is writable

#### 6. Port Already in Use
```bash
# Use different port
python manage.py runserver 8001

# Or kill process using port 8000 (Windows)
netstat -ano | findstr :8000
taskkill /PID <PID> /F

# Or kill process using port 8000 (macOS/Linux)
lsof -ti:8000 | xargs kill -9
```

### Getting Help

If you encounter issues:
1. Check the `logs/` directory for error details
2. Ensure all prerequisites are installed correctly
3. Verify your .env file configuration
4. Try the database reset commands above
5. Create an issue in the repository with:
   - Your operating system
   - Python version
   - Complete error message
   - Steps you've already tried

## 📁 Important Files and Directories

After setup, your project structure should look like:
```
NITRA-CMS/
├── .env                    # Your environment configuration
├── db.sqlite3             # SQLite database (created after migration)
├── logs/                  # Application logs
├── media/                 # User uploaded files
│   ├── fee_proofs/       # Payment proof uploads
│   ├── fee_receipts/     # Generated PDF receipts
│   └── fee_qr/           # QR code images
├── static/               # Static files
├── templates/            # HTML templates
└── venv/                 # Virtual environment (created by you)
```

## 🎉 Next Steps

After successful setup:
1. Explore the different user roles and their features
2. Customize the college information in admin panel
3. Add real faculty and student data
4. Configure email templates
5. Set up production environment (see README.md)

## 🔧 Advanced Configuration

### Custom Domain Setup (Optional)
If you want to run on a custom domain:
```env
ALLOWED_HOSTS=yourdomain.local,127.0.0.1,localhost
```

Add to your hosts file:
```
# Windows: C:\Windows\System32\drivers\etc\hosts
# macOS/Linux: /etc/hosts
127.0.0.1 yourdomain.local
```

### Redis Setup (Optional - for Production)
1. Install Redis server
2. Start Redis service
3. Update .env:
```env
REDIS_URL=redis://127.0.0.1:6379/1
```

### SSL/HTTPS Setup (Production)
For production with SSL:
```env
DEBUG=False
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
```

## 📊 Performance Optimization

### Database Optimization
```bash
# Create database indexes (after loading data)
python manage.py dbshell
# Run appropriate CREATE INDEX commands for your database
```

### Static Files Optimization
```bash
# Compress static files for production
python manage.py collectstatic --noinput
```

## 🧪 Testing the Setup

### Run Built-in Tests
```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test users
python manage.py test fees
```

### Manual Testing Checklist
- [ ] Login with different user types
- [ ] Upload a file (fee proof, study material)
- [ ] Mark attendance as faculty
- [ ] View notifications as student
- [ ] Generate fee receipt as accountant
- [ ] Access admin panel
- [ ] Test email functionality (if configured)

## 🔄 Updating the System

### Pull Latest Changes
```bash
git pull origin main
pip install -r requirements.txt
python manage.py migrate
python manage.py collectstatic --noinput
```

### Backup Before Updates
```bash
# Backup database
cp db.sqlite3 db.sqlite3.backup

# Backup media files
cp -r media media_backup
```

## 🌐 Production Deployment

### Quick Production Setup
1. Set up a production server (Ubuntu/CentOS)
2. Install Python, PostgreSQL, Redis, Nginx
3. Clone repository and follow setup steps
4. Configure environment for production:
```env
DEBUG=False
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DATABASE_URL=postgres://user:password@localhost:5432/nitra_cms_prod
REDIS_URL=redis://127.0.0.1:6379/1
```
5. Use Gunicorn as WSGI server:
```bash
pip install gunicorn
gunicorn cms_project.wsgi:application --bind 0.0.0.0:8000
```
6. Configure Nginx as reverse proxy
7. Set up SSL certificates (Let's Encrypt recommended)

### Production Checklist
- [ ] DEBUG=False
- [ ] Strong SECRET_KEY
- [ ] PostgreSQL database
- [ ] Redis for caching
- [ ] Proper ALLOWED_HOSTS
- [ ] SSL certificates
- [ ] Regular backups
- [ ] Monitoring and logging
- [ ] Firewall configuration

## 📞 Support Contacts

### Documentation
- Main README: `README.md`
- Dummy Data Guide: `DUMMY_DATA_INSTRUCTIONS.md`
- This Setup Guide: `SETUP.md`

### Getting Help
- **Issues**: Create GitHub issue with detailed information
- **Email**: Contact development team
- **Logs**: Check `logs/` directory for error details

### Useful Commands Reference
```bash
# Development
python manage.py runserver
python manage.py shell
python manage.py dbshell

# Database
python manage.py makemigrations
python manage.py migrate
python manage.py flush

# Data Management
python manage.py add_dummy_data
python manage.py createsuperuser
python manage.py collectstatic

# Production
python manage.py check --deploy
gunicorn cms_project.wsgi:application
```

---

**🎉 Congratulations!** You have successfully set up NITRA-CMS.

**Next Steps:**
1. Explore the system with different user roles
2. Customize settings for your institution
3. Add real data and users
4. Consider production deployment

For detailed feature information and advanced configuration, refer to the main **README.md** file.
