from django import forms
from .models import HODProfile
from academic.models import Branch, Section


class HODProfileForm(forms.ModelForm):
    class Meta:
        model = HODProfile
        fields = []

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class HODUserForm(forms.Form):

    first_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter first name'
        }),
        required=True
    )

    last_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter last name'
        }),
        required=False
    )

    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter email address'
        }),
        required=True
    )

    photo = forms.ImageField(
        widget=forms.FileInput(attrs={
            'class': 'form-control-file',
            'accept': 'image/*'
        }),
        required=False,
        help_text="Upload a profile photo (optional)"
    )

    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            self.fields['first_name'].initial = user.first_name
            self.fields['last_name'].initial = user.last_name
            self.fields['email'].initial = user.email


class HODSectionForm(forms.ModelForm):
    """Custom Section form for HOD that restricts branch choices to HOD's assigned branches"""

    class Meta:
        model = Section
        fields = ['name', 'branch', 'year', 'semester']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter section name',
                'required': 'required'
            }),
            'branch': forms.Select(attrs={
                'class': 'form-control custom-select',
                'required': 'required'
            }),
            'year': forms.Select(attrs={
                'class': 'form-control custom-select',
                'required': 'required'
            }),
            'semester': forms.Select(attrs={
                'class': 'form-control custom-select',
                'required': 'required'
            }),
        }

    def __init__(self, hod_profile=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if hod_profile and hod_profile.branches.exists():
            # Restrict branch choices to HOD's assigned branches
            self.fields['branch'].queryset = hod_profile.branches.all()
        else:
            # If no HOD profile or no branches assigned, show no branches
            self.fields['branch'].queryset = Branch.objects.none()

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if not name:
            raise forms.ValidationError("Section name is required.")
        if len(name) > 50:
            raise forms.ValidationError("Section name cannot exceed 50 characters.")
        return name

    def clean(self):
        cleaned_data = super().clean()
        name = cleaned_data.get('name')
        branch = cleaned_data.get('branch')
        year = cleaned_data.get('year')
        semester = cleaned_data.get('semester')

        if name and branch and year and semester:
            # Check unique_together constraint
            existing = Section.objects.filter(
                name=name,
                branch=branch,
                year=year,
                semester=semester
            ).exclude(id=self.instance.id if self.instance else None)
            if existing.exists():
                raise forms.ValidationError(
                    "A section with this name, branch, year, and semester already exists."
                )
        return cleaned_data
